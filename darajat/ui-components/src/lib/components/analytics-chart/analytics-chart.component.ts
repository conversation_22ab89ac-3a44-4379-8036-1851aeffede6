import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartModule } from 'primeng/chart';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
  }[];
}

export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: any;
  scales?: any;
}

@Component({
  selector: 'darajat-analytics-chart',
  standalone: true,
  imports: [
    CommonModule,
    ChartModule,
    CardModule,
    ButtonModule,
    DropdownModule,
  ],
  template: `
    <p-card class="analytics-chart-card">
      <ng-template pTemplate="header">
        <div class="chart-header">
          <div class="chart-title-section">
            <h3 class="chart-title">{{ title }}</h3>
            <p class="chart-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
          </div>
          <div class="chart-actions" *ngIf="showActions">
            <p-dropdown
              *ngIf="timeRangeOptions.length > 0"
              [options]="timeRangeOptions"
              [(ngModel)]="selectedTimeRange"
              (onChange)="onTimeRangeChange($event)"
              placeholder="Select period"
              class="time-range-dropdown"
            ></p-dropdown>
            <button
              pButton
              type="button"
              icon="pi pi-refresh"
              class="p-button-text p-button-sm"
              (click)="onRefresh()"
              [loading]="isLoading"
              pTooltip="Refresh data"
            ></button>
            <button
              pButton
              type="button"
              icon="pi pi-download"
              class="p-button-text p-button-sm"
              (click)="onExport()"
              pTooltip="Export chart"
            ></button>
          </div>
        </div>
      </ng-template>

      <ng-template pTemplate="content">
        <div class="chart-container" [class.loading]="isLoading">
          <div *ngIf="isLoading" class="loading-overlay">
            <i class="pi pi-spin pi-spinner loading-icon"></i>
            <span>Loading chart data...</span>
          </div>
          
          <p-chart
            *ngIf="!isLoading && chartData"
            [type]="chartType"
            [data]="chartData"
            [options]="chartOptions"
            [width]="width"
            [height]="height"
            class="analytics-chart"
          ></p-chart>

          <div *ngIf="!isLoading && !chartData" class="no-data">
            <i class="pi pi-chart-bar no-data-icon"></i>
            <p>No data available</p>
          </div>
        </div>
      </ng-template>

      <ng-template pTemplate="footer" *ngIf="showFooter">
        <div class="chart-footer">
          <div class="chart-stats" *ngIf="stats">
            <div class="stat-item" *ngFor="let stat of stats">
              <span class="stat-label">{{ stat.label }}:</span>
              <span class="stat-value" [ngClass]="stat.class">{{ stat.value }}</span>
            </div>
          </div>
          <div class="chart-legend" *ngIf="showLegend && chartData?.datasets">
            <div class="legend-item" *ngFor="let dataset of chartData.datasets">
              <span 
                class="legend-color" 
                [style.background-color]="dataset.backgroundColor"
              ></span>
              <span class="legend-label">{{ dataset.label }}</span>
            </div>
          </div>
        </div>
      </ng-template>
    </p-card>
  `,
  styleUrls: ['./analytics-chart.component.scss']
})
export class AnalyticsChartComponent implements OnInit, OnDestroy {
  @Input() title = 'Analytics Chart';
  @Input() subtitle = '';
  @Input() chartType: 'bar' | 'line' | 'pie' | 'doughnut' | 'radar' | 'polarArea' = 'bar';
  @Input() chartData: ChartData | null = null;
  @Input() chartOptions: ChartOptions = {};
  @Input() width = '100%';
  @Input() height = '400px';
  @Input() isLoading = false;
  @Input() showActions = true;
  @Input() showFooter = true;
  @Input() showLegend = true;
  @Input() timeRangeOptions: any[] = [
    { label: 'Last 7 days', value: '7d' },
    { label: 'Last 30 days', value: '30d' },
    { label: 'Last 3 months', value: '3m' },
    { label: 'Last year', value: '1y' }
  ];
  @Input() stats: { label: string; value: string; class?: string }[] = [];

  selectedTimeRange = '30d';

  ngOnInit(): void {
    this.setupDefaultOptions();
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private setupDefaultOptions(): void {
    const defaultOptions: ChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false, // We'll use custom legend
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#007ACC',
          borderWidth: 1,
        }
      },
      scales: {
        x: {
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
          },
          ticks: {
            color: '#757575',
          }
        },
        y: {
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
          },
          ticks: {
            color: '#757575',
          }
        }
      }
    };

    // Merge with provided options
    this.chartOptions = { ...defaultOptions, ...this.chartOptions };
  }

  onTimeRangeChange(event: any): void {
    this.selectedTimeRange = event.value;
    // Emit event or call service to update data
    console.log('Time range changed:', this.selectedTimeRange);
  }

  onRefresh(): void {
    this.isLoading = true;
    // Simulate data refresh
    setTimeout(() => {
      this.isLoading = false;
      console.log('Chart data refreshed');
    }, 1000);
  }

  onExport(): void {
    // Implement chart export functionality
    console.log('Exporting chart...');
  }
}
