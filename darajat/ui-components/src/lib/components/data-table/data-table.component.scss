.data-table-container {
  background: #FFFFFF;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .table-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #E0E0E0;
    background: #FAFAFA;

    .table-title-section {
      .table-title {
        margin: 0 0 0.25rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #212121;
      }

      .table-subtitle {
        margin: 0;
        font-size: 0.875rem;
        color: #757575;
      }
    }

    .table-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      .search-container {
        .global-search {
          min-width: 250px;
        }
      }
    }
  }
}

// Table styling
:host ::ng-deep {
  .p-datatable {
    .p-datatable-header {
      background: #FAFAFA;
      border: none;
      padding: 1rem 1.5rem;
      color: #212121;
    }

    .p-datatable-thead > tr > th {
      background: #F5F5F5;
      border: none;
      border-bottom: 2px solid #E0E0E0;
      color: #212121;
      font-weight: 600;
      padding: 1rem;
      font-size: 0.875rem;

      &:first-child {
        border-top-left-radius: 0;
      }

      &:last-child {
        border-top-right-radius: 0;
      }

      .p-sortable-column-icon {
        color: #757575;
      }

      &.p-highlight {
        background: #E3F2FD;
        color: #007ACC;

        .p-sortable-column-icon {
          color: #007ACC;
        }
      }
    }

    .p-datatable-tbody > tr {
      background: #FFFFFF;
      border: none;
      border-bottom: 1px solid #F0F0F0;
      transition: background-color 0.2s ease-in-out;

      &:hover {
        background: #F8F9FA;
      }

      &.selected-row {
        background: #E3F2FD;
      }

      > td {
        border: none;
        padding: 1rem;
        color: #212121;
        font-size: 0.875rem;

        &.actions-cell {
          text-align: center;
          padding: 0.5rem;
        }

        .number-cell {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-weight: 500;
        }

        .currency-cell {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-weight: 600;
          color: #4CAF50;
        }

        .date-cell {
          color: #757575;
        }
      }
    }

    .p-datatable-footer {
      background: #FAFAFA;
      border: none;
      border-top: 1px solid #E0E0E0;
      padding: 1rem 1.5rem;
    }

    // Pagination styling
    .p-paginator {
      background: transparent;
      border: none;
      padding: 1rem 1.5rem;

      .p-paginator-pages {
        .p-paginator-page {
          background: transparent;
          border: 1px solid #E0E0E0;
          color: #757575;
          margin: 0 0.125rem;

          &:hover {
            background: #F5F5F5;
            border-color: #007ACC;
            color: #007ACC;
          }

          &.p-highlight {
            background: #007ACC;
            border-color: #007ACC;
            color: #FFFFFF;
          }
        }
      }

      .p-paginator-prev,
      .p-paginator-next,
      .p-paginator-first,
      .p-paginator-last {
        background: transparent;
        border: 1px solid #E0E0E0;
        color: #757575;

        &:hover {
          background: #F5F5F5;
          border-color: #007ACC;
          color: #007ACC;
        }

        &.p-disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }

    // Loading state
    .p-datatable-loading-overlay {
      background: rgba(255, 255, 255, 0.8);
    }

    // Empty state
    .empty-message {
      text-align: center;
      padding: 3rem 1rem;

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #757575;

        .empty-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }

        p {
          margin: 0;
          font-size: 1rem;
        }
      }
    }

    // Loading state
    .loading-message {
      text-align: center;
      padding: 3rem 1rem;

      .loading-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #757575;

        .loading-icon {
          font-size: 2rem;
          margin-bottom: 1rem;
        }

        p {
          margin: 0;
          font-size: 1rem;
        }
      }
    }
  }

  // Checkbox and radio button styling
  .p-checkbox,
  .p-radiobutton {
    .p-checkbox-box,
    .p-radiobutton-box {
      border-color: #E0E0E0;

      &.p-highlight {
        background: #007ACC;
        border-color: #007ACC;
      }
    }
  }

  // Tag styling for status
  .p-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;

    &.p-tag-success {
      background: #E8F5E8;
      color: #2E7D32;
    }

    &.p-tag-info {
      background: #E3F2FD;
      color: #1976D2;
    }

    &.p-tag-warning {
      background: #FFF3E0;
      color: #F57C00;
    }

    &.p-tag-danger {
      background: #FFEBEE;
      color: #D32F2F;
    }

    &.p-tag-secondary {
      background: #F5F5F5;
      color: #757575;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .data-table-container {
    .table-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .table-actions {
        justify-content: space-between;

        .search-container .global-search {
          min-width: 200px;
        }
      }
    }
  }

  :host ::ng-deep {
    .p-datatable {
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
      }
    }
  }
}

@media (max-width: 576px) {
  :host ::ng-deep {
    .p-datatable {
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
      }
    }
  }
}

// Animation
.data-table-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
