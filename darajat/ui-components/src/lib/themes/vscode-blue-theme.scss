// VSCode Blue Theme for PrimeNG
// Primary colors based on VSCode theme
:root {
  --primary-blue: #007ACC;
  --secondary-blue: #005A9E;
  --light-blue: #E3F2FD;
  --white: #FFFFFF;
  --dark-blue: #003d6b;
  --accent-blue: #0099ff;
  --light-gray: #F5F5F5;
  --medium-gray: #E0E0E0;
  --dark-gray: #424242;
  --text-primary: #212121;
  --text-secondary: #757575;
  --success: #4CAF50;
  --warning: #FF9800;
  --error: #F44336;
  --info: #2196F3;
}

// Override PrimeNG CSS variables
:root {
  --primary-color: var(--primary-blue);
  --primary-color-text: var(--white);
  --surface-0: var(--white);
  --surface-50: #FAFAFA;
  --surface-100: #F5F5F5;
  --surface-200: #EEEEEE;
  --surface-300: #E0E0E0;
  --surface-400: #BDBDBD;
  --surface-500: #9E9E9E;
  --surface-600: #757575;
  --surface-700: #616161;
  --surface-800: #424242;
  --surface-900: #212121;
  --gray-50: #FAFAFA;
  --gray-100: #F5F5F5;
  --gray-200: #EEEEEE;
  --gray-300: #E0E0E0;
  --gray-400: #BDBDBD;
  --gray-500: #9E9E9E;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  --content-padding: 1rem;
  --inline-spacing: 0.5rem;
  --border-radius: 6px;
  --surface-ground: var(--gray-50);
  --surface-section: var(--white);
  --surface-card: var(--white);
  --surface-overlay: var(--white);
  --surface-border: var(--gray-300);
  --surface-hover: var(--gray-50);
  --focus-ring: 0 0 0 0.2rem var(--light-blue);
  --maskbg: rgba(0, 0, 0, 0.4);
  --highlight-bg: var(--light-blue);
  --highlight-text-color: var(--primary-blue);
  --text-color: var(--text-primary);
  --text-color-secondary: var(--text-secondary);
  --primary-color-text: var(--white);
}

// Custom button styles
.p-button {
  background: var(--primary-blue);
  border: 1px solid var(--primary-blue);
  color: var(--white);
  
  &:hover {
    background: var(--secondary-blue);
    border-color: var(--secondary-blue);
  }
  
  &:focus {
    box-shadow: var(--focus-ring);
  }
  
  &.p-button-secondary {
    background: var(--white);
    border: 1px solid var(--primary-blue);
    color: var(--primary-blue);
    
    &:hover {
      background: var(--light-blue);
    }
  }
}

// Custom card styles
.p-card {
  background: var(--white);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .p-card-header {
    background: var(--surface-50);
    border-bottom: 1px solid var(--surface-border);
  }
}

// Custom table styles
.p-datatable {
  .p-datatable-header {
    background: var(--surface-50);
    border: 1px solid var(--surface-border);
    color: var(--text-primary);
  }
  
  .p-datatable-thead > tr > th {
    background: var(--surface-100);
    border: 1px solid var(--surface-border);
    color: var(--text-primary);
    font-weight: 600;
  }
  
  .p-datatable-tbody > tr {
    background: var(--white);
    
    &:nth-child(even) {
      background: var(--surface-50);
    }
    
    &:hover {
      background: var(--light-blue);
    }
  }
  
  .p-datatable-tbody > tr > td {
    border: 1px solid var(--surface-border);
    color: var(--text-primary);
  }
}

// Custom sidebar styles
.p-sidebar {
  background: var(--dark-blue);
  color: var(--white);
  
  .p-sidebar-header {
    background: var(--dark-blue);
    border-bottom: 1px solid var(--secondary-blue);
  }
}

// Custom menu styles
.p-menu {
  background: var(--white);
  border: 1px solid var(--surface-border);
  
  .p-menuitem-link {
    color: var(--text-primary);
    
    &:hover {
      background: var(--light-blue);
      color: var(--primary-blue);
    }
    
    &:focus {
      background: var(--light-blue);
      color: var(--primary-blue);
    }
  }
}

// Custom input styles
.p-inputtext {
  border: 1px solid var(--surface-border);
  background: var(--white);
  color: var(--text-primary);
  
  &:focus {
    border-color: var(--primary-blue);
    box-shadow: var(--focus-ring);
  }
}

// Custom dropdown styles
.p-dropdown {
  background: var(--white);
  border: 1px solid var(--surface-border);
  
  &:focus {
    border-color: var(--primary-blue);
    box-shadow: var(--focus-ring);
  }
  
  .p-dropdown-label {
    color: var(--text-primary);
  }
}

// Custom chart styles
.p-chart {
  .p-chart-title {
    color: var(--text-primary);
    font-weight: 600;
  }
}

// Custom badge styles
.p-badge {
  background: var(--primary-blue);
  color: var(--white);
  
  &.p-badge-success {
    background: var(--success);
  }
  
  &.p-badge-warning {
    background: var(--warning);
  }
  
  &.p-badge-danger {
    background: var(--error);
  }
  
  &.p-badge-info {
    background: var(--info);
  }
}

// Custom progress bar styles
.p-progressbar {
  background: var(--surface-200);
  border-radius: var(--border-radius);
  
  .p-progressbar-value {
    background: var(--primary-blue);
  }
}

// Custom tab view styles
.p-tabview {
  .p-tabview-nav {
    background: var(--surface-50);
    border-bottom: 1px solid var(--surface-border);
    
    .p-tabview-nav-link {
      background: transparent;
      border: 1px solid transparent;
      color: var(--text-secondary);
      
      &:hover {
        background: var(--light-blue);
        color: var(--primary-blue);
      }
    }
    
    .p-highlight .p-tabview-nav-link {
      background: var(--white);
      border-color: var(--surface-border);
      border-bottom-color: var(--white);
      color: var(--primary-blue);
    }
  }
  
  .p-tabview-panels {
    background: var(--white);
  }
}
