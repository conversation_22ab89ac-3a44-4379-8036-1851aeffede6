.dashboard-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #E0E0E0;

    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: #212121;
      margin: 0 0 0.5rem 0;
    }

    .page-subtitle {
      font-size: 1rem;
      color: #757575;
      margin: 0;
    }
  }

  .time-period-section {
    margin-bottom: 2rem;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;

    .chart-container {
      min-height: 400px;
    }
  }

  .table-section {
    margin-bottom: 2rem;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .dashboard-container {
    .charts-section {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;

    .page-header {
      .page-title {
        font-size: 1.5rem;
      }

      .page-subtitle {
        font-size: 0.875rem;
      }
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .charts-section {
      gap: 1rem;
    }
  }
}

@media (max-width: 576px) {
  .dashboard-container {
    padding: 0.75rem;

    .page-header {
      margin-bottom: 1.5rem;

      .page-title {
        font-size: 1.25rem;
      }
    }

    .time-period-section {
      margin-bottom: 1.5rem;
    }

    .stats-grid {
      margin-bottom: 1.5rem;
    }

    .charts-section {
      margin-bottom: 1.5rem;

      .chart-container {
        min-height: 300px;
      }
    }
  }
}

// Animation
.dashboard-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading states
.stats-grid {
  .loading-card {
    background: #f0f0f0;
    border-radius: 6px;
    height: 120px;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

.charts-section {
  .loading-chart {
    background: #f0f0f0;
    border-radius: 6px;
    height: 400px;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
