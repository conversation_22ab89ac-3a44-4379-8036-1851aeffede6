import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export interface ThemeConfig {
    name: string;
    displayName: string;
    primaryColor: string;
    secondaryColor: string;
    isDark: boolean;
    cssClass: string;
}
export declare const AVAILABLE_THEMES: ThemeConfig[];
export declare class ThemeService {
    private readonly THEME_STORAGE_KEY;
    private readonly DEFAULT_THEME;
    private currentThemeSubject;
    currentTheme$: Observable<ThemeConfig>;
    constructor();
    /**
     * Initialize theme from localStorage or use default
     */
    private initializeTheme;
    /**
     * Get all available themes
     */
    getAvailableThemes(): ThemeConfig[];
    /**
     * Get current theme
     */
    getCurrentTheme(): ThemeConfig;
    /**
     * Set theme by name
     */
    setTheme(themeName: string, saveToStorage?: boolean): void;
    /**
     * Toggle between light and dark variants of current theme
     */
    toggleDarkMode(): void;
    /**
     * Check if current theme is dark
     */
    isDarkMode(): boolean;
    /**
     * Apply theme to document
     */
    private applyTheme;
    /**
     * Update CSS custom properties for the theme
     */
    private updateCSSVariables;
    /**
     * Update meta theme-color for mobile browsers
     */
    private updateMetaThemeColor;
    /**
     * Get theme by name
     */
    private getThemeByName;
    /**
     * Save theme to localStorage
     */
    private saveTheme;
    /**
     * Get saved theme from localStorage
     */
    private getSavedTheme;
    /**
     * Reset theme to default
     */
    resetTheme(): void;
    /**
     * Get theme colors for charts and other components
     */
    getThemeColors(): {
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        error: string;
        info: string;
    };
    /**
     * Generate chart color palette based on current theme
     */
    getChartColorPalette(): string[];
    static ɵfac: i0.ɵɵFactoryDeclaration<ThemeService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ThemeService>;
}
