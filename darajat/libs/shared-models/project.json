{"name": "shared-models", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-models/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/shared-models/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/shared-models/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/shared-models/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared-models/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}