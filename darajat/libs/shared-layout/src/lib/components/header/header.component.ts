import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { BadgeModule } from 'primeng/badge';
import { AvatarModule } from 'primeng/avatar';
import { MenuModule } from 'primeng/menu';
import { PopoverModule } from 'primeng/popover';
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'darajat-header',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    InputTextModule,
    InputIconModule,
    IconFieldModule,
    BadgeModule,
    AvatarModule,
    MenuModule,
    PopoverModule,
  ],
  template: `
    <header class="header">
      <div class="header-left">
        <button
          pButton
          type="button"
          icon="pi pi-bars"
          class="p-button-text header-menu-toggle"
          (click)="onMenuToggle()"
          [attr.aria-label]="'Toggle menu'"
        ></button>
        
        <div class="header-brand">
          <img 
            src="assets/images/logo.png" 
            alt="Darajat Logo" 
            class="header-logo"
            onerror="this.style.display='none'"
          />
          <span class="header-title">{{ title }}</span>
        </div>
      </div>

      <div class="header-center">
        <h1 class="page-title">{{ pageTitle }}</h1>
      </div>

      <div class="header-right">
        <!-- Search -->
        <div class="header-search">
          <p-iconField iconPosition="left">
            <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
            <input
              pInputText
              type="text"
              placeholder="Search..."
              [(ngModel)]="searchQuery"
              (input)="onSearch($event)"
              class="search-input"
            />
          </p-iconField>
        </div>

        <!-- Notifications -->
        <button
          pButton
          type="button"
          icon="pi pi-bell"
          class="p-button-text header-notification"
          (click)="onNotificationClick()"
          [attr.aria-label]="'Notifications'"
        >
          <p-badge 
            *ngIf="notificationCount > 0"
            [value]="notificationCount.toString()"
            severity="danger"
            class="notification-badge"
          ></p-badge>
        </button>

        <!-- User Profile -->
        <div class="header-profile">
          <button
            pButton
            type="button"
            class="p-button-text profile-button"
            (click)="profileMenu.toggle($event)"
            [attr.aria-label]="'User menu'"
          >
            <p-avatar
              [image]="userAvatar"
              [label]="userInitials"
              shape="circle"
              size="normal"
              class="user-avatar"
            ></p-avatar>
            <span class="user-name">{{ userName }}</span>
            <i class="pi pi-chevron-down"></i>
          </button>
          
          <p-popover #profileMenu>
            <p-menu [model]="profileMenuItems" [popup]="false"></p-menu>
          </p-popover>
        </div>
      </div>
    </header>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() title = 'Darajat';
  @Input() pageTitle = 'Dashboard';
  @Input() userName = 'John Doe';
  @Input() userAvatar = '';
  @Input() userInitials = 'JD';
  @Input() notificationCount = 0;
  @Input() searchQuery = '';

  @Output() menuToggle = new EventEmitter<void>();
  @Output() search = new EventEmitter<string>();
  @Output() notificationClick = new EventEmitter<void>();
  @Output() profileAction = new EventEmitter<string>();

  profileMenuItems: MenuItem[] = [
    {
      label: 'Profile',
      icon: 'pi pi-user',
      command: () => this.onProfileAction('profile')
    },
    {
      label: 'Settings',
      icon: 'pi pi-cog',
      command: () => this.onProfileAction('settings')
    },
    {
      separator: true
    },
    {
      label: 'Logout',
      icon: 'pi pi-sign-out',
      command: () => this.onProfileAction('logout')
    }
  ];

  onMenuToggle(): void {
    this.menuToggle.emit();
  }

  onSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.search.emit(target.value);
  }

  onNotificationClick(): void {
    this.notificationClick.emit();
  }

  onProfileAction(action: string): void {
    this.profileAction.emit(action);
  }
}
