@import '../../styles/variables.scss';

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  background: var(--white);
  border-bottom: 1px solid var(--medium-gray);
  padding: 0 var(--spacing-lg);
  box-shadow: var(--box-shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    .header-menu-toggle {
      color: var(--text-primary);
      
      &:hover {
        background: var(--light-gray);
      }
    }

    .header-brand {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-logo {
        height: 32px;
        width: auto;
      }

      .header-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-blue);
        margin: 0;
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .page-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin: 0;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    .header-search {
      .search-input {
        width: 250px;
        border-radius: var(--border-radius);
        
        &::placeholder {
          color: var(--text-secondary);
        }
      }
    }

    .header-notification {
      position: relative;
      color: var(--text-primary);
      
      &:hover {
        background: var(--light-gray);
      }

      .notification-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        min-width: 18px;
        height: 18px;
        font-size: var(--font-size-xs);
      }
    }

    .header-profile {
      .profile-button {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-primary);
        padding: var(--spacing-sm);
        border-radius: var(--border-radius);
        
        &:hover {
          background: var(--light-gray);
        }

        .user-avatar {
          width: 32px;
          height: 32px;
        }

        .user-name {
          font-weight: var(--font-weight-medium);
          max-width: 120px;
          @include text-truncate;
        }

        .pi-chevron-down {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: $breakpoint-md) {
  .header {
    padding: 0 var(--spacing-md);

    .header-center {
      .page-title {
        font-size: var(--font-size-base);
      }
    }

    .header-right {
      .header-search {
        .search-input {
          width: 200px;
        }
      }

      .user-name {
        display: none;
      }
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .header {
    padding: 0 var(--spacing-sm);

    .header-brand {
      .header-title {
        display: none;
      }
    }

    .header-center {
      .page-title {
        font-size: var(--font-size-sm);
      }
    }

    .header-right {
      .header-search {
        display: none;
      }
    }
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .header {
    background: var(--dark-gray);
    border-bottom-color: var(--medium-gray);
    color: var(--white);

    .header-left {
      .header-menu-toggle {
        color: var(--white);
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }

      .header-brand {
        .header-title {
          color: var(--accent-blue);
        }
      }
    }

    .header-center {
      .page-title {
        color: var(--white);
      }
    }

    .header-right {
      .header-notification {
        color: var(--white);
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }

      .header-profile {
        .profile-button {
          color: var(--white);
          
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
  }
}
