import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ThemeConfig {
  name: string;
  displayName: string;
  primaryColor: string;
  secondaryColor: string;
  isDark: boolean;
  cssClass: string;
}

export const AVAILABLE_THEMES: ThemeConfig[] = [
  {
    name: 'vscode-blue',
    displayName: 'VSCode Blue',
    primaryColor: '#007ACC',
    secondaryColor: '#005A9E',
    isDark: false,
    cssClass: 'theme-vscode-blue'
  },
  {
    name: 'vscode-blue-dark',
    displayName: 'VSCode Blue Dark',
    primaryColor: '#0099FF',
    secondaryColor: '#007ACC',
    isDark: true,
    cssClass: 'theme-vscode-blue-dark'
  },
  {
    name: 'material-blue',
    displayName: 'Material Blue',
    primaryColor: '#2196F3',
    secondaryColor: '#1976D2',
    isDark: false,
    cssClass: 'theme-material-blue'
  },
  {
    name: 'material-blue-dark',
    displayName: 'Material Blue Dark',
    primaryColor: '#42A5F5',
    secondaryColor: '#2196F3',
    isDark: true,
    cssClass: 'theme-material-blue-dark'
  }
];

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_STORAGE_KEY = 'darajat-theme';
  private readonly DEFAULT_THEME = 'vscode-blue';
  
  private currentThemeSubject = new BehaviorSubject<ThemeConfig>(
    this.getThemeByName(this.DEFAULT_THEME)
  );
  
  public currentTheme$: Observable<ThemeConfig> = this.currentThemeSubject.asObservable();

  constructor() {
    this.initializeTheme();
  }

  /**
   * Initialize theme from localStorage or use default
   */
  private initializeTheme(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      // SSR environment - use default theme
      this.setTheme(this.DEFAULT_THEME, false);
      return;
    }

    const savedTheme = this.getSavedTheme();
    if (savedTheme) {
      this.setTheme(savedTheme, false);
    } else {
      // Check for system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const defaultTheme = prefersDark ? 'vscode-blue-dark' : 'vscode-blue';
      this.setTheme(defaultTheme);
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!this.getSavedTheme()) {
        const theme = e.matches ? 'vscode-blue-dark' : 'vscode-blue';
        this.setTheme(theme, false);
      }
    });
  }

  /**
   * Get all available themes
   */
  getAvailableThemes(): ThemeConfig[] {
    return AVAILABLE_THEMES;
  }

  /**
   * Get current theme
   */
  getCurrentTheme(): ThemeConfig {
    return this.currentThemeSubject.value;
  }

  /**
   * Set theme by name
   */
  setTheme(themeName: string, saveToStorage: boolean = true): void {
    const theme = this.getThemeByName(themeName);
    this.applyTheme(theme);
    this.currentThemeSubject.next(theme);
    
    if (saveToStorage) {
      this.saveTheme(themeName);
    }
  }

  /**
   * Toggle between light and dark variants of current theme
   */
  toggleDarkMode(): void {
    const currentTheme = this.getCurrentTheme();
    let newThemeName: string;

    if (currentTheme.isDark) {
      // Switch to light variant
      newThemeName = currentTheme.name.replace('-dark', '');
    } else {
      // Switch to dark variant
      newThemeName = currentTheme.name + '-dark';
    }

    // Check if the target theme exists
    const targetTheme = AVAILABLE_THEMES.find(t => t.name === newThemeName);
    if (targetTheme) {
      this.setTheme(newThemeName);
    }
  }

  /**
   * Check if current theme is dark
   */
  isDarkMode(): boolean {
    return this.getCurrentTheme().isDark;
  }

  /**
   * Apply theme to document
   */
  private applyTheme(theme: ThemeConfig): void {
    const body = document.body;
    
    // Remove all theme classes
    AVAILABLE_THEMES.forEach(t => {
      body.classList.remove(t.cssClass);
    });
    
    // Add current theme class
    body.classList.add(theme.cssClass);
    
    // Update CSS custom properties
    this.updateCSSVariables(theme);
    
    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme.primaryColor);
  }

  /**
   * Update CSS custom properties for the theme
   */
  private updateCSSVariables(theme: ThemeConfig): void {
    const root = document.documentElement;
    
    root.style.setProperty('--theme-primary', theme.primaryColor);
    root.style.setProperty('--theme-secondary', theme.secondaryColor);
    
    if (theme.isDark) {
      root.style.setProperty('--theme-background', '#1a1a1a');
      root.style.setProperty('--theme-surface', '#2d2d2d');
      root.style.setProperty('--theme-text-primary', '#ffffff');
      root.style.setProperty('--theme-text-secondary', '#b3b3b3');
      root.style.setProperty('--theme-border', '#404040');
    } else {
      root.style.setProperty('--theme-background', '#ffffff');
      root.style.setProperty('--theme-surface', '#f5f5f5');
      root.style.setProperty('--theme-text-primary', '#212121');
      root.style.setProperty('--theme-text-secondary', '#757575');
      root.style.setProperty('--theme-border', '#e0e0e0');
    }
  }

  /**
   * Update meta theme-color for mobile browsers
   */
  private updateMetaThemeColor(color: string): void {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.setAttribute('name', 'theme-color');
      document.head.appendChild(metaThemeColor);
    }
    
    metaThemeColor.setAttribute('content', color);
  }

  /**
   * Get theme by name
   */
  private getThemeByName(name: string): ThemeConfig {
    return AVAILABLE_THEMES.find(theme => theme.name === name) || AVAILABLE_THEMES[0];
  }

  /**
   * Save theme to localStorage
   */
  private saveTheme(themeName: string): void {
    try {
      // Check if localStorage is available (browser environment)
      if (typeof localStorage === 'undefined') {
        return;
      }
      localStorage.setItem(this.THEME_STORAGE_KEY, themeName);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }

  /**
   * Get saved theme from localStorage
   */
  private getSavedTheme(): string | null {
    try {
      // Check if localStorage is available (browser environment)
      if (typeof localStorage === 'undefined') {
        return null;
      }
      return localStorage.getItem(this.THEME_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to get theme from localStorage:', error);
      return null;
    }
  }

  /**
   * Reset theme to default
   */
  resetTheme(): void {
    this.setTheme(this.DEFAULT_THEME);
  }

  /**
   * Get theme colors for charts and other components
   */
  getThemeColors(): { primary: string; secondary: string; success: string; warning: string; error: string; info: string } {
    const theme = this.getCurrentTheme();
    
    return {
      primary: theme.primaryColor,
      secondary: theme.secondaryColor,
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3'
    };
  }

  /**
   * Generate chart color palette based on current theme
   */
  getChartColorPalette(): string[] {
    const colors = this.getThemeColors();
    
    return [
      colors.primary,
      colors.secondary,
      colors.success,
      colors.info,
      colors.warning,
      colors.error,
      '#9C27B0', // Purple
      '#FF5722', // Deep Orange
      '#607D8B', // Blue Grey
      '#795548'  // Brown
    ];
  }
}
