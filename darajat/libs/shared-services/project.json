{"name": "shared-services", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-services/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/shared-services/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/shared-services/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/shared-services/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared-services/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}