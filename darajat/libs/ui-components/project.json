{"name": "ui-components", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui-components/src", "prefix": "lib", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/ui-components/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/ui-components/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/ui-components/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ui-components/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}