import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';

export interface TimePeriod {
  label: string;
  value: string;
  startDate: Date;
  endDate: Date;
}

export interface TimePeriodOption {
  label: string;
  value: string;
  days: number;
}

@Component({
  selector: 'darajat-time-period-selector',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
  ],
  template: `
    <p-card class="time-period-selector">
      <ng-template pTemplate="content">
        <div class="period-selector-content">
          <!-- Quick Periods -->
          <div class="quick-periods">
            <h4>Quick Periods</h4>
            <div class="period-buttons">
              <button
                *ngFor="let period of quickPeriods"
                pButton
                type="button"
                [label]="period.label"
                [class]="'p-button-outlined ' + (selectedPeriod?.value === period.value ? 'p-button-primary' : '')"
                (click)="selectQuickPeriod(period)"
              ></button>
            </div>
          </div>

          <!-- Custom Range -->
          <div class="custom-range" *ngIf="showCustomRange">
            <h4>Custom Range</h4>
            <div class="date-inputs">
              <div class="date-field">
                <label for="startDate">Start Date</label>
                <input
                  type="date"
                  id="startDate"
                  [(ngModel)]="customStartDateString"
                  (change)="onCustomDateChange()"
                  class="date-input"
                />
              </div>

              <div class="date-field">
                <label for="endDate">End Date</label>
                <input
                  type="date"
                  id="endDate"
                  [(ngModel)]="customEndDateString"
                  (change)="onCustomDateChange()"
                  class="date-input"
                />
              </div>
            </div>
          </div>

        <!-- Comparison Tab -->
        <p-tabPanel header="Compare" *ngIf="showComparison">
          <div class="comparison-options">
            <div class="comparison-info">
              <p>Compare current period with:</p>
            </div>
            
            <div class="comparison-buttons">
              <button
                pButton
                type="button"
                label="Previous Period"
                [class]="getComparisonButtonClass('previous')"
                (click)="selectComparison('previous')"
              ></button>
              <button
                pButton
                type="button"
                label="Same Period Last Year"
                [class]="getComparisonButtonClass('year')"
                (click)="selectComparison('year')"
              ></button>
              <button
                pButton
                type="button"
                label="Custom Period"
                [class]="getComparisonButtonClass('custom')"
                (click)="selectComparison('custom')"
              ></button>
            </div>

            <div class="comparison-custom" *ngIf="comparisonType === 'custom'">
              <div class="date-inputs">
                <div class="date-input-group">
                  <label for="compStartDate">Compare Start Date</label>
                  <p-datePicker
                    id="compStartDate"
                    [(ngModel)]="comparisonStartDate"
                    [maxDate]="comparisonEndDate || maxDate"
                    [showIcon]="true"
                    dateFormat="mm/dd/yy"
                    placeholder="Select start date"
                  ></p-datePicker>
                </div>
                
                <div class="date-input-group">
                  <label for="compEndDate">Compare End Date</label>
                  <p-datePicker
                    id="compEndDate"
                    [(ngModel)]="comparisonEndDate"
                    [minDate]="comparisonStartDate"
                    [maxDate]="maxDate"
                    [showIcon]="true"
                    dateFormat="mm/dd/yy"
                    placeholder="Select end date"
                  ></p-datePicker>
                </div>
              </div>
            </div>
          </div>
        </p-tabPanel>
      </p-tabs>

      <!-- Selected Period Display -->
      <div class="selected-period" *ngIf="selectedPeriod">
        <div class="period-info">
          <span class="period-label">{{ selectedPeriod.label }}</span>
          <span class="period-range">
            {{ formatDate(selectedPeriod.startDate) }} - {{ formatDate(selectedPeriod.endDate) }}
          </span>
        </div>
        <div class="period-actions">
          <button
            pButton
            type="button"
            icon="pi pi-refresh"
            class="p-button-text p-button-sm"
            (click)="onRefresh()"
            pTooltip="Refresh data"
          ></button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./time-period-selector.component.scss']
})
export class TimePeriodSelectorComponent implements OnInit {
  @Input() selectedPeriod: TimePeriod | null = null;
  @Input() showComparison = false;
  @Input() maxDate = new Date();
  @Input() quickOptions: TimePeriodOption[] = [
    { label: 'Today', value: 'today', days: 1 },
    { label: 'Yesterday', value: 'yesterday', days: 1 },
    { label: 'Last 7 days', value: '7d', days: 7 },
    { label: 'Last 30 days', value: '30d', days: 30 },
    { label: 'This month', value: 'month', days: 30 },
    { label: 'Last month', value: 'lastMonth', days: 30 }
  ];
  @Input() customOptions: TimePeriodOption[] = [
    { label: 'Last 90 days', value: '90d', days: 90 },
    { label: 'Last 6 months', value: '6m', days: 180 },
    { label: 'Last year', value: '1y', days: 365 },
    { label: 'Year to date', value: 'ytd', days: 365 }
  ];

  @Output() periodChange = new EventEmitter<TimePeriod>();
  @Output() comparisonChange = new EventEmitter<TimePeriod>();
  @Output() refresh = new EventEmitter<void>();

  activeTabIndex = 0;
  selectedQuickPeriod = '30d';
  selectedCustomOption: string | null = null;
  customStartDate: Date | null = null;
  customEndDate: Date | null = null;
  comparisonType: 'previous' | 'year' | 'custom' | null = null;
  comparisonStartDate: Date | null = null;
  comparisonEndDate: Date | null = null;

  ngOnInit(): void {
    if (!this.selectedPeriod) {
      this.selectQuickPeriod(this.quickOptions.find(opt => opt.value === '30d')!);
    }
  }

  onTabChange(event: any): void {
    this.activeTabIndex = event.index;
  }

  selectQuickPeriod(option: TimePeriodOption): void {
    this.selectedQuickPeriod = option.value;
    this.selectedCustomOption = null;
    
    const period = this.calculatePeriod(option);
    this.selectedPeriod = period;
    this.periodChange.emit(period);
  }

  onCustomOptionChange(event: any): void {
    if (event.value) {
      const option = this.customOptions.find(opt => opt.value === event.value);
      if (option) {
        this.selectQuickPeriod(option);
      }
    }
  }

  onCustomDateChange(): void {
    if (this.customStartDate && this.customEndDate) {
      this.applyCustomRange();
    }
  }

  applyCustomRange(): void {
    if (this.isCustomRangeValid()) {
      const period: TimePeriod = {
        label: 'Custom Range',
        value: 'custom',
        startDate: this.customStartDate!,
        endDate: this.customEndDate!
      };
      
      this.selectedPeriod = period;
      this.selectedQuickPeriod = '';
      this.selectedCustomOption = null;
      this.periodChange.emit(period);
    }
  }

  clearCustomRange(): void {
    this.customStartDate = null;
    this.customEndDate = null;
  }

  isCustomRangeValid(): boolean {
    return !!(this.customStartDate && this.customEndDate && 
             this.customStartDate <= this.customEndDate);
  }

  selectComparison(type: 'previous' | 'year' | 'custom'): void {
    this.comparisonType = type;
    
    if (this.selectedPeriod && type !== 'custom') {
      const comparisonPeriod = this.calculateComparisonPeriod(this.selectedPeriod, type);
      this.comparisonChange.emit(comparisonPeriod);
    }
  }

  private calculatePeriod(option: TimePeriodOption): TimePeriod {
    const endDate = new Date();
    let startDate = new Date();

    switch (option.value) {
      case 'today':
        startDate = new Date();
        break;
      case 'yesterday':
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 1);
        endDate.setDate(endDate.getDate() - 1);
        break;
      case 'month':
        startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'lastMonth':
        startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 1, 1);
        endDate.setDate(0); // Last day of previous month
        break;
      case 'ytd':
        startDate = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - option.days);
    }

    return {
      label: option.label,
      value: option.value,
      startDate,
      endDate
    };
  }

  private calculateComparisonPeriod(period: TimePeriod, type: 'previous' | 'year'): TimePeriod {
    const daysDiff = Math.ceil((period.endDate.getTime() - period.startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let startDate = new Date(period.startDate);
    let endDate = new Date(period.endDate);

    if (type === 'previous') {
      startDate.setDate(startDate.getDate() - daysDiff - 1);
      endDate.setDate(endDate.getDate() - daysDiff - 1);
    } else if (type === 'year') {
      startDate.setFullYear(startDate.getFullYear() - 1);
      endDate.setFullYear(endDate.getFullYear() - 1);
    }

    return {
      label: `${period.label} (${type === 'previous' ? 'Previous Period' : 'Last Year'})`,
      value: `${period.value}_${type}`,
      startDate,
      endDate
    };
  }

  getButtonClass(value: string): string {
    const baseClass = 'p-button-sm';
    return this.selectedQuickPeriod === value ? 
      `${baseClass} p-button-primary` : 
      `${baseClass} p-button-outlined`;
  }

  getComparisonButtonClass(type: string): string {
    const baseClass = 'p-button-sm';
    return this.comparisonType === type ? 
      `${baseClass} p-button-primary` : 
      `${baseClass} p-button-outlined`;
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }

  onRefresh(): void {
    this.refresh.emit();
  }
}
