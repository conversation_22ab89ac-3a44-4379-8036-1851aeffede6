import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';

export interface TimePeriodOption {
  label: string;
  value: string;
  days?: number;
  startDate?: Date;
  endDate?: Date;
}

export interface TimePeriod {
  label: string;
  value: string;
  startDate: Date;
  endDate: Date;
}

@Component({
  selector: 'lib-time-period-selector',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
  ],
  template: `
    <p-card class="time-period-selector">
      <ng-template pTemplate="content">
        <div class="period-selector-content">
          <!-- Quick Periods -->
          <div class="quick-periods">
            <h4>Quick Periods</h4>
            <div class="period-buttons">
              <button
                *ngFor="let period of quickPeriods"
                pButton
                type="button"
                [label]="period.label"
                class="p-button-outlined"
                (click)="selectQuickPeriod(period)"
              ></button>
            </div>
          </div>
        </div>
      </ng-template>
    </p-card>
  `,
  styleUrls: ['./time-period-selector.component.scss']
})
export class TimePeriodSelectorComponent implements OnInit {
  @Input() selectedPeriod: TimePeriod | null = null;
  @Input() showCustomRange = true;
  @Input() showActions = true;
  @Input() containerClass = '';

  @Output() periodChange = new EventEmitter<TimePeriod>();

  // Quick periods
  quickPeriods: TimePeriodOption[] = [
    { label: 'Today', value: 'today', days: 1 },
    { label: 'Yesterday', value: 'yesterday', days: 1 },
    { label: 'Last 7 days', value: '7d', days: 7 },
    { label: 'Last 30 days', value: '30d', days: 30 },
    { label: 'This month', value: 'month', days: 30 },
    { label: 'Last month', value: 'lastMonth', days: 30 }
  ];

  ngOnInit(): void {
    if (!this.selectedPeriod) {
      this.selectQuickPeriod(this.quickPeriods.find(opt => opt.value === '30d')!);
    }
  }

  selectQuickPeriod(option: TimePeriodOption): void {
    const endDate = new Date();
    const startDate = new Date();

    switch (option.value) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate.setDate(endDate.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case 'month':
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'lastMonth':
        startDate.setMonth(startDate.getMonth() - 1);
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        endDate.setDate(0);
        endDate.setHours(23, 59, 59, 999);
        break;
    }

    this.selectedPeriod = {
      label: option.label,
      value: option.value,
      startDate,
      endDate
    };

    this.periodChange.emit(this.selectedPeriod);
  }

  applySelection(): void {
    if (this.selectedPeriod) {
      this.periodChange.emit(this.selectedPeriod);
    }
  }

  resetSelection(): void {
    this.selectedPeriod = null;
    this.selectQuickPeriod(this.quickPeriods.find(opt => opt.value === '30d')!);
  }

  isValidSelection(): boolean {
    return this.selectedPeriod !== null;
  }
}