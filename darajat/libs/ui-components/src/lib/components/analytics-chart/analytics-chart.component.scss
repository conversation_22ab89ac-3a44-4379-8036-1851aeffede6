.analytics-chart-card {
  height: 100%;
  
  .chart-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E0E0E0;

    .chart-title-section {
      .chart-title {
        margin: 0 0 0.25rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #212121;
      }

      .chart-subtitle {
        margin: 0;
        font-size: 0.875rem;
        color: #757575;
      }
    }

    .chart-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .time-range-dropdown {
        min-width: 140px;
      }
    }
  }

  .chart-container {
    position: relative;
    padding: 1rem;
    min-height: 300px;

    &.loading {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .loading-overlay {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      color: #757575;

      .loading-icon {
        font-size: 2rem;
      }
    }

    .analytics-chart {
      width: 100%;
      height: 100%;
    }

    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: #757575;

      .no-data-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
      }

      p {
        margin: 0;
        font-size: 1rem;
      }
    }
  }

  .chart-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #E0E0E0;
    background: #FAFAFA;

    .chart-stats {
      display: flex;
      gap: 2rem;
      margin-bottom: 1rem;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .stat-label {
          font-size: 0.875rem;
          color: #757575;
        }

        .stat-value {
          font-size: 0.875rem;
          font-weight: 600;
          color: #212121;

          &.positive {
            color: #4CAF50;
          }

          &.negative {
            color: #F44336;
          }

          &.neutral {
            color: #FF9800;
          }
        }
      }
    }

    .chart-legend {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .legend-label {
          font-size: 0.875rem;
          color: #212121;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .analytics-chart-card {
    .chart-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .chart-actions {
        justify-content: space-between;
      }
    }

    .chart-footer {
      .chart-stats {
        flex-direction: column;
        gap: 0.5rem;
      }

      .chart-legend {
        gap: 0.5rem;
      }
    }
  }
}

// Chart type specific styles
:host ::ng-deep {
  .p-chart {
    .p-chart-canvas {
      max-height: 400px;
    }
  }

  // Bar chart specific
  &.bar-chart {
    .p-chart {
      canvas {
        // Bar chart specific styles
      }
    }
  }

  // Line chart specific
  &.line-chart {
    .p-chart {
      canvas {
        // Line chart specific styles
      }
    }
  }

  // Pie chart specific
  &.pie-chart {
    .p-chart {
      canvas {
        max-width: 300px;
        margin: 0 auto;
      }
    }
  }
}

// Animation
.chart-container {
  .analytics-chart {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading animation
.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
