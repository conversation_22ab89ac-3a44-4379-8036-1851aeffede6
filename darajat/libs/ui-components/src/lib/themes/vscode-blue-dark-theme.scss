// VSCode Blue Dark Theme for PrimeNG
// Dark theme variant with VSCode blue accents
.theme-vscode-blue-dark {
  // Dark theme color variables
  --primary-blue: #0099FF;
  --secondary-blue: #007ACC;
  --light-blue: #1E3A8A;
  --white: #1a1a1a;
  --dark-blue: #0066CC;
  --accent-blue: #00BFFF;
  --light-gray: #2d2d2d;
  --medium-gray: #404040;
  --dark-gray: #1a1a1a;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --success: #4ADE80;
  --warning: #FBBF24;
  --error: #F87171;
  --info: #60A5FA;

  // Override PrimeNG CSS variables for dark theme
  --primary-color: var(--primary-blue);
  --primary-color-text: var(--dark-gray);
  --surface-0: var(--dark-gray);
  --surface-50: #262626;
  --surface-100: #2d2d2d;
  --surface-200: #404040;
  --surface-300: #525252;
  --surface-400: #737373;
  --surface-500: #a3a3a3;
  --surface-600: #d4d4d4;
  --surface-700: #e5e5e5;
  --surface-800: #f5f5f5;
  --surface-900: #ffffff;
  --gray-50: #262626;
  --gray-100: #2d2d2d;
  --gray-200: #404040;
  --gray-300: #525252;
  --gray-400: #737373;
  --gray-500: #a3a3a3;
  --gray-600: #d4d4d4;
  --gray-700: #e5e5e5;
  --gray-800: #f5f5f5;
  --gray-900: #ffffff;
  --content-padding: 1rem;
  --inline-spacing: 0.5rem;
  --border-radius: 6px;
  --surface-ground: var(--dark-gray);
  --surface-section: var(--light-gray);
  --surface-card: var(--light-gray);
  --surface-overlay: var(--light-gray);
  --surface-border: var(--medium-gray);
  --surface-hover: #404040;
  --focus-ring: 0 0 0 0.2rem rgba(0, 153, 255, 0.3);
  --maskbg: rgba(0, 0, 0, 0.7);
  --highlight-bg: var(--light-blue);
  --highlight-text-color: var(--primary-blue);
  --text-color: var(--text-primary);
  --text-color-secondary: var(--text-secondary);

  // Global background
  background-color: var(--dark-gray);
  color: var(--text-primary);

  // Custom button styles for dark theme
  .p-button {
    background: var(--primary-blue);
    border: 1px solid var(--primary-blue);
    color: var(--dark-gray);
    
    &:hover {
      background: var(--accent-blue);
      border-color: var(--accent-blue);
    }
    
    &:focus {
      box-shadow: var(--focus-ring);
    }
    
    &.p-button-secondary {
      background: var(--light-gray);
      border: 1px solid var(--primary-blue);
      color: var(--primary-blue);
      
      &:hover {
        background: var(--light-blue);
        color: var(--accent-blue);
      }
    }

    &.p-button-text {
      color: var(--text-primary);
      
      &:hover {
        background: var(--surface-hover);
      }
    }
  }

  // Custom card styles for dark theme
  .p-card {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    color: var(--text-primary);
    
    .p-card-header {
      background: var(--surface-100);
      border-bottom: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }

    .p-card-title {
      color: var(--text-primary);
    }

    .p-card-subtitle {
      color: var(--text-secondary);
    }
  }

  // Custom table styles for dark theme
  .p-datatable {
    background: var(--light-gray);
    color: var(--text-primary);

    .p-datatable-header {
      background: var(--surface-100);
      border: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }
    
    .p-datatable-thead > tr > th {
      background: var(--surface-200);
      border: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }
    
    .p-datatable-tbody > tr {
      background: var(--light-gray);
      
      &:nth-child(even) {
        background: var(--surface-100);
      }
      
      &:hover {
        background: var(--surface-hover);
      }
    }
    
    .p-datatable-tbody > tr > td {
      border: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }

    .p-datatable-footer {
      background: var(--surface-100);
      border: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }
  }

  // Custom sidebar styles for dark theme
  .p-sidebar {
    background: var(--dark-gray);
    color: var(--text-primary);
    border: 1px solid var(--medium-gray);
    
    .p-sidebar-header {
      background: var(--dark-gray);
      border-bottom: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }
  }

  // Custom menu styles for dark theme
  .p-menu {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    
    .p-menuitem-link {
      color: var(--text-primary);
      
      &:hover {
        background: var(--surface-hover);
        color: var(--primary-blue);
      }
      
      &:focus {
        background: var(--surface-hover);
        color: var(--primary-blue);
      }
    }

    .p-menuitem-text {
      color: var(--text-primary);
    }

    .p-menuitem-icon {
      color: var(--text-secondary);
    }
  }

  // Custom input styles for dark theme
  .p-inputtext {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    color: var(--text-primary);
    
    &:focus {
      border-color: var(--primary-blue);
      box-shadow: var(--focus-ring);
    }

    &::placeholder {
      color: var(--text-secondary);
    }
  }

  // Custom dropdown styles for dark theme
  .p-dropdown {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    
    &:focus {
      border-color: var(--primary-blue);
      box-shadow: var(--focus-ring);
    }
    
    .p-dropdown-label {
      color: var(--text-primary);
    }

    .p-dropdown-trigger {
      color: var(--text-secondary);
    }
  }

  .p-dropdown-panel {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);

    .p-dropdown-items {
      .p-dropdown-item {
        color: var(--text-primary);

        &:hover {
          background: var(--surface-hover);
        }

        &.p-highlight {
          background: var(--primary-blue);
          color: var(--dark-gray);
        }
      }
    }
  }

  // Custom dialog styles for dark theme
  .p-dialog {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    color: var(--text-primary);

    .p-dialog-header {
      background: var(--surface-100);
      border-bottom: 1px solid var(--medium-gray);
      color: var(--text-primary);
    }

    .p-dialog-title {
      color: var(--text-primary);
    }

    .p-dialog-content {
      background: var(--light-gray);
      color: var(--text-primary);
    }

    .p-dialog-footer {
      background: var(--surface-100);
      border-top: 1px solid var(--medium-gray);
    }
  }

  // Custom toast styles for dark theme
  .p-toast {
    .p-toast-message {
      background: var(--light-gray);
      border: 1px solid var(--medium-gray);
      color: var(--text-primary);

      &.p-toast-message-success {
        background: var(--success);
        color: var(--dark-gray);
      }

      &.p-toast-message-info {
        background: var(--info);
        color: var(--dark-gray);
      }

      &.p-toast-message-warn {
        background: var(--warning);
        color: var(--dark-gray);
      }

      &.p-toast-message-error {
        background: var(--error);
        color: var(--dark-gray);
      }
    }
  }

  // Custom tab view styles for dark theme
  .p-tabview {
    .p-tabview-nav {
      background: var(--surface-100);
      border-bottom: 1px solid var(--medium-gray);
      
      .p-tabview-nav-link {
        background: transparent;
        border: 1px solid transparent;
        color: var(--text-secondary);
        
        &:hover {
          background: var(--surface-hover);
          color: var(--primary-blue);
        }
      }
      
      .p-highlight .p-tabview-nav-link {
        background: var(--light-gray);
        border-color: var(--medium-gray);
        border-bottom-color: var(--light-gray);
        color: var(--primary-blue);
      }
    }
    
    .p-tabview-panels {
      background: var(--light-gray);
      color: var(--text-primary);
    }
  }

  // Scrollbar styling for dark theme
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--surface-100);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 4px;

    &:hover {
      background: var(--surface-500);
    }
  }

  ::-webkit-scrollbar-corner {
    background: var(--surface-100);
  }
}
