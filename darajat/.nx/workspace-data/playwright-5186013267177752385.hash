{"14266486248213971227": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["admin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../../dist/.playwright/apps/admin-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../../dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../../dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["admin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "13575439746265869315": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["school"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/school-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/school-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/school-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["school"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "9177728956284458508": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["parents"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/parents-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["parents"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "8633650093016543921": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["students"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/students-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/students-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/students-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["students"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "10660495436904720973": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["professor"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/professor-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["professor"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "5516049090382253110": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["admin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../../dist/.playwright/apps/admin-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../../dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../../dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["admin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "18390534371297125717": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["parents"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/parents-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["parents"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "6917560878122336025": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["professor"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/professor-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["professor"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "9215041018132801096": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["school"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/school-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/school-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/school-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["school"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "9483206923017170211": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["students"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/students-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/students-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/students-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["students"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "17085786273792188182": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["admin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../../dist/.playwright/apps/admin-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../../dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../../dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["admin"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/apps/admin-e2e/test-output", "{workspaceRoot}/dist/.playwright/apps/admin-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "1989983127716618219": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["parents"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/parents-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["parents"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/parents-e2e/test-output", "{workspaceRoot}/dist/.playwright/parents-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "12950054821118642575": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["professor"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/professor-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["professor"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/professor-e2e/test-output", "{workspaceRoot}/dist/.playwright/professor-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "15379903517944987189": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["school"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/school-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/school-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/school-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["school"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/school-e2e/test-output", "{workspaceRoot}/dist/.playwright/school-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "13056397064662306140": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["students"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=../dist/.playwright/students-e2e/test-output/src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "../dist/.playwright/students-e2e/playwright-report/src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "../dist/.playwright/students-e2e/playwright-report/src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["students"], "target": "serve"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/students-e2e/test-output", "{workspaceRoot}/dist/.playwright/students-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}}