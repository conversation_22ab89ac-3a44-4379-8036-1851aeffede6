{"version": "6.0", "nxVersion": "21.3.1", "pathMappings": {"@darajat/shared-layout": ["shared-layout/src/index.ts"], "@darajat/shared-models": ["shared-models/src/index.ts"], "@darajat/shared-services": ["shared-services/src/index.ts"], "@darajat/shared-utils": ["shared-utils/src/index.ts"], "@darajat/ui-components": ["ui-components/src/index.ts"]}, "nxJsonPlugins": [{"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "fileMap": {"nonProjectFiles": [], "projectFileMap": {"students-e2e": [{"file": "students-e2e/eslint.config.mjs", "hash": "9406519109376614868", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "students-e2e/playwright.config.ts", "hash": "1974660213370549756", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "students-e2e/project.json", "hash": "330153593812940294"}, {"file": "students-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "students-e2e/tsconfig.json", "hash": "2249461092017240593"}], "parents-e2e": [{"file": "parents-e2e/eslint.config.mjs", "hash": "9406519109376614868", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "parents-e2e/playwright.config.ts", "hash": "13704513698078476981", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "parents-e2e/project.json", "hash": "11909834569147133280"}, {"file": "parents-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "parents-e2e/tsconfig.json", "hash": "2249461092017240593"}], "professor": [{"file": "professor/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "professor/jest.config.ts", "hash": "3683660531615447890"}, {"file": "professor/project.json", "hash": "9466188672311159791"}, {"file": "professor/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "professor/src/app/app.config.ts", "hash": "4515289115773032281", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "professor/src/app/app.html", "hash": "11987652264972370457"}, {"file": "professor/src/app/app.routes.ts", "hash": "6185150965714414910", "deps": ["npm:@angular/router"]}, {"file": "professor/src/app/app.scss", "hash": "3244421341483603138"}, {"file": "professor/src/app/app.spec.ts", "hash": "13967741013543067193", "deps": ["npm:@angular/core"]}, {"file": "professor/src/app/app.ts", "hash": "14637953342271281760", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "professor/src/app/nx-welcome.ts", "hash": "5296202878151907279", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "professor/src/index.html", "hash": "15536281519583616924"}, {"file": "professor/src/main.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "professor/src/styles.scss", "hash": "5195668842064076916"}, {"file": "professor/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "professor/tsconfig.app.json", "hash": "8568250495626871748"}, {"file": "professor/tsconfig.json", "hash": "6343381516642186316"}, {"file": "professor/tsconfig.spec.json", "hash": "1029128818174064697"}], "@darajat/source": [{"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": ".giti<PERSON>re", "hash": "17559216753634855455"}, {"file": ".prettieri<PERSON>re", "hash": "5407418692668764289"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": ".verdaccio/config.yml", "hash": "300035921567345902"}, {"file": ".vscode/extensions.json", "hash": "108759356477840841"}, {"file": "README.md", "hash": "5473126053202600919"}, {"file": "admin/src/app/features/dashboard/dashboard.component.scss", "hash": "16202208767902530954"}, {"file": "admin/src/app/features/dashboard/dashboard.component.ts", "hash": "16109817382663752604", "deps": ["npm:@angular/core", "npm:@angular/common", "ui-components"]}, {"file": "error.log", "hash": "6007351649531648271"}, {"file": "eslint.config.mjs", "hash": "10770449096712831357", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "jest.config.ts", "hash": "6870352021923392442", "deps": ["npm:jest", "npm:@nx/jest"]}, {"file": "jest.preset.js", "hash": "9430166341120122740", "deps": ["npm:@nx/jest"]}, {"file": "nx.json", "hash": "16607556629786520501"}, {"file": "package-lock.json", "hash": "12968225659825824848"}, {"file": "package.json", "hash": "12049666420641843978", "deps": ["npm:@angular-devkit/core", "npm:@angular-devkit/schematics", "npm:@angular/build", "npm:@angular/cli", "npm:@angular/compiler-cli", "npm:@angular/language-service", "npm:@eslint/js", "npm:@nx/angular", "npm:@nx/devkit", "npm:@nx/eslint", "npm:@nx/eslint-plugin", "npm:@nx/jest", "npm:@nx/js", "npm:@nx/playwright", "npm:@nx/web", "npm:@nx/workspace", "npm:@playwright/test", "npm:@schematics/angular", "npm:@swc-node/register", "npm:@swc/core", "npm:@swc/helpers", "npm:@types/express", "npm:@types/jest", "npm:@types/node", "npm:@typescript-eslint/utils", "npm:angular-eslint", "npm:autoprefixer", "npm:eslint", "npm:eslint-config-prettier", "npm:eslint-plugin-playwright", "npm:jest", "npm:jest-environment-jsdom", "npm:jest-preset-angular", "npm:jsonc-eslint-parser", "npm:ng-packagr", "npm:nx", "npm:postcss", "npm:postcss-url", "npm:prettier", "npm:ts-jest", "npm:ts-node", "npm:tslib", "npm:typescript", "npm:typescript-eslint", "npm:ve<PERSON><PERSON><PERSON>", "npm:@angular-devkit/build-angular", "npm:@angular/cdk", "npm:@angular/common", "npm:@angular/compiler", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/platform-browser", "npm:@angular/platform-browser-dynamic", "npm:@angular/platform-server", "npm:@angular/router", "npm:@angular/ssr", "npm:express", "npm:primeflex", "npm:primeicons", "npm:primeng", "npm:rxjs", "npm:zone.js"]}, {"file": "project.json", "hash": "9712739379736908088"}, {"file": "tsconfig.base.json", "hash": "4475054101621466254"}], "professor-e2e": [{"file": "professor-e2e/eslint.config.mjs", "hash": "9406519109376614868", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "professor-e2e/playwright.config.ts", "hash": "10903281105139529746", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "professor-e2e/project.json", "hash": "2703160209545769317"}, {"file": "professor-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "professor-e2e/tsconfig.json", "hash": "2249461092017240593"}], "shared-services": [{"file": "shared-services/README.md", "hash": "7026570081976240156"}, {"file": "shared-services/eslint.config.mjs", "hash": "7446528395630336329", "deps": ["npm:@nx/eslint-plugin", ["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "shared-services/jest.config.ts", "hash": "4947097804422403944"}, {"file": "shared-services/ng-package.json", "hash": "12774003437378868750"}, {"file": "shared-services/package.json", "hash": "993320111345632627", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "shared-services/project.json", "hash": "2956359999470963051"}, {"file": "shared-services/src/index.ts", "hash": "3643136541375425393"}, {"file": "shared-services/src/lib/shared-services/shared-services.css", "hash": "3244421341483603138"}, {"file": "shared-services/src/lib/shared-services/shared-services.html", "hash": "12894579473132143819"}, {"file": "shared-services/src/lib/shared-services/shared-services.spec.ts", "hash": "3031684176399248767", "deps": ["npm:@angular/core"]}, {"file": "shared-services/src/lib/shared-services/shared-services.ts", "hash": "16534544759737345614", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "shared-services/src/lib/theme/theme.service.ts", "hash": "10964777628557725444", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "shared-services/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "shared-services/tsconfig.json", "hash": "7745567414312992168"}, {"file": "shared-services/tsconfig.lib.json", "hash": "4766185256741444917"}, {"file": "shared-services/tsconfig.lib.prod.json", "hash": "10809217796916261822"}, {"file": "shared-services/tsconfig.spec.json", "hash": "1029128818174064697"}], "shared-layout": [{"file": "shared-layout/README.md", "hash": "17435570537206098810"}, {"file": "shared-layout/eslint.config.mjs", "hash": "7446528395630336329", "deps": ["npm:@nx/eslint-plugin", ["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "shared-layout/jest.config.ts", "hash": "6764044040247705477"}, {"file": "shared-layout/ng-package.json", "hash": "16820471123028480859"}, {"file": "shared-layout/package.json", "hash": "433833460157365386", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "shared-layout/project.json", "hash": "14237012484935521198"}, {"file": "shared-layout/src/index.ts", "hash": "7007047990087735669"}, {"file": "shared-layout/src/lib/components/header/header.component.scss", "hash": "4601482171504154724"}, {"file": "shared-layout/src/lib/components/header/header.component.ts", "hash": "2634279946737097059", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:primeng"]}, {"file": "shared-layout/src/lib/components/main-layout/main-layout.component.scss", "hash": "10988339023020983674"}, {"file": "shared-layout/src/lib/components/main-layout/main-layout.component.ts", "hash": "17379760368090178519", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router"]}, {"file": "shared-layout/src/lib/components/sidebar/sidebar.component.scss", "hash": "12402763514985689359"}, {"file": "shared-layout/src/lib/components/sidebar/sidebar.component.ts", "hash": "11524930417124718987", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:primeng"]}, {"file": "shared-layout/src/lib/shared-layout/shared-layout.css", "hash": "3244421341483603138"}, {"file": "shared-layout/src/lib/shared-layout/shared-layout.html", "hash": "131260603286198541"}, {"file": "shared-layout/src/lib/shared-layout/shared-layout.spec.ts", "hash": "11222542578093953438", "deps": ["npm:@angular/core"]}, {"file": "shared-layout/src/lib/shared-layout/shared-layout.ts", "hash": "10535748488514198017", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "shared-layout/src/lib/styles/global.scss", "hash": "5328974127460202837"}, {"file": "shared-layout/src/lib/styles/variables.scss", "hash": "7131081186710363846"}, {"file": "shared-layout/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "shared-layout/tsconfig.json", "hash": "7745567414312992168"}, {"file": "shared-layout/tsconfig.lib.json", "hash": "4766185256741444917"}, {"file": "shared-layout/tsconfig.lib.prod.json", "hash": "10809217796916261822"}, {"file": "shared-layout/tsconfig.spec.json", "hash": "1029128818174064697"}], "school-e2e": [{"file": "school-e2e/eslint.config.mjs", "hash": "9406519109376614868", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "school-e2e/playwright.config.ts", "hash": "18203638374193892412", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "school-e2e/project.json", "hash": "8821091821832416726"}, {"file": "school-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "school-e2e/tsconfig.json", "hash": "2249461092017240593"}], "school": [{"file": "school/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "school/jest.config.ts", "hash": "2013401180720136420"}, {"file": "school/project.json", "hash": "3241369669311918479"}, {"file": "school/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "school/src/app/app.config.ts", "hash": "4515289115773032281", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "school/src/app/app.html", "hash": "11987652264972370457"}, {"file": "school/src/app/app.routes.ts", "hash": "6185150965714414910", "deps": ["npm:@angular/router"]}, {"file": "school/src/app/app.scss", "hash": "3244421341483603138"}, {"file": "school/src/app/app.spec.ts", "hash": "10467739489784983788", "deps": ["npm:@angular/core"]}, {"file": "school/src/app/app.ts", "hash": "16254851478823937786", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "school/src/app/nx-welcome.ts", "hash": "18386108279583235540", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "school/src/index.html", "hash": "8116355787876920041"}, {"file": "school/src/main.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "school/src/styles.scss", "hash": "5195668842064076916"}, {"file": "school/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "school/tsconfig.app.json", "hash": "8568250495626871748"}, {"file": "school/tsconfig.json", "hash": "6343381516642186316"}, {"file": "school/tsconfig.spec.json", "hash": "1029128818174064697"}], "ui-components": [{"file": "ui-components/README.md", "hash": "9454200568591758621"}, {"file": "ui-components/eslint.config.mjs", "hash": "7446528395630336329", "deps": ["npm:@nx/eslint-plugin", ["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "ui-components/jest.config.ts", "hash": "13217512676914395311"}, {"file": "ui-components/ng-package.json", "hash": "6663681397182362451"}, {"file": "ui-components/package.json", "hash": "6309587052651514645", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "ui-components/project.json", "hash": "9937627921553664925"}, {"file": "ui-components/src/index.ts", "hash": "6340372017727696805"}, {"file": "ui-components/src/lib/components/analytics-chart/analytics-chart.component.scss", "hash": "13595116308881659002"}, {"file": "ui-components/src/lib/components/analytics-chart/analytics-chart.component.ts", "hash": "14938711126822220405", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:primeng"]}, {"file": "ui-components/src/lib/components/data-table/data-table.component.scss", "hash": "17175871470972205780"}, {"file": "ui-components/src/lib/components/data-table/data-table.component.ts", "hash": "*******************", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:primeng"]}, {"file": "ui-components/src/lib/components/stats-card/stats-card.component.scss", "hash": "3811159917325278554"}, {"file": "ui-components/src/lib/components/stats-card/stats-card.component.ts", "hash": "5438297560101853240", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:primeng"]}, {"file": "ui-components/src/lib/components/time-period-selector/time-period-selector.component.scss", "hash": "7677635968816774797"}, {"file": "ui-components/src/lib/components/time-period-selector/time-period-selector.component.ts", "hash": "3113690553466832791", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:primeng"]}, {"file": "ui-components/src/lib/primeng.module.ts", "hash": "17572977890022378302", "deps": ["npm:@angular/core", "npm:primeng"]}, {"file": "ui-components/src/lib/themes/vscode-blue-dark-theme.scss", "hash": "3851910308510629634"}, {"file": "ui-components/src/lib/themes/vscode-blue-theme.scss", "hash": "12677019705002213754"}, {"file": "ui-components/src/lib/ui-components/ui-components.css", "hash": "3244421341483603138"}, {"file": "ui-components/src/lib/ui-components/ui-components.html", "hash": "10332686609474566201"}, {"file": "ui-components/src/lib/ui-components/ui-components.spec.ts", "hash": "9561046933287069430", "deps": ["npm:@angular/core"]}, {"file": "ui-components/src/lib/ui-components/ui-components.ts", "hash": "7951011446651179016", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "ui-components/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "ui-components/tsconfig.json", "hash": "7745567414312992168"}, {"file": "ui-components/tsconfig.lib.json", "hash": "4766185256741444917"}, {"file": "ui-components/tsconfig.lib.prod.json", "hash": "174503844014784742"}, {"file": "ui-components/tsconfig.spec.json", "hash": "1029128818174064697"}], "parents": [{"file": "parents/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "parents/jest.config.ts", "hash": "17079848052162376834"}, {"file": "parents/project.json", "hash": "4323906731404159810"}, {"file": "parents/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "parents/src/app/app.config.ts", "hash": "4515289115773032281", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "parents/src/app/app.html", "hash": "11987652264972370457"}, {"file": "parents/src/app/app.routes.ts", "hash": "6185150965714414910", "deps": ["npm:@angular/router"]}, {"file": "parents/src/app/app.scss", "hash": "3244421341483603138"}, {"file": "parents/src/app/app.spec.ts", "hash": "15361418652323876855", "deps": ["npm:@angular/core"]}, {"file": "parents/src/app/app.ts", "hash": "9678976067464130827", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "parents/src/app/nx-welcome.ts", "hash": "1377328142657811065", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "parents/src/index.html", "hash": "17923183260149268402"}, {"file": "parents/src/main.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "parents/src/styles.scss", "hash": "5195668842064076916"}, {"file": "parents/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "parents/tsconfig.app.json", "hash": "8568250495626871748"}, {"file": "parents/tsconfig.json", "hash": "6343381516642186316"}, {"file": "parents/tsconfig.spec.json", "hash": "1029128818174064697"}], "shared-utils": [{"file": "shared-utils/README.md", "hash": "5352665523196431657"}, {"file": "shared-utils/eslint.config.mjs", "hash": "7446528395630336329", "deps": ["npm:@nx/eslint-plugin", ["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "shared-utils/jest.config.ts", "hash": "1703067853909835797"}, {"file": "shared-utils/ng-package.json", "hash": "17926138550971426114"}, {"file": "shared-utils/package.json", "hash": "8517705142743930602", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "shared-utils/project.json", "hash": "1732052706046651782"}, {"file": "shared-utils/src/index.ts", "hash": "1438452712617484999"}, {"file": "shared-utils/src/lib/shared-utils/shared-utils.css", "hash": "3244421341483603138"}, {"file": "shared-utils/src/lib/shared-utils/shared-utils.html", "hash": "14114367384469376407"}, {"file": "shared-utils/src/lib/shared-utils/shared-utils.spec.ts", "hash": "18133371803145653179", "deps": ["npm:@angular/core"]}, {"file": "shared-utils/src/lib/shared-utils/shared-utils.ts", "hash": "14969948161816151844", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "shared-utils/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "shared-utils/tsconfig.json", "hash": "7745567414312992168"}, {"file": "shared-utils/tsconfig.lib.json", "hash": "4766185256741444917"}, {"file": "shared-utils/tsconfig.lib.prod.json", "hash": "10809217796916261822"}, {"file": "shared-utils/tsconfig.spec.json", "hash": "1029128818174064697"}], "shared-models": [{"file": "shared-models/README.md", "hash": "6095378938642385271"}, {"file": "shared-models/eslint.config.mjs", "hash": "7446528395630336329", "deps": ["npm:@nx/eslint-plugin", ["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "shared-models/jest.config.ts", "hash": "2565669890020151847"}, {"file": "shared-models/ng-package.json", "hash": "15844311635958417785"}, {"file": "shared-models/package.json", "hash": "4058988854364260185", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "shared-models/project.json", "hash": "13579431610068659060"}, {"file": "shared-models/src/index.ts", "hash": "12943425610188949450"}, {"file": "shared-models/src/lib/shared-models/shared-models.css", "hash": "3244421341483603138"}, {"file": "shared-models/src/lib/shared-models/shared-models.html", "hash": "2721796942346658283"}, {"file": "shared-models/src/lib/shared-models/shared-models.spec.ts", "hash": "8479613081168875270", "deps": ["npm:@angular/core"]}, {"file": "shared-models/src/lib/shared-models/shared-models.ts", "hash": "11761209030007435910", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "shared-models/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "shared-models/tsconfig.json", "hash": "7745567414312992168"}, {"file": "shared-models/tsconfig.lib.json", "hash": "4766185256741444917"}, {"file": "shared-models/tsconfig.lib.prod.json", "hash": "10809217796916261822"}, {"file": "shared-models/tsconfig.spec.json", "hash": "1029128818174064697"}], "admin": [{"file": "apps/admin/eslint.config.mjs", "hash": "16756400681144203361", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/admin/jest.config.ts", "hash": "6801427979096267512"}, {"file": "apps/admin/project.json", "hash": "13638337267239832678"}, {"file": "apps/admin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/admin/src/app/app.config.server.ts", "hash": "16664769899172877666", "deps": ["npm:@angular/core", "npm:@angular/ssr"]}, {"file": "apps/admin/src/app/app.config.ts", "hash": "500917040339330447", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/platform-browser"]}, {"file": "apps/admin/src/app/app.html", "hash": "9982673033907225339"}, {"file": "apps/admin/src/app/app.routes.server.ts", "hash": "16960858348243221237", "deps": ["npm:@angular/ssr"]}, {"file": "apps/admin/src/app/app.routes.ts", "hash": "16852356747905977939", "deps": ["npm:@angular/router"]}, {"file": "apps/admin/src/app/app.scss", "hash": "3244421341483603138"}, {"file": "apps/admin/src/app/app.spec.ts", "hash": "1281019857725725581", "deps": ["npm:@angular/core"]}, {"file": "apps/admin/src/app/app.ts", "hash": "7637760117418910836", "deps": ["npm:@angular/core", "npm:@angular/router", "shared-layout", "shared-services"]}, {"file": "apps/admin/src/app/features/classes/classes.component.ts", "hash": "1148902671624300727", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/app/features/dashboard/dashboard.component.scss", "hash": "16202208767902530954"}, {"file": "apps/admin/src/app/features/dashboard/dashboard.component.ts", "hash": "16109817382663752604", "deps": ["npm:@angular/core", "npm:@angular/common", "ui-components"]}, {"file": "apps/admin/src/app/features/reports/reports.component.ts", "hash": "1782705881371405007", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/app/features/schedules/schedules.component.ts", "hash": "13327198026566679472", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/app/features/settings/settings.component.ts", "hash": "17281698548405385299", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/app/features/students/students.component.ts", "hash": "10959486380718353501", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/app/features/teachers/teachers.component.ts", "hash": "12140178686571450047", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/app/nx-welcome.ts", "hash": "682561857912004471", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/admin/src/index.html", "hash": "7899792888691802531"}, {"file": "apps/admin/src/main.server.ts", "hash": "17902611827504992161", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/admin/src/main.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/admin/src/server.ts", "hash": "4605081636799508606", "deps": ["npm:@angular/ssr", "npm:express"]}, {"file": "apps/admin/src/styles.scss", "hash": "5195668842064076916"}, {"file": "apps/admin/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "apps/admin/tsconfig.app.json", "hash": "16022103093302060448"}, {"file": "apps/admin/tsconfig.json", "hash": "4663124578817442910"}, {"file": "apps/admin/tsconfig.spec.json", "hash": "5331518322626158299"}], "students": [{"file": "students/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "students/jest.config.ts", "hash": "3993836414716952995"}, {"file": "students/project.json", "hash": "17157445883068833277"}, {"file": "students/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "students/src/app/app.config.ts", "hash": "4515289115773032281", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "students/src/app/app.html", "hash": "11987652264972370457"}, {"file": "students/src/app/app.routes.ts", "hash": "6185150965714414910", "deps": ["npm:@angular/router"]}, {"file": "students/src/app/app.scss", "hash": "3244421341483603138"}, {"file": "students/src/app/app.spec.ts", "hash": "11056436542485431954", "deps": ["npm:@angular/core"]}, {"file": "students/src/app/app.ts", "hash": "8206675309898443821", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "students/src/app/nx-welcome.ts", "hash": "12994821492369460618", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "students/src/index.html", "hash": "3053707860930809233"}, {"file": "students/src/main.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "students/src/styles.scss", "hash": "5195668842064076916"}, {"file": "students/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "students/tsconfig.app.json", "hash": "8568250495626871748"}, {"file": "students/tsconfig.json", "hash": "6343381516642186316"}, {"file": "students/tsconfig.spec.json", "hash": "1029128818174064697"}], "admin-e2e": [{"file": "apps/admin-e2e/eslint.config.mjs", "hash": "1172734138943286526", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "apps/admin-e2e/playwright.config.ts", "hash": "7432757985344820213", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "apps/admin-e2e/project.json", "hash": "8281949658785373040"}, {"file": "apps/admin-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "apps/admin-e2e/tsconfig.json", "hash": "15726545290547512047"}]}}}