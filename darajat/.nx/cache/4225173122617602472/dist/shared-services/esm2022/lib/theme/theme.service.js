import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import * as i0 from "@angular/core";
export const AVAILABLE_THEMES = [
    {
        name: 'vscode-blue',
        displayName: 'VSCode Blue',
        primaryColor: '#007ACC',
        secondaryColor: '#005A9E',
        isDark: false,
        cssClass: 'theme-vscode-blue'
    },
    {
        name: 'vscode-blue-dark',
        displayName: 'VSCode Blue Dark',
        primaryColor: '#0099FF',
        secondaryColor: '#007ACC',
        isDark: true,
        cssClass: 'theme-vscode-blue-dark'
    },
    {
        name: 'material-blue',
        displayName: 'Material Blue',
        primaryColor: '#2196F3',
        secondaryColor: '#1976D2',
        isDark: false,
        cssClass: 'theme-material-blue'
    },
    {
        name: 'material-blue-dark',
        displayName: 'Material Blue Dark',
        primaryColor: '#42A5F5',
        secondaryColor: '#2196F3',
        isDark: true,
        cssClass: 'theme-material-blue-dark'
    }
];
export class ThemeService {
    THEME_STORAGE_KEY = 'darajat-theme';
    DEFAULT_THEME = 'vscode-blue';
    currentThemeSubject = new BehaviorSubject(this.getThemeByName(this.DEFAULT_THEME));
    currentTheme$ = this.currentThemeSubject.asObservable();
    constructor() {
        this.initializeTheme();
    }
    /**
     * Initialize theme from localStorage or use default
     */
    initializeTheme() {
        const savedTheme = this.getSavedTheme();
        if (savedTheme) {
            this.setTheme(savedTheme, false);
        }
        else {
            // Check for system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const defaultTheme = prefersDark ? 'vscode-blue-dark' : 'vscode-blue';
            this.setTheme(defaultTheme);
        }
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!this.getSavedTheme()) {
                const theme = e.matches ? 'vscode-blue-dark' : 'vscode-blue';
                this.setTheme(theme, false);
            }
        });
    }
    /**
     * Get all available themes
     */
    getAvailableThemes() {
        return AVAILABLE_THEMES;
    }
    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentThemeSubject.value;
    }
    /**
     * Set theme by name
     */
    setTheme(themeName, saveToStorage = true) {
        const theme = this.getThemeByName(themeName);
        this.applyTheme(theme);
        this.currentThemeSubject.next(theme);
        if (saveToStorage) {
            this.saveTheme(themeName);
        }
    }
    /**
     * Toggle between light and dark variants of current theme
     */
    toggleDarkMode() {
        const currentTheme = this.getCurrentTheme();
        let newThemeName;
        if (currentTheme.isDark) {
            // Switch to light variant
            newThemeName = currentTheme.name.replace('-dark', '');
        }
        else {
            // Switch to dark variant
            newThemeName = currentTheme.name + '-dark';
        }
        // Check if the target theme exists
        const targetTheme = AVAILABLE_THEMES.find(t => t.name === newThemeName);
        if (targetTheme) {
            this.setTheme(newThemeName);
        }
    }
    /**
     * Check if current theme is dark
     */
    isDarkMode() {
        return this.getCurrentTheme().isDark;
    }
    /**
     * Apply theme to document
     */
    applyTheme(theme) {
        const body = document.body;
        // Remove all theme classes
        AVAILABLE_THEMES.forEach(t => {
            body.classList.remove(t.cssClass);
        });
        // Add current theme class
        body.classList.add(theme.cssClass);
        // Update CSS custom properties
        this.updateCSSVariables(theme);
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(theme.primaryColor);
    }
    /**
     * Update CSS custom properties for the theme
     */
    updateCSSVariables(theme) {
        const root = document.documentElement;
        root.style.setProperty('--theme-primary', theme.primaryColor);
        root.style.setProperty('--theme-secondary', theme.secondaryColor);
        if (theme.isDark) {
            root.style.setProperty('--theme-background', '#1a1a1a');
            root.style.setProperty('--theme-surface', '#2d2d2d');
            root.style.setProperty('--theme-text-primary', '#ffffff');
            root.style.setProperty('--theme-text-secondary', '#b3b3b3');
            root.style.setProperty('--theme-border', '#404040');
        }
        else {
            root.style.setProperty('--theme-background', '#ffffff');
            root.style.setProperty('--theme-surface', '#f5f5f5');
            root.style.setProperty('--theme-text-primary', '#212121');
            root.style.setProperty('--theme-text-secondary', '#757575');
            root.style.setProperty('--theme-border', '#e0e0e0');
        }
    }
    /**
     * Update meta theme-color for mobile browsers
     */
    updateMetaThemeColor(color) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.setAttribute('name', 'theme-color');
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.setAttribute('content', color);
    }
    /**
     * Get theme by name
     */
    getThemeByName(name) {
        return AVAILABLE_THEMES.find(theme => theme.name === name) || AVAILABLE_THEMES[0];
    }
    /**
     * Save theme to localStorage
     */
    saveTheme(themeName) {
        try {
            localStorage.setItem(this.THEME_STORAGE_KEY, themeName);
        }
        catch (error) {
            console.warn('Failed to save theme to localStorage:', error);
        }
    }
    /**
     * Get saved theme from localStorage
     */
    getSavedTheme() {
        try {
            return localStorage.getItem(this.THEME_STORAGE_KEY);
        }
        catch (error) {
            console.warn('Failed to get theme from localStorage:', error);
            return null;
        }
    }
    /**
     * Reset theme to default
     */
    resetTheme() {
        this.setTheme(this.DEFAULT_THEME);
    }
    /**
     * Get theme colors for charts and other components
     */
    getThemeColors() {
        const theme = this.getCurrentTheme();
        return {
            primary: theme.primaryColor,
            secondary: theme.secondaryColor,
            success: '#4CAF50',
            warning: '#FF9800',
            error: '#F44336',
            info: '#2196F3'
        };
    }
    /**
     * Generate chart color palette based on current theme
     */
    getChartColorPalette() {
        const colors = this.getThemeColors();
        return [
            colors.primary,
            colors.secondary,
            colors.success,
            colors.info,
            colors.warning,
            colors.error,
            '#9C27B0', // Purple
            '#FF5722', // Deep Orange
            '#607D8B', // Blue Grey
            '#795548' // Brown
        ];
    }
    static ɵfac = function ThemeService_Factory(__ngFactoryType__) { return new (__ngFactoryType__ || ThemeService)(); };
    static ɵprov = /*@__PURE__*/ i0.ɵɵdefineInjectable({ token: ThemeService, factory: ThemeService.ɵfac, providedIn: 'root' });
}
(() => { (typeof ngDevMode === "undefined" || ngDevMode) && i0.ɵsetClassMetadata(ThemeService, [{
        type: Injectable,
        args: [{
                providedIn: 'root'
            }]
    }], () => [], null); })();
//# sourceMappingURL=data:application/json;base64,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