import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import * as i0 from "@angular/core";
export class SharedServices {
    static ɵfac = function SharedServices_Factory(__ngFactoryType__) { return new (__ngFactoryType__ || SharedServices)(); };
    static ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: SharedServices, selectors: [["lib-shared-services"]], decls: 2, vars: 0, template: function SharedServices_Template(rf, ctx) { if (rf & 1) {
            i0.ɵɵdomElementStart(0, "p");
            i0.ɵɵtext(1, "SharedServices works!");
            i0.ɵɵdomElementEnd();
        } }, dependencies: [CommonModule], encapsulation: 2 });
}
(() => { (typeof ngDevMode === "undefined" || ngDevMode) && i0.ɵsetClassMetadata(SharedServices, [{
        type: Component,
        args: [{ selector: 'lib-shared-services', imports: [CommonModule], template: "<p>SharedServices works!</p>\n" }]
    }], null, null); })();
(() => { (typeof ngDevMode === "undefined" || ngDevMode) && i0.ɵsetClassDebugInfo(SharedServices, { className: "SharedServices", filePath: "lib/shared-services/shared-services.ts", lineNumber: 10 }); })();
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2hhcmVkLXNlcnZpY2VzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vc2hhcmVkLXNlcnZpY2VzL3NyYy9saWIvc2hhcmVkLXNlcnZpY2VzL3NoYXJlZC1zZXJ2aWNlcy50cyIsIi4uLy4uLy4uLy4uLy4uL3NoYXJlZC1zZXJ2aWNlcy9zcmMvbGliL3NoYXJlZC1zZXJ2aWNlcy9zaGFyZWQtc2VydmljZXMuaHRtbCJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxPQUFPLEVBQUUsU0FBUyxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBQzFDLE9BQU8sRUFBRSxZQUFZLEVBQUUsTUFBTSxpQkFBaUIsQ0FBQzs7QUFRL0MsTUFBTSxPQUFPLGNBQWM7d0dBQWQsY0FBYzs2REFBZCxjQUFjO1lDVDNCLDRCQUFHO1lBQUEscUNBQXFCO1lBQUEsb0JBQUk7NEJES2hCLFlBQVk7O2lGQUlYLGNBQWM7Y0FOMUIsU0FBUzsyQkFDRSxxQkFBcUIsV0FDdEIsQ0FBQyxZQUFZLENBQUM7O2tGQUlaLGNBQWMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb21wb25lbnQgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcbmltcG9ydCB7IENvbW1vbk1vZHVsZSB9IGZyb20gJ0Bhbmd1bGFyL2NvbW1vbic7XG5cbkBDb21wb25lbnQoe1xuICBzZWxlY3RvcjogJ2xpYi1zaGFyZWQtc2VydmljZXMnLFxuICBpbXBvcnRzOiBbQ29tbW9uTW9kdWxlXSxcbiAgdGVtcGxhdGVVcmw6ICcuL3NoYXJlZC1zZXJ2aWNlcy5odG1sJyxcbiAgc3R5bGVVcmw6ICcuL3NoYXJlZC1zZXJ2aWNlcy5jc3MnLFxufSlcbmV4cG9ydCBjbGFzcyBTaGFyZWRTZXJ2aWNlcyB7fVxuIiwiPHA+U2hhcmVkU2VydmljZXMgd29ya3MhPC9wPlxuIl19