[34mBuilding Angular Package[39m
[37m[39m
[37m------------------------------------------------------------------------------[39m
[37mBuilding entry point '@darajat/shared-layout'[39m
[37m------------------------------------------------------------------------------[39m
[1G[1G[36m⠋[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠙[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠹[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠸[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠼[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠴[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠦[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠧[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠇[39m Compiling with Angular sources in full compilation mode.[33mWARNING: ▲ [WARNING] Deprecation [plugin angular-sass][39m
[33m[39m
[33m    src/lib/components/main-layout/main-layout.component.scss:1:8:[39m
[33m      1 │ @import '../../styles/variables.scss';[39m
[33m        ╵         ^[39m
[33m[39m
[33m[39m
[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m
[33m  [39m
[33m  More info and automated migrator: https://sass-lang.com/d/import[39m
[33m[39m
[33m[39m
[33mWARNING: ▲ [WARNING] Deprecation [plugin angular-sass][39m
[33m[39m
[33m    src/lib/components/header/header.component.scss:1:8:[39m
[33m      1 │ @import '../../styles/variables.scss';[39m
[33m        ╵         ^[39m
[33m[39m
[33m[39m
[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m
[33m  [39m
[33m  More info and automated migrator: https://sass-lang.com/d/import[39m
[33m[39m
[33m[39m
[1G[0K[36m⠏[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠋[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠙[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠹[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠸[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠼[39m Compiling with Angular sources in full compilation mode.[33mWARNING: ▲ [WARNING] Deprecation [plugin angular-sass][39m
[33m[39m
[33m    src/lib/components/sidebar/sidebar.component.scss:1:8:[39m
[33m      1 │ @import '../../styles/variables.scss';[39m
[33m        ╵         ^[39m
[33m[39m
[33m[39m
[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m
[33m  [39m
[33m  More info and automated migrator: https://sass-lang.com/d/import[39m
[33m[39m
[33m[39m
[1G[0K[31m✖[39m Compiling with Angular sources in full compilation mode.

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31m[96mshared-layout/src/lib/components/header/header.component.ts[0m:[93m10[0m:[93m36[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/overlaypanel' or its corresponding type declarations.[39m


[7m10[0m import { OverlayPanelModule } from 'primeng/overlaypanel';
[7m  [0m [91m                                   ~~~~~~~~~~~~~~~~~~~~~~[0m
[96mshared-layout/src/lib/components/header/header.component.ts[0m:[93m25[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m25[0m     OverlayPanelModule,
[7m  [0m [91m    ~~~~~~~~~~~~~~~~~~[0m

  [96mshared-layout/src/lib/components/header/header.component.ts[0m:[93m25[0m:[93m5[0m
    [7m25[0m     OverlayPanelModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~~~~~~[0m
    Unknown reference.

Pass --verbose to see the stacktrace.

