[34mBuilding Angular Package[39m
[37m[39m
[37m------------------------------------------------------------------------------[39m
[37mBuilding entry point '@darajat/ui-components'[39m
[37m------------------------------------------------------------------------------[39m
[1G[1G[36m⠋[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠙[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠹[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠸[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠼[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠴[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠦[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠧[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠇[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠏[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠋[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠙[39m Compiling with Angular sources in partial compilation mode.[1G[0K[36m⠹[39m Compiling with Angular sources in partial compilation mode.[1G[0K[31m✖[39m Compiling with Angular sources in partial compilation mode.

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31m[96mui-components/src/lib/primeng.module.ts[0m:[93m7[0m:[93m31[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/sidebar' or its corresponding type declarations.[39m


[7m7[0m import { SidebarModule } from 'primeng/sidebar';
[7m [0m [91m                              ~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/primeng.module.ts[0m:[93m11[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/dropdown' or its corresponding type declarations.

[7m11[0m import { DropdownModule } from 'primeng/dropdown';
[7m  [0m [91m                               ~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/primeng.module.ts[0m:[93m12[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/calendar' or its corresponding type declarations.

[7m12[0m import { CalendarModule } from 'primeng/calendar';
[7m  [0m [91m                               ~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/primeng.module.ts[0m:[93m16[0m:[93m31[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/tabview' or its corresponding type declarations.

[7m16[0m import { TabViewModule } from 'primeng/tabview';
[7m  [0m [91m                              ~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/primeng.module.ts[0m:[93m28[0m:[93m36[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/overlaypanel' or its corresponding type declarations.

[7m28[0m import { OverlayPanelModule } from 'primeng/overlaypanel';
[7m  [0m [91m                                   ~~~~~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/primeng.module.ts[0m:[93m73[0m:[93m12[0m - [91merror[0m[90m NG1010: [0mValue at position 3 in the NgModule.imports of PrimeNgModule is not a reference
  Value could not be determined statically.

[7m73[0m   imports: PRIMENG_MODULES,
[7m  [0m [91m           ~~~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/primeng.module.ts[0m:[93m41[0m:[93m3[0m
    [7m41[0m   SidebarModule,
    [7m  [0m [96m  ~~~~~~~~~~~~~[0m
    Unknown reference.
[96mui-components/src/lib/components/analytics-chart/analytics-chart.component.ts[0m:[93m6[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/dropdown' or its corresponding type declarations.

[7m6[0m import { DropdownModule } from 'primeng/dropdown';
[7m [0m [91m                               ~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/analytics-chart/analytics-chart.component.ts[0m:[93m35[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m35[0m     DropdownModule,
[7m  [0m [91m    ~~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/components/analytics-chart/analytics-chart.component.ts[0m:[93m35[0m:[93m5[0m
    [7m35[0m     DropdownModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~~[0m
    Unknown reference.
[96mui-components/src/lib/components/stats-card/stats-card.component.ts[0m:[93m65[0m:[93m19[0m - [91merror[0m[90m NG8002: [0mCan't bind to 'pTooltip' since it isn't a known property of 'button'.

[7m65[0m                   [pTooltip]="action.label"
[7m  [0m [91m                  ~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/data-table/data-table.component.ts[0m:[93m6[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/dropdown' or its corresponding type declarations.

[7m6[0m import { DropdownModule } from 'primeng/dropdown';
[7m [0m [91m                               ~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/data-table/data-table.component.ts[0m:[93m9[0m:[93m36[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/overlaypanel' or its corresponding type declarations.

[7m9[0m import { OverlayPanelModule } from 'primeng/overlaypanel';
[7m [0m [91m                                   ~~~~~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/data-table/data-table.component.ts[0m:[93m45[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m45[0m     DropdownModule,
[7m  [0m [91m    ~~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/components/data-table/data-table.component.ts[0m:[93m45[0m:[93m5[0m
    [7m45[0m     DropdownModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~~[0m
    Unknown reference.
[96mui-components/src/lib/components/data-table/data-table.component.ts[0m:[93m48[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m48[0m     OverlayPanelModule,
[7m  [0m [91m    ~~~~~~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/components/data-table/data-table.component.ts[0m:[93m48[0m:[93m5[0m
    [7m48[0m     OverlayPanelModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~~~~~~[0m
    Unknown reference.
[96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m3[0m:[93m31[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/tabview' or its corresponding type declarations.

[7m3[0m import { TabViewModule } from 'primeng/tabview';
[7m [0m [91m                              ~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m4[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/calendar' or its corresponding type declarations.

[7m4[0m import { CalendarModule } from 'primeng/calendar';
[7m [0m [91m                               ~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m6[0m:[93m32[0m - [91merror[0m[90m TS2307: [0mCannot find module 'primeng/dropdown' or its corresponding type declarations.

[7m6[0m import { DropdownModule } from 'primeng/dropdown';
[7m [0m [91m                               ~~~~~~~~~~~~~~~~~~[0m
[96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m26[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m26[0m     TabViewModule,
[7m  [0m [91m    ~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m26[0m:[93m5[0m
    [7m26[0m     TabViewModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~[0m
    Unknown reference.
[96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m27[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m27[0m     CalendarModule,
[7m  [0m [91m    ~~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m27[0m:[93m5[0m
    [7m27[0m     CalendarModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~~[0m
    Unknown reference.
[96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m29[0m:[93m5[0m - [91merror[0m[90m NG1010: [0m'imports' must be an array of components, directives, pipes, or NgModules.
  Value could not be determined statically.

[7m29[0m     DropdownModule,
[7m  [0m [91m    ~~~~~~~~~~~~~~[0m

  [96mui-components/src/lib/components/time-period-selector/time-period-selector.component.ts[0m:[93m29[0m:[93m5[0m
    [7m29[0m     DropdownModule,
    [7m  [0m [96m    ~~~~~~~~~~~~~~[0m
    Unknown reference.

Pass --verbose to see the stacktrace.

