> nx run shared-services:build:production


[34mBuilding Angular Package[39m
[37m[39m
[37m------------------------------------------------------------------------------[39m
[37mBuilding entry point '@darajat/shared-services'[39m
[37m------------------------------------------------------------------------------[39m
[1G[1G[36m⠋[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠙[39m Compiling with Angular sources in full compilation mode.[1G[0K[36m⠹[39m Compiling with Angular sources in full compilation mode.[1G[0K[32m✔[39m Compiling with Angular sources in full compilation mode.
[1G[1G[36m⠋[39m Copying assets[1G[0K[32m✔[39m Copying assets
[1G[36m⠋[39m Writing package manifest[1G[0K[32m✔[39m Writing package manifest
[1G[32m✔[39m Built @darajat/shared-services
[32m[39m
[32m------------------------------------------------------------------------------[39m
[32mBuilt Angular Package[39m
[32m- from: /Users/<USER>/Desktop/darajat/darajat/shared-services[39m
[32m- to:   /Users/<USER>/Desktop/darajat/darajat/dist/shared-services[39m
[32m------------------------------------------------------------------------------[39m
[37m[37m[39m[37m[39m
[37m[37mBuild at: [1m2025-07-20T22:13:26.685Z[22m - Time: [1m1942[22mms[39m[37m[39m
[37m[37m[39m[37m[39m
