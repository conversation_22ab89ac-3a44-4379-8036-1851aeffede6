@import '../../styles/variables.scss';

.sidebar {
  position: fixed;
  top: var(--header-height);
  left: 0;
  bottom: 0;
  width: var(--sidebar-width);
  background: var(--dark-blue);
  color: var(--white);
  transition: width var(--transition-base);
  z-index: var(--z-index-sticky);
  display: flex;
  flex-direction: column;
  box-shadow: var(--box-shadow);

  &.expanded {
    width: var(--sidebar-expanded-width);
  }

  .sidebar-content {
    flex: 1;
    overflow: hidden;
    padding: var(--spacing-md) 0;

    .sidebar-nav {
      height: 100%;

      .nav-list {
        list-style: none;
        margin: 0;
        padding: 0;

        .nav-item {
          margin-bottom: var(--spacing-xs);

          &.active {
            .nav-link {
              background: var(--primary-blue);
              color: var(--white);

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: var(--accent-blue);
              }
            }
          }

          .nav-link {
            position: relative;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition-base);
            border: none;
            background: transparent;
            width: 100%;
            cursor: pointer;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
              color: var(--white);
            }

            &:focus {
              outline: none;
              background: rgba(255, 255, 255, 0.1);
              color: var(--white);
            }

            .nav-icon {
              font-size: var(--font-size-lg);
              min-width: 24px;
              text-align: center;
              flex-shrink: 0;
            }

            .nav-label {
              font-weight: var(--font-weight-medium);
              white-space: nowrap;
              opacity: 1;
              transition: opacity var(--transition-base);
              flex: 1;

              &.hidden {
                opacity: 0;
                pointer-events: none;
              }
            }

            .nav-badge {
              margin-left: auto;
              flex-shrink: 0;
            }
          }

          .nav-button {
            justify-content: flex-start;
            text-align: left;
          }
        }
      }
    }
  }

  .sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .sidebar-toggle {
      width: 100%;
      color: rgba(255, 255, 255, 0.8);
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--white);
      }
    }
  }
}

// Badge styles
:host ::ng-deep {
  .badge-primary {
    background: var(--primary-blue);
    color: var(--white);
  }

  .badge-danger {
    background: var(--error);
    color: var(--white);
  }

  .badge-warning {
    background: var(--warning);
    color: var(--white);
  }

  .badge-success {
    background: var(--success);
    color: var(--white);
  }

  .badge-info {
    background: var(--info);
    color: var(--white);
  }
}

// Responsive design
@media (max-width: $breakpoint-lg) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-base);

    &.expanded {
      transform: translateX(0);
      width: var(--sidebar-expanded-width);
    }
  }
}

// Mobile overlay
@media (max-width: $breakpoint-md) {
  .sidebar {
    &.expanded {
      width: 100vw;
      background: rgba(0, 0, 0, 0.9);

      .sidebar-content {
        width: var(--sidebar-expanded-width);
        background: var(--dark-blue);
      }
    }
  }
}

// Animation for nav labels
.sidebar:not(.expanded) {
  .nav-label {
    transform: translateX(-10px);
    transition: transform var(--transition-base), opacity var(--transition-base);
  }
}

.sidebar.expanded {
  .nav-label {
    transform: translateX(0);
    transition: transform var(--transition-base) 0.1s, opacity var(--transition-base) 0.1s;
  }
}

// Hover effects for collapsed sidebar
.sidebar:not(.expanded) {
  .nav-item:hover {
    .nav-link {
      &::after {
        content: attr(data-label);
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        background: var(--dark-gray);
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--border-radius);
        white-space: nowrap;
        margin-left: var(--spacing-sm);
        font-size: var(--font-size-sm);
        z-index: var(--z-index-tooltip);
        box-shadow: var(--box-shadow);
        opacity: 0;
        animation: fadeIn 0.2s ease-in-out forwards;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

// Scrollbar styling for sidebar content
.sidebar-content {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
