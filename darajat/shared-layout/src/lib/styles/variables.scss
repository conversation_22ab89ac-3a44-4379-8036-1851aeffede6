// VSCode Blue Color Scheme Variables
// Primary Colors
$primary-blue: #007ACC;
$secondary-blue: #005A9E;
$light-blue: #E3F2FD;
$white: #FFFFFF;
$dark-blue: #003d6b;
$accent-blue: #0099ff;

// Neutral Colors
$light-gray: #F5F5F5;
$medium-gray: #E0E0E0;
$dark-gray: #424242;
$text-primary: #212121;
$text-secondary: #757575;

// Status Colors
$success: #4CAF50;
$warning: #FF9800;
$error: #F44336;
$info: #2196F3;

// Layout Variables
$header-height: 64px;
$sidebar-width: 80px;
$sidebar-expanded-width: 250px;
$content-padding: 24px;
$border-radius: 6px;
$box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-elevated: 0 4px 8px rgba(0, 0, 0, 0.15);

// Typography
$font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-2xl: 1.5rem;
$font-size-3xl: 1.875rem;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-2xl: 3rem;

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1400px;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// Animation
$transition-base: all 0.2s ease-in-out;
$transition-fast: all 0.15s ease-in-out;
$transition-slow: all 0.3s ease-in-out;

// CSS Custom Properties for runtime theming
:root {
  --primary-blue: #{$primary-blue};
  --secondary-blue: #{$secondary-blue};
  --light-blue: #{$light-blue};
  --white: #{$white};
  --dark-blue: #{$dark-blue};
  --accent-blue: #{$accent-blue};
  
  --light-gray: #{$light-gray};
  --medium-gray: #{$medium-gray};
  --dark-gray: #{$dark-gray};
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  
  --success: #{$success};
  --warning: #{$warning};
  --error: #{$error};
  --info: #{$info};
  
  --header-height: #{$header-height};
  --sidebar-width: #{$sidebar-width};
  --sidebar-expanded-width: #{$sidebar-expanded-width};
  --content-padding: #{$content-padding};
  --border-radius: #{$border-radius};
  
  --font-family: #{$font-family};
  --font-size-base: #{$font-size-base};
  --font-weight-normal: #{$font-weight-normal};
  --font-weight-medium: #{$font-weight-medium};
  --font-weight-semibold: #{$font-weight-semibold};
  --font-weight-bold: #{$font-weight-bold};
  
  --spacing-xs: #{$spacing-xs};
  --spacing-sm: #{$spacing-sm};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --spacing-xl: #{$spacing-xl};
  --spacing-2xl: #{$spacing-2xl};
  
  --transition-base: #{$transition-base};
  --transition-fast: #{$transition-fast};
  --transition-slow: #{$transition-slow};
  
  --box-shadow: #{$box-shadow};
  --box-shadow-elevated: #{$box-shadow-elevated};
}

// Utility mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin card-style {
  background: $white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
}

@mixin button-primary {
  background: $primary-blue;
  color: $white;
  border: 1px solid $primary-blue;
  border-radius: $border-radius;
  padding: $spacing-sm $spacing-md;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  cursor: pointer;
  
  &:hover {
    background: $secondary-blue;
    border-color: $secondary-blue;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.2);
  }
}

@mixin button-secondary {
  background: $white;
  color: $primary-blue;
  border: 1px solid $primary-blue;
  border-radius: $border-radius;
  padding: $spacing-sm $spacing-md;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  cursor: pointer;
  
  &:hover {
    background: $light-blue;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-blue, 0.2);
  }
}

@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
