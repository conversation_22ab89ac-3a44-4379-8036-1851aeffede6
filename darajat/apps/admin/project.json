{"name": "admin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/admin/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/admin", "browser": "apps/admin/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/admin/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/admin/public"}], "styles": ["apps/admin/src/styles.scss"], "server": "apps/admin/src/main.server.ts", "ssr": {"entry": "apps/admin/src/server.ts"}, "outputMode": "server"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "admin:build:production"}, "development": {"buildTarget": "admin:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "admin:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/admin/jest.config.ts"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "admin:build", "port": 4200, "staticFilePath": "dist/apps/admin/browser", "spa": true}}}}