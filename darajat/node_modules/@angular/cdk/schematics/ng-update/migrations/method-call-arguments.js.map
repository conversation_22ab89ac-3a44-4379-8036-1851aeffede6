{"version": 3, "file": "method-call-arguments.js", "sourceRoot": "", "sources": ["method-call-arguments.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,iCAAiC;AACjC,2DAAsD;AAGtD,kDAAmE;AAEnE;;;GAGG;AACH,MAAa,4BAA6B,SAAQ,qBAAsB;IAAxE;;QACE,iEAAiE;QACjE,SAAI,GAA4B,IAAA,oCAAqB,EAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;QAEhF,2DAA2D;QAC3D,YAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IA8CnC,CAAC;IA5CU,SAAS,CAAC,IAAa;QAC9B,IAAI,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,0BAA0B,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAChF,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAEO,8BAA8B,CAAC,IAAuB;QAC5D,MAAM,cAAc,GAAG,IAAI,CAAC,UAAyC,CAAC;QAEtE,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;QAC7D,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;QAE5C,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,wFAAwF;QACxF,yFAAyF;QACzF,uFAAuF;QACvF,sFAAsF;QACtF,qFAAqF;QACrF,wFAAwF;QACxF,wFAAwF;QACxF,uFAAuF;QACvF,yCAAyC;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;aACtB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC;aAC7E,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,IAAI,CAAC,mBAAmB,CACtB,IAAI,EACJ,kBAAkB,YAAY,GAAG,GAAG,GAAG,UAAU,IAAI;YACnD,QAAQ,OAAO,CAAC,KAAK,wBAAwB,OAAO,CAAC,OAAO,EAAE,CACjE,CAAC;IACJ,CAAC;CACF;AAnDD,oEAmDC"}