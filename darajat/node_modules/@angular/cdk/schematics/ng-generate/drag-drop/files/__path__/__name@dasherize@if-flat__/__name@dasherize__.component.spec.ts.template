import { <% if(!standalone) { %>waitForAsync, <% } %>ComponentFixture, TestBed } from '@angular/core/testing';<% if(!standalone) { %>
import { DragDropModule } from '@angular/cdk/drag-drop';<% } %>
import { <%= classify(name) %>Component } from './<%= dasherize(name) %>.component';

describe('<%= classify(name) %>Component', () => {
  let component: <%= classify(name) %>Component;
  let fixture: ComponentFixture<<%= classify(name) %>Component>;<% if(!standalone) { %>

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [<%= classify(name) %>Component],
      imports: [DragDropModule]
    });
  }));<% } %>

  beforeEach(() => {
    fixture = TestBed.createComponent(<%= classify(name) %>Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should compile', () => {
    expect(component).toBeTruthy();
  });
});
