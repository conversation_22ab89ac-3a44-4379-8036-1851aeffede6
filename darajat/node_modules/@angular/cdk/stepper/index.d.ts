import * as i0 from '@angular/core';
import { ElementRef, TemplateRef, InjectionToken, OnChanges, QueryList, EventEmitter, AfterContentInit, AfterViewInit, OnDestroy } from '@angular/core';
import { NgForm, FormGroupDirective, AbstractControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { F as FocusableOption } from '../focus-key-manager.d-DBw2_DcY.js';
import { B as BidiModule } from '../bidi-module.d-IN1Vp56w.js';
import '../list-key-manager.d-CkFcwXee.js';
import '../focus-monitor.d-2iZxjw4R.js';

declare class CdkStepHeader implements FocusableOption {
    _elementRef: ElementRef<HTMLElement>;
    constructor(...args: unknown[]);
    /** Focuses the step header. */
    focus(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStepHeader, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<CdkStepHeader, "[cdkStepHeader]", never, {}, {}, never, never, true, never>;
}

declare class CdkStepLabel {
    template: TemplateRef<any>;
    constructor(...args: unknown[]);
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStepLabel, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<CdkStepLabel, "[cdkStepLabel]", never, {}, {}, never, never, true, never>;
}

/**
 * Position state of the content of each step in stepper that is used for transitioning
 * the content into correct position upon step selection change.
 */
type StepContentPositionState = 'previous' | 'current' | 'next';
/** Possible orientation of a stepper. */
type StepperOrientation = 'horizontal' | 'vertical';
/** Change event emitted on selection changes. */
declare class StepperSelectionEvent {
    /** Index of the step now selected. */
    selectedIndex: number;
    /** Index of the step previously selected. */
    previouslySelectedIndex: number;
    /** The step instance now selected. */
    selectedStep: CdkStep;
    /** The step instance previously selected. */
    previouslySelectedStep: CdkStep;
}
/** The state of each step. */
type StepState = 'number' | 'edit' | 'done' | 'error' | string;
/** Enum to represent the different states of the steps. */
declare const STEP_STATE: {
    NUMBER: string;
    EDIT: string;
    DONE: string;
    ERROR: string;
};
/** InjectionToken that can be used to specify the global stepper options. */
declare const STEPPER_GLOBAL_OPTIONS: InjectionToken<StepperOptions>;
/** Configurable options for stepper. */
interface StepperOptions {
    /**
     * Whether the stepper should display an error state or not.
     * Default behavior is assumed to be false.
     */
    showError?: boolean;
    /**
     * Whether the stepper should display the default indicator type
     * or not.
     * Default behavior is assumed to be true.
     */
    displayDefaultIndicatorType?: boolean;
}
declare class CdkStep implements OnChanges {
    private _stepperOptions;
    _stepper: CdkStepper;
    _displayDefaultIndicatorType: boolean;
    /** Template for step label if it exists. */
    stepLabel: CdkStepLabel;
    /** Forms that have been projected into the step. */
    protected _childForms: QueryList<Partial<NgForm | FormGroupDirective>> | undefined;
    /** Template for step content. */
    content: TemplateRef<any>;
    /** The top level abstract control of the step. */
    stepControl: AbstractControl;
    /** Whether user has attempted to move away from the step. */
    get interacted(): boolean;
    set interacted(value: boolean);
    private _interacted;
    /** Emits when the user has attempted to move away from the step. */
    readonly interactedStream: EventEmitter<CdkStep>;
    /** Plain text label of the step. */
    label: string;
    /** Error message to display when there's an error. */
    errorMessage: string;
    /** Aria label for the tab. */
    ariaLabel: string;
    /**
     * Reference to the element that the tab is labelled by.
     * Will be cleared if `aria-label` is set at the same time.
     */
    ariaLabelledby: string;
    /** State of the step. */
    get state(): StepState;
    set state(value: StepState);
    private _state;
    /** Whether the user can return to this step once it has been marked as completed. */
    get editable(): boolean;
    set editable(value: boolean);
    private _editable;
    /** Whether the completion of step is optional. */
    optional: boolean;
    /** Whether step is marked as completed. */
    get completed(): boolean;
    set completed(value: boolean);
    _completedOverride: i0.WritableSignal<boolean | null>;
    /** Current index of the step within the stepper. */
    readonly index: i0.WritableSignal<number>;
    /** Whether the step is selected. */
    readonly isSelected: i0.Signal<boolean>;
    /** Type of indicator that should be shown for the step. */
    readonly indicatorType: i0.Signal<string>;
    /** Whether the user can navigate to the step. */
    readonly isNavigable: i0.Signal<boolean>;
    /** Whether step has an error. */
    get hasError(): boolean;
    set hasError(value: boolean);
    private _customError;
    private _getDefaultError;
    constructor(...args: unknown[]);
    /** Selects this step component. */
    select(): void;
    /** Resets the step to its initial state. Note that this includes resetting form data. */
    reset(): void;
    ngOnChanges(): void;
    _markAsInteracted(): void;
    /** Determines whether the error state can be shown. */
    _showError(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStep, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<CdkStep, "cdk-step", ["cdkStep"], { "stepControl": { "alias": "stepControl"; "required": false; }; "label": { "alias": "label"; "required": false; }; "errorMessage": { "alias": "errorMessage"; "required": false; }; "ariaLabel": { "alias": "aria-label"; "required": false; }; "ariaLabelledby": { "alias": "aria-labelledby"; "required": false; }; "state": { "alias": "state"; "required": false; }; "editable": { "alias": "editable"; "required": false; }; "optional": { "alias": "optional"; "required": false; }; "completed": { "alias": "completed"; "required": false; }; "hasError": { "alias": "hasError"; "required": false; }; }, { "interactedStream": "interacted"; }, ["stepLabel", "_childForms"], ["*"], true, never>;
    static ngAcceptInputType_editable: unknown;
    static ngAcceptInputType_optional: unknown;
    static ngAcceptInputType_completed: unknown;
    static ngAcceptInputType_hasError: unknown;
}
declare class CdkStepper implements AfterContentInit, AfterViewInit, OnDestroy {
    private _dir;
    private _changeDetectorRef;
    protected _elementRef: ElementRef<HTMLElement>;
    /** Emits when the component is destroyed. */
    protected readonly _destroyed: Subject<void>;
    /** Used for managing keyboard focus. */
    private _keyManager;
    /** Full list of steps inside the stepper, including inside nested steppers. */
    _steps: QueryList<CdkStep>;
    /** Steps that belong to the current stepper, excluding ones from nested steppers. */
    readonly steps: QueryList<CdkStep>;
    /** The list of step headers of the steps in the stepper. */
    _stepHeader: QueryList<CdkStepHeader>;
    /** List of step headers sorted based on their DOM order. */
    private _sortedHeaders;
    /** Whether the validity of previous steps should be checked or not. */
    linear: boolean;
    /** The index of the selected step. */
    get selectedIndex(): number;
    set selectedIndex(index: number);
    private _selectedIndex;
    /** The step that is selected. */
    get selected(): CdkStep | undefined;
    set selected(step: CdkStep | undefined);
    /** Event emitted when the selected step has changed. */
    readonly selectionChange: EventEmitter<StepperSelectionEvent>;
    /** Output to support two-way binding on `[(selectedIndex)]` */
    readonly selectedIndexChange: EventEmitter<number>;
    /** Used to track unique ID for each stepper component. */
    private _groupId;
    /** Orientation of the stepper. */
    get orientation(): StepperOrientation;
    set orientation(value: StepperOrientation);
    private _orientation;
    constructor(...args: unknown[]);
    ngAfterContentInit(): void;
    ngAfterViewInit(): void;
    ngOnDestroy(): void;
    /** Selects and focuses the next step in list. */
    next(): void;
    /** Selects and focuses the previous step in list. */
    previous(): void;
    /** Resets the stepper to its initial state. Note that this includes clearing form data. */
    reset(): void;
    /** Returns a unique id for each step label element. */
    _getStepLabelId(i: number): string;
    /** Returns unique id for each step content element. */
    _getStepContentId(i: number): string;
    /** Marks the component to be change detected. */
    _stateChanged(): void;
    /** Returns position state of the step with the given index. */
    _getAnimationDirection(index: number): StepContentPositionState;
    /** Returns the index of the currently-focused step header. */
    _getFocusIndex(): number | null;
    private _updateSelectedItemIndex;
    _onKeydown(event: KeyboardEvent): void;
    private _anyControlsInvalidOrPending;
    private _layoutDirection;
    /** Checks whether the stepper contains the focused element. */
    private _containsFocus;
    /** Checks whether the passed-in index is a valid step index. */
    private _isValidIndex;
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStepper, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<CdkStepper, "[cdkStepper]", ["cdkStepper"], { "linear": { "alias": "linear"; "required": false; }; "selectedIndex": { "alias": "selectedIndex"; "required": false; }; "selected": { "alias": "selected"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; }, { "selectionChange": "selectionChange"; "selectedIndexChange": "selectedIndexChange"; }, ["_steps", "_stepHeader"], never, true, never>;
    static ngAcceptInputType_linear: unknown;
    static ngAcceptInputType_selectedIndex: unknown;
}

/** Button that moves to the next step in a stepper workflow. */
declare class CdkStepperNext {
    _stepper: CdkStepper;
    /** Type of the next button. Defaults to "submit" if not specified. */
    type: string;
    constructor(...args: unknown[]);
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStepperNext, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<CdkStepperNext, "button[cdkStepperNext]", never, { "type": { "alias": "type"; "required": false; }; }, {}, never, never, true, never>;
}
/** Button that moves to the previous step in a stepper workflow. */
declare class CdkStepperPrevious {
    _stepper: CdkStepper;
    /** Type of the previous button. Defaults to "button" if not specified. */
    type: string;
    constructor(...args: unknown[]);
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStepperPrevious, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<CdkStepperPrevious, "button[cdkStepperPrevious]", never, { "type": { "alias": "type"; "required": false; }; }, {}, never, never, true, never>;
}

declare class CdkStepperModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<CdkStepperModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<CdkStepperModule, never, [typeof BidiModule, typeof CdkStep, typeof CdkStepper, typeof CdkStepHeader, typeof CdkStepLabel, typeof CdkStepperNext, typeof CdkStepperPrevious], [typeof CdkStep, typeof CdkStepper, typeof CdkStepHeader, typeof CdkStepLabel, typeof CdkStepperNext, typeof CdkStepperPrevious]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<CdkStepperModule>;
}

export { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };
export type { StepContentPositionState, StepState, StepperOptions, StepperOrientation };
