{"version": 3, "file": "listbox.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/listbox/listbox.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/listbox/listbox-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  _IdGenerator,\n  ActiveDescendantKeyManager,\n  Highlightable,\n  ListKeyManagerOption,\n} from '../a11y';\nimport {Directionality} from '../bidi';\nimport {coerceArray} from '../coercion';\nimport {SelectionModel} from '../collections';\nimport {\n  A,\n  DOWN_ARROW,\n  END,\n  ENTER,\n  hasModifierKey,\n  HOME,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  SPACE,\n  UP_ARROW,\n} from '../keycodes';\nimport {Platform} from '../platform';\nimport {\n  AfterContentInit,\n  booleanAttribute,\n  ChangeDetectorRef,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  forwardRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  Output,\n  QueryList,\n  Renderer2,\n  signal,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {defer, merge, Observable, Subject} from 'rxjs';\nimport {filter, map, startWith, switchMap, takeUntil} from 'rxjs/operators';\n\n/**\n * An implementation of SelectionModel that internally always represents the selection as a\n * multi-selection. This is necessary so that we can recover the full selection if the user\n * switches the listbox from single-selection to multi-selection after initialization.\n *\n * This selection model may report multiple selected values, even if it is in single-selection\n * mode. It is up to the user (CdkListbox) to check for invalid selections.\n */\nclass ListboxSelectionModel<T> extends SelectionModel<T> {\n  constructor(\n    public multiple = false,\n    initiallySelectedValues?: T[],\n    emitChanges = true,\n    compareWith?: (o1: T, o2: T) => boolean,\n  ) {\n    super(true, initiallySelectedValues, emitChanges, compareWith);\n  }\n\n  override isMultipleSelection(): boolean {\n    return this.multiple;\n  }\n\n  override select(...values: T[]) {\n    // The super class is always in multi-selection mode, so we need to override the behavior if\n    // this selection model actually belongs to a single-selection listbox.\n    if (this.multiple) {\n      return super.select(...values);\n    } else {\n      return super.setSelection(...values);\n    }\n  }\n}\n\n/** A selectable option in a listbox. */\n@Directive({\n  selector: '[cdkOption]',\n  exportAs: 'cdkOption',\n  host: {\n    'role': 'option',\n    'class': 'cdk-option',\n    '[id]': 'id',\n    '[attr.aria-selected]': 'isSelected()',\n    '[attr.tabindex]': '_getTabIndex()',\n    '[attr.aria-disabled]': 'disabled',\n    '[class.cdk-option-active]': 'isActive()',\n    '(click)': '_clicked.next($event)',\n    '(focus)': '_handleFocus()',\n  },\n})\nexport class CdkOption<T = unknown> implements ListKeyManagerOption, Highlightable, OnDestroy {\n  /** The id of the option's host element. */\n  @Input()\n  get id() {\n    return this._id || this._generatedId;\n  }\n  set id(value) {\n    this._id = value;\n  }\n  private _id: string;\n  private _generatedId = inject(_IdGenerator).getId('cdk-option-');\n\n  /** The value of this option. */\n  @Input('cdkOption') value: T;\n\n  /**\n   * The text used to locate this item during listbox typeahead. If not specified,\n   * the `textContent` of the item will be used.\n   */\n  @Input('cdkOptionTypeaheadLabel') typeaheadLabel: string | null;\n\n  /** Whether this option is disabled. */\n  @Input({alias: 'cdkOptionDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this.listbox.disabled || this._disabled();\n  }\n  set disabled(value: boolean) {\n    this._disabled.set(value);\n  }\n  private _disabled = signal(false);\n\n  /** The tabindex of the option when it is enabled. */\n  @Input('tabindex')\n  get enabledTabIndex() {\n    return this._enabledTabIndex() === undefined\n      ? this.listbox.enabledTabIndex\n      : this._enabledTabIndex();\n  }\n  set enabledTabIndex(value) {\n    this._enabledTabIndex.set(value);\n  }\n  private _enabledTabIndex = signal<number | null | undefined>(undefined);\n\n  /** The option's host element */\n  readonly element: HTMLElement = inject(ElementRef).nativeElement;\n\n  /** The parent listbox this option belongs to. */\n  protected readonly listbox: CdkListbox<T> = inject(CdkListbox);\n\n  /** Emits when the option is destroyed. */\n  protected destroyed = new Subject<void>();\n\n  /** Emits when the option is clicked. */\n  readonly _clicked = new Subject<MouseEvent>();\n\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n\n  /** Whether this option is selected. */\n  isSelected() {\n    return this.listbox.isSelected(this);\n  }\n\n  /** Whether this option is active. */\n  isActive() {\n    return this.listbox.isActive(this);\n  }\n\n  /** Toggle the selected state of this option. */\n  toggle() {\n    this.listbox.toggle(this);\n  }\n\n  /** Select this option if it is not selected. */\n  select() {\n    this.listbox.select(this);\n  }\n\n  /** Deselect this option if it is selected. */\n  deselect() {\n    this.listbox.deselect(this);\n  }\n\n  /** Focus this option. */\n  focus() {\n    this.element.focus();\n  }\n\n  /** Get the label for this element which is required by the FocusableOption interface. */\n  getLabel() {\n    return (this.typeaheadLabel ?? this.element.textContent?.trim()) || '';\n  }\n\n  /**\n   * No-op implemented as a part of `Highlightable`.\n   * @docs-private\n   */\n  setActiveStyles() {\n    // If the listbox is using `aria-activedescendant` the option won't have focus so the\n    // browser won't scroll them into view automatically so we need to do it ourselves.\n    if (this.listbox.useActiveDescendant) {\n      this.element.scrollIntoView({block: 'nearest', inline: 'nearest'});\n    }\n  }\n\n  /**\n   * No-op implemented as a part of `Highlightable`.\n   * @docs-private\n   */\n  setInactiveStyles() {}\n\n  /** Handle focus events on the option. */\n  protected _handleFocus() {\n    // Options can wind up getting focused in active descendant mode if the user clicks on them.\n    // In this case, we push focus back to the parent listbox to prevent an extra tab stop when\n    // the user performs a shift+tab.\n    if (this.listbox.useActiveDescendant) {\n      this.listbox._setActiveOption(this);\n      this.listbox.focus();\n    }\n  }\n\n  /** Get the tabindex for this option. */\n  protected _getTabIndex() {\n    if (this.listbox.useActiveDescendant || this.disabled) {\n      return -1;\n    }\n    return this.isActive() ? this.enabledTabIndex : -1;\n  }\n}\n\n@Directive({\n  selector: '[cdkListbox]',\n  exportAs: 'cdkListbox',\n  host: {\n    'role': 'listbox',\n    'class': 'cdk-listbox',\n    '[id]': 'id',\n    '[attr.tabindex]': '_getTabIndex()',\n    '[attr.aria-disabled]': 'disabled',\n    '[attr.aria-multiselectable]': 'multiple',\n    '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n    '[attr.aria-orientation]': 'orientation',\n    '(focus)': '_handleFocus()',\n    '(keydown)': '_handleKeydown($event)',\n    '(focusout)': '_handleFocusOut($event)',\n    '(focusin)': '_handleFocusIn()',\n  },\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => CdkListbox),\n      multi: true,\n    },\n  ],\n})\nexport class CdkListbox<T = unknown> implements AfterContentInit, OnDestroy, ControlValueAccessor {\n  private _cleanupWindowBlur: (() => void) | undefined;\n\n  /** The id of the option's host element. */\n  @Input()\n  get id() {\n    return this._id || this._generatedId;\n  }\n  set id(value) {\n    this._id = value;\n  }\n  private _id: string;\n  private _generatedId = inject(_IdGenerator).getId('cdk-listbox-');\n\n  /** The tabindex to use when the listbox is enabled. */\n  @Input('tabindex')\n  get enabledTabIndex() {\n    return this._enabledTabIndex() === undefined ? 0 : this._enabledTabIndex();\n  }\n  set enabledTabIndex(value) {\n    this._enabledTabIndex.set(value);\n  }\n  private _enabledTabIndex = signal<number | null | undefined>(undefined);\n\n  /** The value selected in the listbox, represented as an array of option values. */\n  @Input('cdkListboxValue')\n  get value(): readonly T[] {\n    return this._invalid ? [] : this.selectionModel.selected;\n  }\n  set value(value: readonly T[]) {\n    this._setSelection(value);\n  }\n\n  /**\n   * Whether the listbox allows multiple options to be selected. If the value switches from `true`\n   * to `false`, and more than one option is selected, all options are deselected.\n   */\n  @Input({alias: 'cdkListboxMultiple', transform: booleanAttribute})\n  get multiple(): boolean {\n    return this.selectionModel.multiple;\n  }\n  set multiple(value: boolean) {\n    this.selectionModel.multiple = value;\n\n    if (this.options) {\n      this._updateInternalValue();\n    }\n  }\n\n  /** Whether the listbox is disabled. */\n  @Input({alias: 'cdkListboxDisabled', transform: booleanAttribute})\n  get disabled() {\n    return this._disabled();\n  }\n  set disabled(value: boolean) {\n    this._disabled.set(value);\n  }\n  private _disabled = signal(false);\n\n  /** Whether the listbox will use active descendant or will move focus onto the options. */\n  @Input({alias: 'cdkListboxUseActiveDescendant', transform: booleanAttribute})\n  get useActiveDescendant() {\n    return this._useActiveDescendant();\n  }\n  set useActiveDescendant(value: boolean) {\n    this._useActiveDescendant.set(value);\n  }\n  private _useActiveDescendant = signal(false);\n\n  /** The orientation of the listbox. Only affects keyboard interaction, not visual layout. */\n  @Input('cdkListboxOrientation')\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(value: 'horizontal' | 'vertical') {\n    this._orientation = value === 'horizontal' ? 'horizontal' : 'vertical';\n    if (value === 'horizontal') {\n      this.listKeyManager?.withHorizontalOrientation(this._dir?.value || 'ltr');\n    } else {\n      this.listKeyManager?.withVerticalOrientation();\n    }\n  }\n  private _orientation: 'horizontal' | 'vertical' = 'vertical';\n\n  /** The function used to compare option values. */\n  @Input('cdkListboxCompareWith')\n  get compareWith(): undefined | ((o1: T, o2: T) => boolean) {\n    return this.selectionModel.compareWith;\n  }\n  set compareWith(fn: undefined | ((o1: T, o2: T) => boolean)) {\n    this.selectionModel.compareWith = fn;\n  }\n\n  /**\n   * Whether the keyboard navigation should wrap when the user presses arrow down on the last item\n   * or arrow up on the first item.\n   */\n  @Input({alias: 'cdkListboxNavigationWrapDisabled', transform: booleanAttribute})\n  get navigationWrapDisabled() {\n    return this._navigationWrapDisabled;\n  }\n  set navigationWrapDisabled(wrap: boolean) {\n    this._navigationWrapDisabled = wrap;\n    this.listKeyManager?.withWrap(!this._navigationWrapDisabled);\n  }\n  private _navigationWrapDisabled = false;\n\n  /** Whether keyboard navigation should skip over disabled items. */\n  @Input({alias: 'cdkListboxNavigatesDisabledOptions', transform: booleanAttribute})\n  get navigateDisabledOptions() {\n    return this._navigateDisabledOptions;\n  }\n  set navigateDisabledOptions(skip: boolean) {\n    this._navigateDisabledOptions = skip;\n    this.listKeyManager?.skipPredicate(\n      this._navigateDisabledOptions ? this._skipNonePredicate : this._skipDisabledPredicate,\n    );\n  }\n  private _navigateDisabledOptions = false;\n\n  /** Emits when the selected value(s) in the listbox change. */\n  @Output('cdkListboxValueChange') readonly valueChange = new Subject<ListboxValueChangeEvent<T>>();\n\n  /** The child options in this listbox. */\n  @ContentChildren(CdkOption, {descendants: true}) protected options: QueryList<CdkOption<T>>;\n\n  /** The selection model used by the listbox. */\n  protected selectionModel = new ListboxSelectionModel<T>();\n\n  /** The key manager that manages keyboard navigation for this listbox. */\n  protected listKeyManager: ActiveDescendantKeyManager<CdkOption<T>>;\n\n  /** Emits when the listbox is destroyed. */\n  protected readonly destroyed = new Subject<void>();\n\n  /** The host element of the listbox. */\n  protected readonly element: HTMLElement = inject(ElementRef).nativeElement;\n\n  /** The Angular zone. */\n  protected readonly ngZone = inject(NgZone);\n\n  /** The change detector for this listbox. */\n  protected readonly changeDetectorRef = inject(ChangeDetectorRef);\n\n  /** Whether the currently selected value in the selection model is invalid. */\n  private _invalid = false;\n\n  /** The last user-triggered option. */\n  private _lastTriggered: CdkOption<T> | null = null;\n\n  /** Callback called when the listbox has been touched */\n  private _onTouched = () => {};\n\n  /** Callback called when the listbox value changes */\n  private _onChange: (value: readonly T[]) => void = () => {};\n\n  /** Emits when an option has been clicked. */\n  private _optionClicked = defer(() =>\n    (this.options.changes as Observable<CdkOption<T>[]>).pipe(\n      startWith(this.options),\n      switchMap(options =>\n        merge(...options.map(option => option._clicked.pipe(map(event => ({option, event}))))),\n      ),\n    ),\n  );\n\n  /** The directionality of the page. */\n  private readonly _dir = inject(Directionality, {optional: true});\n\n  /** Whether the component is being rendered in the browser. */\n  private readonly _isBrowser: boolean = inject(Platform).isBrowser;\n\n  /** A predicate that skips disabled options. */\n  private readonly _skipDisabledPredicate = (option: CdkOption<T>) => option.disabled;\n\n  /** A predicate that does not skip any options. */\n  private readonly _skipNonePredicate = () => false;\n\n  /** Whether the listbox currently has focus. */\n  private _hasFocus = false;\n\n  /** A reference to the option that was active before the listbox lost focus. */\n  private _previousActiveOption: CdkOption<T> | null = null;\n\n  constructor() {\n    if (this._isBrowser) {\n      const renderer = inject(Renderer2);\n\n      this._cleanupWindowBlur = this.ngZone.runOutsideAngular(() => {\n        return renderer.listen('window', 'blur', () => {\n          if (this.element.contains(document.activeElement) && this._previousActiveOption) {\n            this._setActiveOption(this._previousActiveOption);\n            this._previousActiveOption = null;\n          }\n        });\n      });\n    }\n  }\n\n  ngAfterContentInit() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      this._verifyNoOptionValueCollisions();\n      this._verifyOptionValues();\n    }\n\n    this._initKeyManager();\n\n    // Update the internal value whenever the options or the model value changes.\n    merge(this.selectionModel.changed, this.options.changes)\n      .pipe(startWith(null), takeUntil(this.destroyed))\n      .subscribe(() => this._updateInternalValue());\n\n    this._optionClicked\n      .pipe(\n        filter(({option}) => !option.disabled),\n        takeUntil(this.destroyed),\n      )\n      .subscribe(({option, event}) => this._handleOptionClicked(option, event));\n  }\n\n  ngOnDestroy() {\n    this._cleanupWindowBlur?.();\n    this.listKeyManager?.destroy();\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n\n  /**\n   * Toggle the selected state of the given option.\n   * @param option The option to toggle\n   */\n  toggle(option: CdkOption<T>) {\n    this.toggleValue(option.value);\n  }\n\n  /**\n   * Toggle the selected state of the given value.\n   * @param value The value to toggle\n   */\n  toggleValue(value: T) {\n    if (this._invalid) {\n      this.selectionModel.clear(false);\n    }\n    this.selectionModel.toggle(value);\n  }\n\n  /**\n   * Select the given option.\n   * @param option The option to select\n   */\n  select(option: CdkOption<T>) {\n    this.selectValue(option.value);\n  }\n\n  /**\n   * Select the given value.\n   * @param value The value to select\n   */\n  selectValue(value: T) {\n    if (this._invalid) {\n      this.selectionModel.clear(false);\n    }\n    this.selectionModel.select(value);\n  }\n\n  /**\n   * Deselect the given option.\n   * @param option The option to deselect\n   */\n  deselect(option: CdkOption<T>) {\n    this.deselectValue(option.value);\n  }\n\n  /**\n   * Deselect the given value.\n   * @param value The value to deselect\n   */\n  deselectValue(value: T) {\n    if (this._invalid) {\n      this.selectionModel.clear(false);\n    }\n    this.selectionModel.deselect(value);\n  }\n\n  /**\n   * Set the selected state of all options.\n   * @param isSelected The new selected state to set\n   */\n  setAllSelected(isSelected: boolean) {\n    if (!isSelected) {\n      this.selectionModel.clear();\n    } else {\n      if (this._invalid) {\n        this.selectionModel.clear(false);\n      }\n      this.selectionModel.select(...this.options.map(option => option.value));\n    }\n  }\n\n  /**\n   * Get whether the given option is selected.\n   * @param option The option to get the selected state of\n   */\n  isSelected(option: CdkOption<T>) {\n    return this.isValueSelected(option.value);\n  }\n\n  /**\n   * Get whether the given option is active.\n   * @param option The option to get the active state of\n   */\n  isActive(option: CdkOption<T>): boolean {\n    return !!(this.listKeyManager?.activeItem === option);\n  }\n\n  /**\n   * Get whether the given value is selected.\n   * @param value The value to get the selected state of\n   */\n  isValueSelected(value: T) {\n    if (this._invalid) {\n      return false;\n    }\n    return this.selectionModel.isSelected(value);\n  }\n\n  /**\n   * Registers a callback to be invoked when the listbox's value changes from user input.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnChange(fn: (value: readonly T[]) => void): void {\n    this._onChange = fn;\n  }\n\n  /**\n   * Registers a callback to be invoked when the listbox is blurred by the user.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnTouched(fn: () => {}): void {\n    this._onTouched = fn;\n  }\n\n  /**\n   * Sets the listbox's value.\n   * @param value The new value of the listbox\n   * @docs-private\n   */\n  writeValue(value: readonly T[]): void {\n    this._setSelection(value);\n    this._verifyOptionValues();\n  }\n\n  /**\n   * Sets the disabled state of the listbox.\n   * @param isDisabled The new disabled state\n   * @docs-private\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this.changeDetectorRef.markForCheck();\n  }\n\n  /** Focus the listbox's host element. */\n  focus() {\n    this.element.focus();\n  }\n\n  /**\n   * Triggers the given option in response to user interaction.\n   * - In single selection mode: selects the option and deselects any other selected option.\n   * - In multi selection mode: toggles the selected state of the option.\n   * @param option The option to trigger\n   */\n  protected triggerOption(option: CdkOption<T> | null) {\n    if (option && !option.disabled) {\n      this._lastTriggered = option;\n      const changed = this.multiple\n        ? this.selectionModel.toggle(option.value)\n        : this.selectionModel.select(option.value);\n      if (changed) {\n        this._onChange(this.value);\n        this.valueChange.next({\n          value: this.value,\n          listbox: this,\n          option: option,\n        });\n      }\n    }\n  }\n\n  /**\n   * Trigger the given range of options in response to user interaction.\n   * Should only be called in multi-selection mode.\n   * @param trigger The option that was triggered\n   * @param from The start index of the options to toggle\n   * @param to The end index of the options to toggle\n   * @param on Whether to toggle the option range on\n   */\n  protected triggerRange(trigger: CdkOption<T> | null, from: number, to: number, on: boolean) {\n    if (this.disabled || (trigger && trigger.disabled)) {\n      return;\n    }\n    this._lastTriggered = trigger;\n    const isEqual = this.compareWith ?? Object.is;\n    const updateValues = [...this.options]\n      .slice(Math.max(0, Math.min(from, to)), Math.min(this.options.length, Math.max(from, to) + 1))\n      .filter(option => !option.disabled)\n      .map(option => option.value);\n    const selected = [...this.value];\n    for (const updateValue of updateValues) {\n      const selectedIndex = selected.findIndex(selectedValue =>\n        isEqual(selectedValue, updateValue),\n      );\n      if (on && selectedIndex === -1) {\n        selected.push(updateValue);\n      } else if (!on && selectedIndex !== -1) {\n        selected.splice(selectedIndex, 1);\n      }\n    }\n    let changed = this.selectionModel.setSelection(...selected);\n    if (changed) {\n      this._onChange(this.value);\n      this.valueChange.next({\n        value: this.value,\n        listbox: this,\n        option: trigger,\n      });\n    }\n  }\n\n  /**\n   * Sets the given option as active.\n   * @param option The option to make active\n   */\n  _setActiveOption(option: CdkOption<T>) {\n    this.listKeyManager.setActiveItem(option);\n  }\n\n  /** Called when the listbox receives focus. */\n  protected _handleFocus() {\n    if (!this.useActiveDescendant) {\n      if (this.selectionModel.selected.length > 0) {\n        this._setNextFocusToSelectedOption();\n      } else {\n        this.listKeyManager.setNextItemActive();\n      }\n\n      this._focusActiveOption();\n    }\n  }\n\n  /** Called when the user presses keydown on the listbox. */\n  protected _handleKeydown(event: KeyboardEvent) {\n    if (this.disabled) {\n      return;\n    }\n\n    const {keyCode} = event;\n    const previousActiveIndex = this.listKeyManager.activeItemIndex;\n    const ctrlKeys = ['ctrlKey', 'metaKey'] as const;\n\n    if (this.multiple && keyCode === A && hasModifierKey(event, ...ctrlKeys)) {\n      // Toggle all options off if they're all selected, otherwise toggle them all on.\n      this.triggerRange(\n        null,\n        0,\n        this.options.length - 1,\n        this.options.length !== this.value.length,\n      );\n      event.preventDefault();\n      return;\n    }\n\n    if (\n      this.multiple &&\n      (keyCode === SPACE || keyCode === ENTER) &&\n      hasModifierKey(event, 'shiftKey')\n    ) {\n      if (this.listKeyManager.activeItem && this.listKeyManager.activeItemIndex != null) {\n        this.triggerRange(\n          this.listKeyManager.activeItem,\n          this._getLastTriggeredIndex() ?? this.listKeyManager.activeItemIndex,\n          this.listKeyManager.activeItemIndex,\n          !this.listKeyManager.activeItem.isSelected(),\n        );\n      }\n      event.preventDefault();\n      return;\n    }\n\n    if (\n      this.multiple &&\n      keyCode === HOME &&\n      hasModifierKey(event, ...ctrlKeys) &&\n      hasModifierKey(event, 'shiftKey')\n    ) {\n      const trigger = this.listKeyManager.activeItem;\n      if (trigger) {\n        const from = this.listKeyManager.activeItemIndex!;\n        this.listKeyManager.setFirstItemActive();\n        this.triggerRange(\n          trigger,\n          from,\n          this.listKeyManager.activeItemIndex!,\n          !trigger.isSelected(),\n        );\n      }\n      event.preventDefault();\n      return;\n    }\n\n    if (\n      this.multiple &&\n      keyCode === END &&\n      hasModifierKey(event, ...ctrlKeys) &&\n      hasModifierKey(event, 'shiftKey')\n    ) {\n      const trigger = this.listKeyManager.activeItem;\n      if (trigger) {\n        const from = this.listKeyManager.activeItemIndex!;\n        this.listKeyManager.setLastItemActive();\n        this.triggerRange(\n          trigger,\n          from,\n          this.listKeyManager.activeItemIndex!,\n          !trigger.isSelected(),\n        );\n      }\n      event.preventDefault();\n      return;\n    }\n\n    if (keyCode === SPACE || keyCode === ENTER) {\n      this.triggerOption(this.listKeyManager.activeItem);\n      event.preventDefault();\n      return;\n    }\n\n    const isNavKey =\n      keyCode === UP_ARROW ||\n      keyCode === DOWN_ARROW ||\n      keyCode === LEFT_ARROW ||\n      keyCode === RIGHT_ARROW ||\n      keyCode === HOME ||\n      keyCode === END;\n    this.listKeyManager.onKeydown(event);\n    // Will select an option if shift was pressed while navigating to the option\n    if (isNavKey && event.shiftKey && previousActiveIndex !== this.listKeyManager.activeItemIndex) {\n      this.triggerOption(this.listKeyManager.activeItem);\n    }\n  }\n\n  /** Called when a focus moves into the listbox. */\n  protected _handleFocusIn() {\n    // Note that we use a `focusin` handler for this instead of the existing `focus` handler,\n    // because focus won't land on the listbox if `useActiveDescendant` is enabled.\n    this._hasFocus = true;\n  }\n\n  /**\n   * Called when the focus leaves an element in the listbox.\n   * @param event The focusout event\n   */\n  protected _handleFocusOut(event: FocusEvent) {\n    // Some browsers (e.g. Chrome and Firefox) trigger the focusout event when the user returns back to the document.\n    // To prevent losing the active option in this case, we store it in `_previousActiveOption` and restore it on the window `blur` event\n    // This ensures that the `activeItem` matches the actual focused element when the user returns to the document.\n    this._previousActiveOption = this.listKeyManager.activeItem;\n\n    const otherElement = event.relatedTarget as Element;\n    if (this.element !== otherElement && !this.element.contains(otherElement)) {\n      this._onTouched();\n      this._hasFocus = false;\n      this._setNextFocusToSelectedOption();\n    }\n  }\n\n  /** Get the id of the active option if active descendant is being used. */\n  protected _getAriaActiveDescendant(): string | null | undefined {\n    return this.useActiveDescendant ? this.listKeyManager?.activeItem?.id : null;\n  }\n\n  /** Get the tabindex for the listbox. */\n  protected _getTabIndex() {\n    if (this.disabled) {\n      return -1;\n    }\n    return this.useActiveDescendant || !this.listKeyManager.activeItem ? this.enabledTabIndex : -1;\n  }\n\n  /** Initialize the key manager. */\n  private _initKeyManager() {\n    this.listKeyManager = new ActiveDescendantKeyManager(this.options)\n      .withWrap(!this._navigationWrapDisabled)\n      .withTypeAhead()\n      .withHomeAndEnd()\n      .withAllowedModifierKeys(['shiftKey'])\n      .skipPredicate(\n        this._navigateDisabledOptions ? this._skipNonePredicate : this._skipDisabledPredicate,\n      );\n\n    if (this.orientation === 'vertical') {\n      this.listKeyManager.withVerticalOrientation();\n    } else {\n      this.listKeyManager.withHorizontalOrientation(this._dir?.value || 'ltr');\n    }\n\n    if (this.selectionModel.selected.length) {\n      Promise.resolve().then(() => this._setNextFocusToSelectedOption());\n    }\n\n    this.listKeyManager.change.subscribe(() => this._focusActiveOption());\n\n    this.options.changes.pipe(takeUntil(this.destroyed)).subscribe(() => {\n      const activeOption = this.listKeyManager.activeItem;\n\n      // If the active option was deleted, we need to reset\n      // the key manager so it can allow focus back in.\n      if (activeOption && !this.options.find(option => option === activeOption)) {\n        this.listKeyManager.setActiveItem(-1);\n        this.changeDetectorRef.markForCheck();\n      }\n    });\n  }\n\n  /** Focus the active option. */\n  private _focusActiveOption() {\n    if (!this.useActiveDescendant) {\n      this.listKeyManager.activeItem?.focus();\n    }\n    this.changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Set the selected values.\n   * @param value The list of new selected values.\n   */\n  private _setSelection(value: readonly T[]) {\n    if (this._invalid) {\n      this.selectionModel.clear(false);\n    }\n    this.selectionModel.setSelection(...this._coerceValue(value));\n\n    if (!this._hasFocus) {\n      this._setNextFocusToSelectedOption();\n    }\n  }\n\n  /** Sets the first selected option as first in the keyboard focus order. */\n  private _setNextFocusToSelectedOption() {\n    // Null check the options since they only get defined after `ngAfterContentInit`.\n    const selected = this.options?.find(option => option.isSelected());\n\n    if (selected) {\n      this.listKeyManager.updateActiveItem(selected);\n    }\n  }\n\n  /** Update the internal value of the listbox based on the selection model. */\n  private _updateInternalValue() {\n    const indexCache = new Map<T, number>();\n    this.selectionModel.sort((a: T, b: T) => {\n      const aIndex = this._getIndexForValue(indexCache, a);\n      const bIndex = this._getIndexForValue(indexCache, b);\n      return aIndex - bIndex;\n    });\n    const selected = this.selectionModel.selected;\n    this._invalid =\n      (!this.multiple && selected.length > 1) || !!this._getInvalidOptionValues(selected).length;\n    this.changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Gets the index of the given value in the given list of options.\n   * @param cache The cache of indices found so far\n   * @param value The value to find\n   * @return The index of the value in the options list\n   */\n  private _getIndexForValue(cache: Map<T, number>, value: T) {\n    const isEqual = this.compareWith || Object.is;\n    if (!cache.has(value)) {\n      let index = -1;\n      for (let i = 0; i < this.options.length; i++) {\n        if (isEqual(value, this.options.get(i)!.value)) {\n          index = i;\n          break;\n        }\n      }\n      cache.set(value, index);\n    }\n    return cache.get(value)!;\n  }\n\n  /**\n   * Handle the user clicking an option.\n   * @param option The option that was clicked.\n   */\n  private _handleOptionClicked(option: CdkOption<T>, event: MouseEvent) {\n    event.preventDefault();\n    this.listKeyManager.setActiveItem(option);\n    if (event.shiftKey && this.multiple) {\n      this.triggerRange(\n        option,\n        this._getLastTriggeredIndex() ?? this.listKeyManager.activeItemIndex!,\n        this.listKeyManager.activeItemIndex!,\n        !option.isSelected(),\n      );\n    } else {\n      this.triggerOption(option);\n    }\n  }\n\n  /** Verifies that no two options represent the same value under the compareWith function. */\n  private _verifyNoOptionValueCollisions() {\n    this.options.changes.pipe(startWith(this.options), takeUntil(this.destroyed)).subscribe(() => {\n      const isEqual = this.compareWith ?? Object.is;\n      for (let i = 0; i < this.options.length; i++) {\n        const option = this.options.get(i)!;\n        let duplicate: CdkOption<T> | null = null;\n        for (let j = i + 1; j < this.options.length; j++) {\n          const other = this.options.get(j)!;\n          if (isEqual(option.value, other.value)) {\n            duplicate = other;\n            break;\n          }\n        }\n        if (duplicate) {\n          // TODO(mmalerba): Link to docs about this.\n          if (this.compareWith) {\n            console.warn(\n              `Found multiple CdkOption representing the same value under the given compareWith function`,\n              {\n                option1: option.element,\n                option2: duplicate.element,\n                compareWith: this.compareWith,\n              },\n            );\n          } else {\n            console.warn(`Found multiple CdkOption with the same value`, {\n              option1: option.element,\n              option2: duplicate.element,\n            });\n          }\n          return;\n        }\n      }\n    });\n  }\n\n  /** Verifies that the option values are valid. */\n  private _verifyOptionValues() {\n    if (this.options && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      const selected = this.selectionModel.selected;\n      const invalidValues = this._getInvalidOptionValues(selected);\n\n      if (!this.multiple && selected.length > 1) {\n        throw Error('Listbox cannot have more than one selected value in multi-selection mode.');\n      }\n\n      if (invalidValues.length) {\n        throw Error('Listbox has selected values that do not match any of its options.');\n      }\n    }\n  }\n\n  /**\n   * Coerces a value into an array representing a listbox selection.\n   * @param value The value to coerce\n   * @return An array\n   */\n  private _coerceValue(value: readonly T[]) {\n    return value == null ? [] : coerceArray(value);\n  }\n\n  /**\n   * Get the sublist of values that do not represent valid option values in this listbox.\n   * @param values The list of values\n   * @return The sublist of values that are not valid option values\n   */\n  private _getInvalidOptionValues(values: readonly T[]) {\n    const isEqual = this.compareWith || Object.is;\n    const validValues = (this.options || []).map(option => option.value);\n    return values.filter(value => !validValues.some(validValue => isEqual(value, validValue)));\n  }\n\n  /** Get the index of the last triggered option. */\n  private _getLastTriggeredIndex() {\n    const index = this.options.toArray().indexOf(this._lastTriggered!);\n    return index === -1 ? null : index;\n  }\n}\n\n/** Change event that is fired whenever the value of the listbox changes. */\nexport interface ListboxValueChangeEvent<T> {\n  /** The new value of the listbox. */\n  readonly value: readonly T[];\n\n  /** Reference to the listbox that emitted the event. */\n  readonly listbox: CdkListbox<T>;\n\n  /** Reference to the option that was triggered. */\n  readonly option: CdkOption<T> | null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkListbox, CdkOption} from './listbox';\n\nconst EXPORTED_DECLARATIONS = [CdkListbox, CdkOption];\n\n@NgModule({\n  imports: [...EXPORTED_DECLARATIONS],\n  exports: [...EXPORTED_DECLARATIONS],\n})\nexport class CdkListboxModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAmDA;;;;;;;AAOG;AACH,MAAM,qBAAyB,SAAQ,cAAiB,CAAA;AAE7C,IAAA,QAAA;IADT,WACS,CAAA,QAAA,GAAW,KAAK,EACvB,uBAA6B,EAC7B,WAAW,GAAG,IAAI,EAClB,WAAuC,EAAA;QAEvC,KAAK,CAAC,IAAI,EAAE,uBAAuB,EAAE,WAAW,EAAE,WAAW,CAAC;QALvD,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAQR,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,QAAQ;;IAGb,MAAM,CAAC,GAAG,MAAW,EAAA;;;AAG5B,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;;aACzB;AACL,YAAA,OAAO,KAAK,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;;;AAGzC;AAED;MAgBa,SAAS,CAAA;;AAEpB,IAAA,IACI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY;;IAEtC,IAAI,EAAE,CAAC,KAAK,EAAA;AACV,QAAA,IAAI,CAAC,GAAG,GAAG,KAAK;;AAEV,IAAA,GAAG;IACH,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC;;AAG5C,IAAA,KAAK;AAEzB;;;AAGG;AAC+B,IAAA,cAAc;;AAGhD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;;IAElD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;AAEnB,IAAA,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;;AAGjC,IAAA,IACI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,KAAK;AACjC,cAAE,IAAI,CAAC,OAAO,CAAC;AACf,cAAE,IAAI,CAAC,gBAAgB,EAAE;;IAE7B,IAAI,eAAe,CAAC,KAAK,EAAA;AACvB,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC;;AAE1B,IAAA,gBAAgB,GAAG,MAAM,CAA4B,SAAS,CAAC;;AAG9D,IAAA,OAAO,GAAgB,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa;;AAG7C,IAAA,OAAO,GAAkB,MAAM,CAAC,UAAU,CAAC;;AAGpD,IAAA,SAAS,GAAG,IAAI,OAAO,EAAQ;;AAGhC,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAc;IAE7C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;;;IAI3B,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;;;IAItC,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;;;IAIpC,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;;;IAI3B,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;;;IAI3B,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;;;IAI7B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;;;IAItB,QAAQ,GAAA;AACN,QAAA,OAAO,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE;;AAGxE;;;AAGG;IACH,eAAe,GAAA;;;AAGb,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;AACpC,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAC,CAAC;;;AAItE;;;AAGG;AACH,IAAA,iBAAiB;;IAGP,YAAY,GAAA;;;;AAIpB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;AACpC,YAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnC,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;;;;IAKd,YAAY,GAAA;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACrD,OAAO,CAAC,CAAC;;AAEX,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;;uGAjIzC,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,6MAsB2B,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,UAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,uBAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAtBpD,SAAS,EAAA,UAAA,EAAA,CAAA;kBAfrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,OAAO,EAAE,YAAY;AACrB,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,sBAAsB,EAAE,cAAc;AACtC,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,2BAA2B,EAAE,YAAY;AACzC,wBAAA,SAAS,EAAE,uBAAuB;AAClC,wBAAA,SAAS,EAAE,gBAAgB;AAC5B,qBAAA;AACF,iBAAA;8BAIK,EAAE,EAAA,CAAA;sBADL;gBAWmB,KAAK,EAAA,CAAA;sBAAxB,KAAK;uBAAC,WAAW;gBAMgB,cAAc,EAAA,CAAA;sBAA/C,KAAK;uBAAC,yBAAyB;gBAI5B,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,mBAAmB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAW5D,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,UAAU;;MA8HN,UAAU,CAAA;AACb,IAAA,kBAAkB;;AAG1B,IAAA,IACI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY;;IAEtC,IAAI,EAAE,CAAC,KAAK,EAAA;AACV,QAAA,IAAI,CAAC,GAAG,GAAG,KAAK;;AAEV,IAAA,GAAG;IACH,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC;;AAGjE,IAAA,IACI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,KAAK,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE;;IAE5E,IAAI,eAAe,CAAC,KAAK,EAAA;AACvB,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC;;AAE1B,IAAA,gBAAgB,GAAG,MAAM,CAA4B,SAAS,CAAC;;AAGvE,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;;IAE1D,IAAI,KAAK,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;AAG3B;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ;;IAErC,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK;AAEpC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,oBAAoB,EAAE;;;;AAK/B,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE;;IAEzB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;AAEnB,IAAA,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;;AAGjC,IAAA,IACI,mBAAmB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,oBAAoB,EAAE;;IAEpC,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC;;AAE9B,IAAA,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAC;;AAG5C,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,KAAgC,EAAA;AAC9C,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,KAAK,YAAY,GAAG,YAAY,GAAG,UAAU;AACtE,QAAA,IAAI,KAAK,KAAK,YAAY,EAAE;AAC1B,YAAA,IAAI,CAAC,cAAc,EAAE,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC;;aACpE;AACL,YAAA,IAAI,CAAC,cAAc,EAAE,uBAAuB,EAAE;;;IAG1C,YAAY,GAA8B,UAAU;;AAG5D,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW;;IAExC,IAAI,WAAW,CAAC,EAA2C,EAAA;AACzD,QAAA,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,EAAE;;AAGtC;;;AAGG;AACH,IAAA,IACI,sBAAsB,GAAA;QACxB,OAAO,IAAI,CAAC,uBAAuB;;IAErC,IAAI,sBAAsB,CAAC,IAAa,EAAA;AACtC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI;QACnC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;;IAEtD,uBAAuB,GAAG,KAAK;;AAGvC,IAAA,IACI,uBAAuB,GAAA;QACzB,OAAO,IAAI,CAAC,wBAAwB;;IAEtC,IAAI,uBAAuB,CAAC,IAAa,EAAA;AACvC,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI;QACpC,IAAI,CAAC,cAAc,EAAE,aAAa,CAChC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CACtF;;IAEK,wBAAwB,GAAG,KAAK;;AAGE,IAAA,WAAW,GAAG,IAAI,OAAO,EAA8B;;AAGtC,IAAA,OAAO;;AAGxD,IAAA,cAAc,GAAG,IAAI,qBAAqB,EAAK;;AAG/C,IAAA,cAAc;;AAGL,IAAA,SAAS,GAAG,IAAI,OAAO,EAAQ;;AAG/B,IAAA,OAAO,GAAgB,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa;;AAGvD,IAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;;AAGvB,IAAA,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;;IAGxD,QAAQ,GAAG,KAAK;;IAGhB,cAAc,GAAwB,IAAI;;AAG1C,IAAA,UAAU,GAAG,MAAK,GAAG;;AAGrB,IAAA,SAAS,GAAkC,MAAK,GAAG;;AAGnD,IAAA,cAAc,GAAG,KAAK,CAAC,MAC5B,IAAI,CAAC,OAAO,CAAC,OAAsC,CAAC,IAAI,CACvD,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EACvB,SAAS,CAAC,OAAO,IACf,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACvF,CACF,CACF;;IAGgB,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG/C,IAAA,UAAU,GAAY,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS;;IAGhD,sBAAsB,GAAG,CAAC,MAAoB,KAAK,MAAM,CAAC,QAAQ;;AAGlE,IAAA,kBAAkB,GAAG,MAAM,KAAK;;IAGzC,SAAS,GAAG,KAAK;;IAGjB,qBAAqB,GAAwB,IAAI;AAEzD,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;YAElC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBAC3D,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAK;AAC5C,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC/E,wBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACjD,wBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;;AAErC,iBAAC,CAAC;AACJ,aAAC,CAAC;;;IAIN,kBAAkB,GAAA;AAChB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,CAAC,8BAA8B,EAAE;YACrC,IAAI,CAAC,mBAAmB,EAAE;;QAG5B,IAAI,CAAC,eAAe,EAAE;;AAGtB,QAAA,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;AACpD,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;aAC/C,SAAS,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAE/C,QAAA,IAAI,CAAC;aACF,IAAI,CACH,MAAM,CAAC,CAAC,EAAC,MAAM,EAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EACtC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AAE1B,aAAA,SAAS,CAAC,CAAC,EAAC,MAAM,EAAE,KAAK,EAAC,KAAK,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;;IAG7E,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,kBAAkB,IAAI;AAC3B,QAAA,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE;AAC9B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;;AAG3B;;;AAGG;AACH,IAAA,MAAM,CAAC,MAAoB,EAAA;AACzB,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGhC;;;AAGG;AACH,IAAA,WAAW,CAAC,KAAQ,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;;AAElC,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGnC;;;AAGG;AACH,IAAA,MAAM,CAAC,MAAoB,EAAA;AACzB,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGhC;;;AAGG;AACH,IAAA,WAAW,CAAC,KAAQ,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;;AAElC,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGnC;;;AAGG;AACH,IAAA,QAAQ,CAAC,MAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGlC;;;AAGG;AACH,IAAA,aAAa,CAAC,KAAQ,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;;AAElC,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC;;AAGrC;;;AAGG;AACH,IAAA,cAAc,CAAC,UAAmB,EAAA;QAChC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;aACtB;AACL,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;;YAElC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;;;AAI3E;;;AAGG;AACH,IAAA,UAAU,CAAC,MAAoB,EAAA;QAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC;;AAG3C;;;AAGG;AACH,IAAA,QAAQ,CAAC,MAAoB,EAAA;QAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,UAAU,KAAK,MAAM,CAAC;;AAGvD;;;AAGG;AACH,IAAA,eAAe,CAAC,KAAQ,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,OAAO,KAAK;;QAEd,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC;;AAG9C;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,EAAiC,EAAA;AAChD,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAmB,EAAA;AAC5B,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,mBAAmB,EAAE;;AAG5B;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;;;IAIvC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;;AAGtB;;;;;AAKG;AACO,IAAA,aAAa,CAAC,MAA2B,EAAA;AACjD,QAAA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,MAAM;AAC5B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC;kBACjB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;kBACvC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;YAC5C,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1B,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,MAAM,EAAE,MAAM;AACf,iBAAA,CAAC;;;;AAKR;;;;;;;AAOG;AACO,IAAA,YAAY,CAAC,OAA4B,EAAE,IAAY,EAAE,EAAU,EAAE,EAAW,EAAA;AACxF,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;YAClD;;AAEF,QAAA,IAAI,CAAC,cAAc,GAAG,OAAO;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;AAC7C,QAAA,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO;AAClC,aAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;aAC5F,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ;aACjC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;QAC9B,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAChC,QAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AACtC,YAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,IACpD,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CACpC;AACD,YAAA,IAAI,EAAE,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AAC9B,gBAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;;iBACrB,IAAI,CAAC,EAAE,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AACtC,gBAAA,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;;;QAGrC,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC;QAC3D,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1B,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,MAAM,EAAE,OAAO;AAChB,aAAA,CAAC;;;AAIN;;;AAGG;AACH,IAAA,gBAAgB,CAAC,MAAoB,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC;;;IAIjC,YAAY,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3C,IAAI,CAAC,6BAA6B,EAAE;;iBAC/B;AACL,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;;YAGzC,IAAI,CAAC,kBAAkB,EAAE;;;;AAKnB,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC3C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;AAGF,QAAA,MAAM,EAAC,OAAO,EAAC,GAAG,KAAK;AACvB,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe;AAC/D,QAAA,MAAM,QAAQ,GAAG,CAAC,SAAS,EAAE,SAAS,CAAU;AAEhD,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,EAAE,GAAG,QAAQ,CAAC,EAAE;;YAExE,IAAI,CAAC,YAAY,CACf,IAAI,EACJ,CAAC,EACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EACvB,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAC1C;YACD,KAAK,CAAC,cAAc,EAAE;YACtB;;QAGF,IACE,IAAI,CAAC,QAAQ;AACb,aAAC,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC;AACxC,YAAA,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,EACjC;AACA,YAAA,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,IAAI,IAAI,EAAE;AACjF,gBAAA,IAAI,CAAC,YAAY,CACf,IAAI,CAAC,cAAc,CAAC,UAAU,EAC9B,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,EACpE,IAAI,CAAC,cAAc,CAAC,eAAe,EACnC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,CAC7C;;YAEH,KAAK,CAAC,cAAc,EAAE;YACtB;;QAGF,IACE,IAAI,CAAC,QAAQ;AACb,YAAA,OAAO,KAAK,IAAI;AAChB,YAAA,cAAc,CAAC,KAAK,EAAE,GAAG,QAAQ,CAAC;AAClC,YAAA,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,EACjC;AACA,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU;YAC9C,IAAI,OAAO,EAAE;AACX,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,eAAgB;AACjD,gBAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE;AACxC,gBAAA,IAAI,CAAC,YAAY,CACf,OAAO,EACP,IAAI,EACJ,IAAI,CAAC,cAAc,CAAC,eAAgB,EACpC,CAAC,OAAO,CAAC,UAAU,EAAE,CACtB;;YAEH,KAAK,CAAC,cAAc,EAAE;YACtB;;QAGF,IACE,IAAI,CAAC,QAAQ;AACb,YAAA,OAAO,KAAK,GAAG;AACf,YAAA,cAAc,CAAC,KAAK,EAAE,GAAG,QAAQ,CAAC;AAClC,YAAA,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,EACjC;AACA,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU;YAC9C,IAAI,OAAO,EAAE;AACX,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,eAAgB;AACjD,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;AACvC,gBAAA,IAAI,CAAC,YAAY,CACf,OAAO,EACP,IAAI,EACJ,IAAI,CAAC,cAAc,CAAC,eAAgB,EACpC,CAAC,OAAO,CAAC,UAAU,EAAE,CACtB;;YAEH,KAAK,CAAC,cAAc,EAAE;YACtB;;QAGF,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE;YAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAClD,KAAK,CAAC,cAAc,EAAE;YACtB;;AAGF,QAAA,MAAM,QAAQ,GACZ,OAAO,KAAK,QAAQ;AACpB,YAAA,OAAO,KAAK,UAAU;AACtB,YAAA,OAAO,KAAK,UAAU;AACtB,YAAA,OAAO,KAAK,WAAW;AACvB,YAAA,OAAO,KAAK,IAAI;YAChB,OAAO,KAAK,GAAG;AACjB,QAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC;;AAEpC,QAAA,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,mBAAmB,KAAK,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;YAC7F,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;;;;IAK5C,cAAc,GAAA;;;AAGtB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB;;;AAGG;AACO,IAAA,eAAe,CAAC,KAAiB,EAAA;;;;QAIzC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU;AAE3D,QAAA,MAAM,YAAY,GAAG,KAAK,CAAC,aAAwB;AACnD,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACzE,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC,6BAA6B,EAAE;;;;IAK9B,wBAAwB,GAAA;AAChC,QAAA,OAAO,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,GAAG,IAAI;;;IAIpE,YAAY,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,CAAC,CAAC;;QAEX,OAAO,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;;;IAIxF,eAAe,GAAA;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,OAAO;AAC9D,aAAA,QAAQ,CAAC,CAAC,IAAI,CAAC,uBAAuB;AACtC,aAAA,aAAa;AACb,aAAA,cAAc;AACd,aAAA,uBAAuB,CAAC,CAAC,UAAU,CAAC;AACpC,aAAA,aAAa,CACZ,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CACtF;AAEH,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;AACnC,YAAA,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;;aACxC;AACL,YAAA,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC;;QAG1E,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE;AACvC,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAC;;AAGpE,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAErE,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAClE,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU;;;AAInD,YAAA,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,YAAY,CAAC,EAAE;gBACzE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;;AAEzC,SAAC,CAAC;;;IAII,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE;;AAEzC,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;;AAGvC;;;AAGG;AACK,IAAA,aAAa,CAAC,KAAmB,EAAA;AACvC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;;AAElC,QAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAE7D,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,6BAA6B,EAAE;;;;IAKhC,6BAA6B,GAAA;;AAEnC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;QAElE,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;;;IAK1C,oBAAoB,GAAA;AAC1B,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAa;QACvC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAI,EAAE,CAAI,KAAI;YACtC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC;YACpD,OAAO,MAAM,GAAG,MAAM;AACxB,SAAC,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;AAC7C,QAAA,IAAI,CAAC,QAAQ;YACX,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,MAAM;AAC5F,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;;AAGvC;;;;;AAKG;IACK,iBAAiB,CAAC,KAAqB,EAAE,KAAQ,EAAA;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;QAC7C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACrB,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,gBAAA,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC,KAAK,CAAC,EAAE;oBAC9C,KAAK,GAAG,CAAC;oBACT;;;AAGJ,YAAA,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;;AAEzB,QAAA,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAE;;AAG1B;;;AAGG;IACK,oBAAoB,CAAC,MAAoB,EAAE,KAAiB,EAAA;QAClE,KAAK,CAAC,cAAc,EAAE;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC;QACzC,IAAI,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,YAAY,CACf,MAAM,EACN,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,eAAgB,EACrE,IAAI,CAAC,cAAc,CAAC,eAAgB,EACpC,CAAC,MAAM,CAAC,UAAU,EAAE,CACrB;;aACI;AACL,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;;;;IAKtB,8BAA8B,GAAA;QACpC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAC3F,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;AAC7C,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAE;gBACnC,IAAI,SAAS,GAAwB,IAAI;AACzC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAChD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAE;oBAClC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;wBACtC,SAAS,GAAG,KAAK;wBACjB;;;gBAGJ,IAAI,SAAS,EAAE;;AAEb,oBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,wBAAA,OAAO,CAAC,IAAI,CACV,CAAA,yFAAA,CAA2F,EAC3F;4BACE,OAAO,EAAE,MAAM,CAAC,OAAO;4BACvB,OAAO,EAAE,SAAS,CAAC,OAAO;4BAC1B,WAAW,EAAE,IAAI,CAAC,WAAW;AAC9B,yBAAA,CACF;;yBACI;AACL,wBAAA,OAAO,CAAC,IAAI,CAAC,CAAA,4CAAA,CAA8C,EAAE;4BAC3D,OAAO,EAAE,MAAM,CAAC,OAAO;4BACvB,OAAO,EAAE,SAAS,CAAC,OAAO;AAC3B,yBAAA,CAAC;;oBAEJ;;;AAGN,SAAC,CAAC;;;IAII,mBAAmB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACnE,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAE5D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACzC,gBAAA,MAAM,KAAK,CAAC,2EAA2E,CAAC;;AAG1F,YAAA,IAAI,aAAa,CAAC,MAAM,EAAE;AACxB,gBAAA,MAAM,KAAK,CAAC,mEAAmE,CAAC;;;;AAKtF;;;;AAIG;AACK,IAAA,YAAY,CAAC,KAAmB,EAAA;AACtC,QAAA,OAAO,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC;;AAGhD;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,MAAoB,EAAA;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;QAC7C,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;QACpE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;;;IAIpF,sBAAsB,GAAA;AAC5B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,cAAe,CAAC;AAClE,QAAA,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK;;uGAtxBzB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAA,EAAA,CAAA,UAAA,EAAA,iBAAA,CAAA,EAAA,KAAA,EAAA,CAAA,iBAAA,EAAA,OAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,EAqC2B,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,EAahB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,CAAA,+BAAA,EAAA,qBAAA,EAUL,gBAAgB,CAqCb,EAAA,WAAA,EAAA,CAAA,uBAAA,EAAA,aAAA,CAAA,EAAA,WAAA,EAAA,CAAA,uBAAA,EAAA,aAAA,CAAA,EAAA,sBAAA,EAAA,CAAA,kCAAA,EAAA,wBAAA,EAAA,gBAAgB,CAWd,EAAA,uBAAA,EAAA,CAAA,oCAAA,EAAA,yBAAA,EAAA,gBAAgB,CApHrE,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,UAAA,EAAA,yBAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,2BAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,uBAAA,EAAA,aAAA,EAAA,EAAA,cAAA,EAAA,aAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,UAAU,CAAC;AACzC,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EA8HgB,SAAS,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA5Hf,UAAU,EAAA,UAAA,EAAA,CAAA;kBAzBtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,MAAM,EAAE,SAAS;AACjB,wBAAA,OAAO,EAAE,aAAa;AACtB,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,8BAA8B,EAAE,4BAA4B;AAC5D,wBAAA,yBAAyB,EAAE,aAAa;AACxC,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,YAAY,EAAE,yBAAyB;AACvC,wBAAA,WAAW,EAAE,kBAAkB;AAChC,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,gBAAgB,CAAC;AACzC,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACF,qBAAA;AACF,iBAAA;wDAMK,EAAE,EAAA,CAAA;sBADL;gBAYG,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,UAAU;gBAWb,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,iBAAiB;gBAapB,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAc7D,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAW7D,mBAAmB,EAAA,CAAA;sBADtB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,+BAA+B,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAWxE,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,uBAAuB;gBAgB1B,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,uBAAuB;gBAa1B,sBAAsB,EAAA,CAAA;sBADzB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kCAAkC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAY3E,uBAAuB,EAAA,CAAA;sBAD1B,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oCAAoC,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAavC,WAAW,EAAA,CAAA;sBAApD,MAAM;uBAAC,uBAAuB;gBAG4B,OAAO,EAAA,CAAA;sBAAjE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;;;ACnXjD,MAAM,qBAAqB,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;MAMxC,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YANE,UAAU,EAAE,SAAS,CAArB,EAAA,OAAA,EAAA,CAAA,UAAU,EAAE,SAAS,CAAA,EAAA,CAAA;wGAMvC,gBAAgB,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,GAAG,qBAAqB,CAAC;AACnC,oBAAA,OAAO,EAAE,CAAC,GAAG,qBAAqB,CAAC;AACpC,iBAAA;;;;;"}