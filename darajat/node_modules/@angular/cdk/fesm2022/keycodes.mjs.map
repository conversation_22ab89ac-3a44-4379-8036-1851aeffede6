{"version": 3, "file": "keycodes.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/keycodes/modifiers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport type ModifierKey = 'altKey' | 'shiftKey' | 'ctrlKey' | 'metaKey';\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nexport function hasModifierKey(event: KeyboardEvent, ...modifiers: ModifierKey[]): boolean {\n  if (modifiers.length) {\n    return modifiers.some(modifier => event[modifier]);\n  }\n\n  return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\n"], "names": [], "mappings": ";;AAUA;;;AAGG;SACa,cAAc,CAAC,KAAoB,EAAE,GAAG,SAAwB,EAAA;AAC9E,IAAA,IAAI,SAAS,CAAC,MAAM,EAAE;AACpB,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;;AAGpD,IAAA,OAAO,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO;AACzE;;;;"}