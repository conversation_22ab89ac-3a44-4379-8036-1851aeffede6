{"version": 3, "file": "portal.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/portal/portal-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/portal/portal.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/portal/dom-portal-outlet.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/portal/portal-directives.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nexport function throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nexport function throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nexport function throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nexport function throwUnknownPortalTypeError() {\n  throw Error(\n    'Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n      'a ComponentPortal or a TemplatePortal.',\n  );\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nexport function throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nexport function throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  TemplateRef,\n  ViewContainerRef,\n  ElementRef,\n  ComponentRef,\n  EmbeddedViewRef,\n  Injector,\n} from '@angular/core';\nimport {\n  throwNullPortalOutletError,\n  throwPortalAlreadyAttachedError,\n  throwNoPortalAttachedError,\n  throwNullPortalError,\n  throwPortalOutletAlreadyDisposedError,\n  throwUnknownPortalTypeError,\n} from './portal-errors';\n\n/** Interface that can be used to generically type a class. */\nexport interface ComponentType<T> {\n  new (...args: any[]): T;\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nexport abstract class Portal<T> {\n  private _attachedHost: PortalOutlet | null;\n\n  /** Attach this portal to a host. */\n  attach(host: PortalOutlet): T {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n\n    this._attachedHost = host;\n    return <T>host.attach(this);\n  }\n\n  /** Detach this portal from its host */\n  detach(): void {\n    let host = this._attachedHost;\n\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n\n  /** Whether this portal is attached to a host. */\n  get isAttached(): boolean {\n    return this._attachedHost != null;\n  }\n\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host: PortalOutlet | null) {\n    this._attachedHost = host;\n  }\n}\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nexport class ComponentPortal<T> extends Portal<ComponentRef<T>> {\n  /** The type of the component that will be instantiated for attachment. */\n  component: ComponentType<T>;\n\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef?: ViewContainerRef | null;\n\n  /** Injector used for the instantiation of the component. */\n  injector?: Injector | null;\n\n  /**\n   * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n   */\n  projectableNodes?: Node[][] | null;\n\n  constructor(\n    component: ComponentType<T>,\n    viewContainerRef?: ViewContainerRef | null,\n    injector?: Injector | null,\n    projectableNodes?: Node[][] | null,\n  ) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.projectableNodes = projectableNodes;\n  }\n}\n\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nexport class TemplatePortal<C = any> extends Portal<EmbeddedViewRef<C>> {\n  constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    public templateRef: TemplateRef<C>,\n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    public viewContainerRef: ViewContainerRef,\n    /** Contextual data to be passed in to the embedded view. */\n    public context?: C,\n    /** The injector to use for the embedded view. */\n    public injector?: Injector,\n  ) {\n    super();\n  }\n\n  get origin(): ElementRef {\n    return this.templateRef.elementRef;\n  }\n\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  override attach(host: PortalOutlet, context: C | undefined = this.context): EmbeddedViewRef<C> {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  override detach(): void {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nexport class DomPortal<T = HTMLElement> extends Portal<T> {\n  /** DOM node hosting the portal's content. */\n  readonly element: T;\n\n  constructor(element: T | ElementRef<T>) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n\n/** A `PortalOutlet` is a space that can contain a single `Portal`. */\nexport interface PortalOutlet {\n  /** Attaches a portal to this outlet. */\n  attach(portal: Portal<any>): any;\n\n  /** Detaches the currently attached portal from this outlet. */\n  detach(): any;\n\n  /** Performs cleanup before the outlet is destroyed. */\n  dispose(): void;\n\n  /** Whether there is currently a portal attached to this outlet. */\n  hasAttached(): boolean;\n}\n\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nexport abstract class BasePortalOutlet implements PortalOutlet {\n  /** The portal currently attached to the host. */\n  protected _attachedPortal: Portal<any> | null;\n\n  /** A function that will permanently dispose this host. */\n  private _disposeFn: (() => void) | null;\n\n  /** Whether this host has already been permanently disposed. */\n  private _isDisposed: boolean = false;\n\n  /** Whether this host has an attached portal. */\n  hasAttached(): boolean {\n    return !!this._attachedPortal;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /** Attaches a portal. */\n  attach(portal: Portal<any>): any {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n\n  abstract attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n\n  abstract attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C>;\n\n  // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n  readonly attachDomPortal: null | ((portal: DomPortal) => any) = null;\n\n  /** Detaches a previously attached portal. */\n  detach(): void {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n\n  /** Permanently dispose of this portal host. */\n  dispose(): void {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n\n  /** @docs-private */\n  setDisposeFn(fn: () => void) {\n    this._disposeFn = fn;\n  }\n\n  private _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ApplicationRef,\n  ComponentRef,\n  EmbeddedViewRef,\n  EnvironmentInjector,\n  Injector,\n  NgModuleRef,\n  createComponent,\n} from '@angular/core';\nimport {BasePortalOutlet, ComponentPortal, DomPortal, TemplatePortal} from './portal';\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nexport class DomPortalOutlet extends BasePortalOutlet {\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   */\n  constructor(\n    /** Element into which the content is projected. */\n    public outletElement: Element,\n    private _appRef?: ApplicationRef,\n    private _defaultInjector?: Injector,\n  ) {\n    super();\n  }\n\n  /**\n   * Attach the given ComponentPortal to DOM element.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    let componentRef: ComponentRef<T>;\n\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      const injector = portal.injector || portal.viewContainerRef.injector;\n      const ngModuleRef = injector.get(NgModuleRef, null, {optional: true}) || undefined;\n\n      componentRef = portal.viewContainerRef.createComponent(portal.component, {\n        index: portal.viewContainerRef.length,\n        injector,\n        ngModuleRef,\n        projectableNodes: portal.projectableNodes || undefined,\n      });\n\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      const appRef = this._appRef!;\n\n      const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n      const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n      componentRef = createComponent(portal.component, {\n        elementInjector,\n        environmentInjector,\n        projectableNodes: portal.projectableNodes || undefined,\n      });\n\n      appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (appRef.viewCount > 0) {\n          appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n\n    return componentRef;\n  }\n\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector,\n    });\n\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n\n    this._attachedPortal = portal;\n\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n\n  /**\n   * Attaches a DOM portal by transferring its content into the outlet.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  override attachDomPortal = (portal: DomPortal) => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this.outletElement.ownerDocument.createComment('dom-portal');\n\n    element.parentNode!.insertBefore(anchorNode, element);\n    this.outletElement.appendChild(element);\n    this._attachedPortal = portal;\n\n    super.setDisposeFn(() => {\n      // We can't use `replaceWith` here because IE doesn't support it.\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n\n  /**\n   * Clears out a portal from the DOM.\n   */\n  override dispose(): void {\n    super.dispose();\n    this.outletElement.remove();\n  }\n\n  /** Gets the root HTMLElement for an instantiated component. */\n  private _getComponentRootNode(componentRef: ComponentRef<any>): HTMLElement {\n    return (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentRef,\n  Directive,\n  EmbeddedViewRef,\n  EventEmitter,\n  NgModule,\n  OnDestroy,\n  OnInit,\n  Output,\n  TemplateRef,\n  ViewContainerRef,\n  Input,\n  inject,\n  NgModuleRef,\n  DOCUMENT,\n} from '@angular/core';\n\nimport {BasePortalOutlet, ComponentPortal, Portal, TemplatePortal, DomPortal} from './portal';\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n@Directive({\n  selector: '[cdkPortal]',\n  exportAs: 'cdkPortal',\n})\nexport class CdkPortal extends TemplatePortal {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const templateRef = inject<TemplateRef<any>>(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n\n    super(templateRef, viewContainerRef);\n  }\n}\n\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\n@Directive({\n  selector: '[cdk-portal], [portal]',\n  exportAs: 'cdkPortal',\n  providers: [\n    {\n      provide: CdkPortal,\n      useExisting: TemplatePortalDirective,\n    },\n  ],\n})\nexport class TemplatePortalDirective extends CdkPortal {}\n\n/**\n * Possible attached references to the CdkPortalOutlet.\n */\nexport type CdkPortalOutletAttachedRef = ComponentRef<any> | EmbeddedViewRef<any> | null;\n\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n@Directive({\n  selector: '[cdkPortalOutlet]',\n  exportAs: 'cdkPortalOutlet',\n})\nexport class CdkPortalOutlet extends BasePortalOutlet implements OnInit, OnDestroy {\n  private _moduleRef = inject(NgModuleRef, {optional: true});\n  private _document = inject(DOCUMENT);\n  private _viewContainerRef = inject(ViewContainerRef);\n\n  /** Whether the portal component is initialized. */\n  private _isInitialized = false;\n\n  /** Reference to the currently-attached component/view ref. */\n  private _attachedRef: CdkPortalOutletAttachedRef;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n  }\n\n  /** Portal associated with the Portal outlet. */\n  @Input('cdkPortalOutlet')\n  get portal(): Portal<any> | null {\n    return this._attachedPortal;\n  }\n\n  set portal(portal: Portal<any> | null | undefined | '') {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n\n    if (this.hasAttached()) {\n      super.detach();\n    }\n\n    if (portal) {\n      super.attach(portal);\n    }\n\n    this._attachedPortal = portal || null;\n  }\n\n  /** Emits when a portal is attached to the outlet. */\n  @Output() readonly attached: EventEmitter<CdkPortalOutletAttachedRef> =\n    new EventEmitter<CdkPortalOutletAttachedRef>();\n\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef(): CdkPortalOutletAttachedRef {\n    return this._attachedRef;\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    portal.setAttachedHost(this);\n\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef =\n      portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n\n    const ref = viewContainerRef.createComponent(portal.component, {\n      index: viewContainerRef.length,\n      injector: portal.injector || viewContainerRef.injector,\n      projectableNodes: portal.projectableNodes || undefined,\n      ngModuleRef: this._moduleRef || undefined,\n    });\n\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild((ref.hostView as EmbeddedViewRef<any>).rootNodes[0]);\n    }\n\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n\n    return ref;\n  }\n\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector,\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n\n    return viewRef;\n  }\n\n  /**\n   * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  override attachDomPortal = (portal: DomPortal) => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n\n    portal.setAttachedHost(this);\n    element.parentNode!.insertBefore(anchorNode, element);\n    this._getRootNode().appendChild(element);\n    this._attachedPortal = portal;\n\n    super.setDisposeFn(() => {\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode!.replaceChild(element, anchorNode);\n      }\n    });\n  };\n\n  /** Gets the root node of the portal outlet. */\n  private _getRootNode(): HTMLElement {\n    const nativeElement: Node = this._viewContainerRef.element.nativeElement;\n\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return (\n      nativeElement.nodeType === nativeElement.ELEMENT_NODE\n        ? nativeElement\n        : nativeElement.parentNode!\n    ) as HTMLElement;\n  }\n}\n\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n@Directive({\n  selector: '[cdkPortalHost], [portalHost]',\n  exportAs: 'cdkPortalHost',\n  inputs: [{name: 'portal', alias: 'cdkPortalHost'}],\n  providers: [\n    {\n      provide: CdkPortalOutlet,\n      useExisting: PortalHostDirective,\n    },\n  ],\n})\nexport class PortalHostDirective extends CdkPortalOutlet {}\n\n@NgModule({\n  imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n  exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n})\nexport class PortalModule {}\n"], "names": [], "mappings": ";;;AAQA;;;AAGG;SACa,oBAAoB,GAAA;AAClC,IAAA,MAAM,KAAK,CAAC,iCAAiC,CAAC;AAChD;AAEA;;;AAGG;SACa,+BAA+B,GAAA;AAC7C,IAAA,MAAM,KAAK,CAAC,oCAAoC,CAAC;AACnD;AAEA;;;AAGG;SACa,qCAAqC,GAAA;AACnD,IAAA,MAAM,KAAK,CAAC,6CAA6C,CAAC;AAC5D;AAEA;;;AAGG;SACa,2BAA2B,GAAA;IACzC,MAAM,KAAK,CACT,+EAA+E;AAC7E,QAAA,wCAAwC,CAC3C;AACH;AAEA;;;AAGG;SACa,0BAA0B,GAAA;AACxC,IAAA,MAAM,KAAK,CAAC,sDAAsD,CAAC;AACrE;AAEA;;;AAGG;SACa,0BAA0B,GAAA;AACxC,IAAA,MAAM,KAAK,CAAC,8DAA8D,CAAC;AAC7E;;AC3BA;;;AAGG;MACmB,MAAM,CAAA;AAClB,IAAA,aAAa;;AAGrB,IAAA,MAAM,CAAC,IAAkB,EAAA;AACvB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,gBAAA,0BAA0B,EAAE;;AAG9B,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,gBAAA,+BAA+B,EAAE;;;AAIrC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,OAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;;;IAI7B,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa;AAE7B,QAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;YACzB,IAAI,CAAC,MAAM,EAAE;;AACR,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACxD,YAAA,0BAA0B,EAAE;;;;AAKhC,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI;;AAGnC;;;AAGG;AACH,IAAA,eAAe,CAAC,IAAyB,EAAA;AACvC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;AAE5B;AAED;;AAEG;AACG,MAAO,eAAmB,SAAQ,MAAuB,CAAA;;AAE7D,IAAA,SAAS;AAET;;;;AAIG;AACH,IAAA,gBAAgB;;AAGhB,IAAA,QAAQ;AAER;;AAEG;AACH,IAAA,gBAAgB;AAEhB,IAAA,WAAA,CACE,SAA2B,EAC3B,gBAA0C,EAC1C,QAA0B,EAC1B,gBAAkC,EAAA;AAElC,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;;AAE3C;AAED;;AAEG;AACG,MAAO,cAAwB,SAAQ,MAA0B,CAAA;AAG5D,IAAA,WAAA;AAEA,IAAA,gBAAA;AAEA,IAAA,OAAA;AAEA,IAAA,QAAA;AART,IAAA,WAAA;;IAES,WAA2B;;IAE3B,gBAAkC;;IAElC,OAAW;;IAEX,QAAmB,EAAA;AAE1B,QAAA,KAAK,EAAE;QARA,IAAW,CAAA,WAAA,GAAX,WAAW;QAEX,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAEhB,IAAO,CAAA,OAAA,GAAP,OAAO;QAEP,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAKjB,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;;AAGpC;;;;AAIG;AACM,IAAA,MAAM,CAAC,IAAkB,EAAE,OAAyB,GAAA,IAAI,CAAC,OAAO,EAAA;AACvE,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;AACtB,QAAA,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;;IAGlB,MAAM,GAAA;AACb,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS;AACxB,QAAA,OAAO,KAAK,CAAC,MAAM,EAAE;;AAExB;AAED;;;;AAIG;AACG,MAAO,SAA2B,SAAQ,MAAS,CAAA;;AAE9C,IAAA,OAAO;AAEhB,IAAA,WAAA,CAAY,OAA0B,EAAA;AACpC,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,YAAY,UAAU,GAAG,OAAO,CAAC,aAAa,GAAG,OAAO;;AAEjF;AAiBD;;;AAGG;MACmB,gBAAgB,CAAA;;AAE1B,IAAA,eAAe;;AAGjB,IAAA,UAAU;;IAGV,WAAW,GAAY,KAAK;;IAGpC,WAAW,GAAA;AACT,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe;;;AAQ/B,IAAA,MAAM,CAAC,MAAmB,EAAA;AACxB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,CAAC,MAAM,EAAE;AACX,gBAAA,oBAAoB,EAAE;;AAGxB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACtB,gBAAA,+BAA+B,EAAE;;AAGnC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,qCAAqC,EAAE;;;AAI3C,QAAA,IAAI,MAAM,YAAY,eAAe,EAAE;AACrC,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;;AACpC,aAAA,IAAI,MAAM,YAAY,cAAc,EAAE;AAC3C,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,YAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;;;aAEnC,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,YAAY,SAAS,EAAE;AAC9D,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;AAGrC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,2BAA2B,EAAE;;;;IASxB,eAAe,GAAwC,IAAI;;IAGpE,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC;AAC1C,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI;;QAG7B,IAAI,CAAC,gBAAgB,EAAE;;;IAIzB,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,MAAM,EAAE;;QAGf,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;AAIzB,IAAA,YAAY,CAAC,EAAc,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;IAGd,gBAAgB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;;AAG3B;;ACjQD;;;AAGG;AACG,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;AAU1C,IAAA,aAAA;AACC,IAAA,OAAA;AACA,IAAA,gBAAA;AAXV;;;;;;AAMG;AACH,IAAA,WAAA;;IAES,aAAsB,EACrB,OAAwB,EACxB,gBAA2B,EAAA;AAEnC,QAAA,KAAK,EAAE;QAJA,IAAa,CAAA,aAAA,GAAb,aAAa;QACZ,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;;AAK1B;;;;AAIG;AACH,IAAA,qBAAqB,CAAI,MAA0B,EAAA;AACjD,QAAA,IAAI,YAA6B;;;;;AAMjC,QAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;YAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ;AACpE,YAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,SAAS;YAElF,YAAY,GAAG,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE;AACvE,gBAAA,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;gBACrC,QAAQ;gBACR,WAAW;AACX,gBAAA,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,SAAS;AACvD,aAAA,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;;aAC1C;AACL,YAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;AACpE,gBAAA,MAAM,KAAK,CAAC,qEAAqE,CAAC;;AAEpF,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAQ;AAE5B,YAAA,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,IAAI;AACjF,YAAA,MAAM,mBAAmB,GAAG,eAAe,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,QAAQ,CAAC;AACrF,YAAA,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC/C,eAAe;gBACf,mBAAmB;AACnB,gBAAA,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,SAAS;AACvD,aAAA,CAAC;AAEF,YAAA,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;AACxC,YAAA,IAAI,CAAC,YAAY,CAAC,MAAK;;;AAGrB,gBAAA,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE;AACxB,oBAAA,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;;gBAE1C,YAAY,CAAC,OAAO,EAAE;AACxB,aAAC,CAAC;;;;AAIJ,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAE7B,QAAA,OAAO,YAAY;;AAGrB;;;;AAIG;AACH,IAAA,oBAAoB,CAAI,MAAyB,EAAA;AAC/C,QAAA,IAAI,aAAa,GAAG,MAAM,CAAC,gBAAgB;AAC3C,QAAA,IAAI,OAAO,GAAG,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE;YACjF,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC1B,SAAA,CAAC;;;;;AAMF,QAAA,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;;;;QAK/E,OAAO,CAAC,aAAa,EAAE;AAEvB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;YACrB,IAAI,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC;AAC1C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,gBAAA,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC;;AAE/B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;;AAG7B,QAAA,OAAO,OAAO;;AAGhB;;;;;AAKG;AACM,IAAA,eAAe,GAAG,CAAC,MAAiB,KAAI;AAC/C,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC1E,YAAA,MAAM,KAAK,CAAC,uDAAuD,CAAC;;;;AAKtE,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC;QAE/E,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC;AACrD,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC;AACvC,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAE7B,QAAA,KAAK,CAAC,YAAY,CAAC,MAAK;;AAEtB,YAAA,IAAI,UAAU,CAAC,UAAU,EAAE;gBACzB,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC;;AAE3D,SAAC,CAAC;AACJ,KAAC;AAED;;AAEG;IACM,OAAO,GAAA;QACd,KAAK,CAAC,OAAO,EAAE;AACf,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;AAIrB,IAAA,qBAAqB,CAAC,YAA+B,EAAA;QAC3D,OAAQ,YAAY,CAAC,QAAiC,CAAC,SAAS,CAAC,CAAC,CAAgB;;AAErF;;AChJD;;;AAGG;AAKG,MAAO,SAAU,SAAQ,cAAc,CAAA;AAG3C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,WAAW,GAAG,MAAM,CAAmB,WAAW,CAAC;AACzD,QAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAEjD,QAAA,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;;uGAP3B,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBAJrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,WAAW;AACtB,iBAAA;;AAYD;;;AAGG;AAWG,MAAO,uBAAwB,SAAQ,SAAS,CAAA;uGAAzC,uBAAuB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAPvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,SAAS;AAClB,gBAAA,WAAW,EAAE,uBAAuB;AACrC,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAVnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,SAAS;AAClB,4BAAA,WAAW,EAAyB,uBAAA;AACrC,yBAAA;AACF,qBAAA;AACF,iBAAA;;AAQD;;;;;;AAMG;AAKG,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;IAC3C,UAAU,GAAG,MAAM,CAAC,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAClD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;;IAG5C,cAAc,GAAG,KAAK;;AAGtB,IAAA,YAAY;AAIpB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;;AAIT,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,eAAe;;IAG7B,IAAI,MAAM,CAAC,MAA2C,EAAA;;;;;AAKpD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACzD;;AAGF,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,KAAK,CAAC,MAAM,EAAE;;QAGhB,IAAI,MAAM,EAAE;AACV,YAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;;AAGtB,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,IAAI;;;AAIpB,IAAA,QAAQ,GACzB,IAAI,YAAY,EAA8B;;AAGhD,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAG1B,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAG5B,WAAW,GAAA;QACT,KAAK,CAAC,OAAO,EAAE;QACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI;;AAGjD;;;;;AAKG;AACH,IAAA,qBAAqB,CAAI,MAA0B,EAAA;AACjD,QAAA,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;;;AAI5B,QAAA,MAAM,gBAAgB,GACpB,MAAM,CAAC,gBAAgB,IAAI,IAAI,GAAG,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;QAEpF,MAAM,GAAG,GAAG,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE;YAC7D,KAAK,EAAE,gBAAgB,CAAC,MAAM;AAC9B,YAAA,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ;AACtD,YAAA,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,SAAS;AACtD,YAAA,WAAW,EAAE,IAAI,CAAC,UAAU,IAAI,SAAS;AAC1C,SAAA,CAAC;;;;AAKF,QAAA,IAAI,gBAAgB,KAAK,IAAI,CAAC,iBAAiB,EAAE;AAC/C,YAAA,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,CAAE,GAAG,CAAC,QAAiC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;QAGtF,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG;AACvB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;AAEvB,QAAA,OAAO,GAAG;;AAGZ;;;;AAIG;AACH,IAAA,oBAAoB,CAAI,MAAyB,EAAA;AAC/C,QAAA,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;AAC5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE;YAC5F,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC1B,SAAA,CAAC;AACF,QAAA,KAAK,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAExD,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAE3B,QAAA,OAAO,OAAO;;AAGhB;;;;;AAKG;AACM,IAAA,eAAe,GAAG,CAAC,MAAiB,KAAI;AAC/C,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC1E,YAAA,MAAM,KAAK,CAAC,uDAAuD,CAAC;;;;QAKtE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC;AAE7D,QAAA,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;QAC5B,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC;QACrD,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC;AACxC,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAE7B,QAAA,KAAK,CAAC,YAAY,CAAC,MAAK;AACtB,YAAA,IAAI,UAAU,CAAC,UAAU,EAAE;gBACzB,UAAU,CAAC,UAAW,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC;;AAE5D,SAAC,CAAC;AACJ,KAAC;;IAGO,YAAY,GAAA;QAClB,MAAM,aAAa,GAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa;;;AAIxE,QAAA,QACE,aAAa,CAAC,QAAQ,KAAK,aAAa,CAAC;AACvC,cAAE;AACF,cAAE,aAAa,CAAC,UAAW;;uGAzJtB,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,iBAAiB;AAC5B,iBAAA;wDAoBK,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,iBAAiB;gBA0BL,QAAQ,EAAA,CAAA;sBAA1B;;AAkHH;;;AAGG;AAYG,MAAO,mBAAoB,SAAQ,eAAe,CAAA;uGAA3C,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EAPnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,WAAW,EAAE,mBAAmB;AACjC,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAX/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAC,CAAC;AAClD,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,eAAe;AACxB,4BAAA,WAAW,EAAqB,mBAAA;AACjC,yBAAA;AACF,qBAAA;AACF,iBAAA;;MAOY,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EA9NZ,OAAA,EAAA,CAAA,SAAS,EA2CT,eAAe,EAlBf,uBAAuB,EA+LvB,mBAAmB,CAAA,EAAA,OAAA,EAAA,CAxNnB,SAAS,EA2CT,eAAe,EAlBf,uBAAuB,EA+LvB,mBAAmB,CAAA,EAAA,CAAA;wGAMnB,YAAY,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;oBACnF,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;AACpF,iBAAA;;;;;"}