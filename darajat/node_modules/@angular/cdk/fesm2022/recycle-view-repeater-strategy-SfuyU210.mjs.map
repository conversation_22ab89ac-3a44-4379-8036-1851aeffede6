{"version": 3, "file": "recycle-view-repeater-strategy-SfuyU210.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/collections/array-data-source.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/collections/view-repeater.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/collections/recycle-view-repeater-strategy.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Observable, isObservable, of as observableOf} from 'rxjs';\nimport {DataSource} from './data-source';\n\n/** DataSource wrapper for a native array. */\nexport class ArrayDataSource<T> extends DataSource<T> {\n  constructor(private _data: readonly T[] | Observable<readonly T[]>) {\n    super();\n  }\n\n  connect(): Observable<readonly T[]> {\n    return isObservable(this._data) ? this._data : observableOf(this._data);\n  }\n\n  disconnect() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  InjectionToken,\n  IterableChangeRecord,\n  IterableChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\n\n/**\n * The context for an embedded view in the repeater's view container.\n *\n * @template T The type for the embedded view's $implicit property.\n */\nexport interface _ViewRepeaterItemContext<T> {\n  $implicit?: T;\n}\n\n/**\n * The arguments needed to construct an embedded view for an item in a view\n * container.\n *\n * @template C The type for the context passed to each embedded view.\n */\nexport interface _ViewRepeaterItemInsertArgs<C> {\n  templateRef: TemplateRef<C>;\n  context?: C;\n  index?: number;\n}\n\n/**\n * A factory that derives the embedded view context for an item in a view\n * container.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport type _ViewRepeaterItemContextFactory<T, R, C extends _ViewRepeaterItemContext<T>> = (\n  record: IterableChangeRecord<R>,\n  adjustedPreviousIndex: number | null,\n  currentIndex: number | null,\n) => _ViewRepeaterItemInsertArgs<C>;\n\n/**\n * Extracts the value of an item from an `IterableChangeRecord`.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n */\nexport type _ViewRepeaterItemValueResolver<T, R> = (record: IterableChangeRecord<R>) => T;\n\n/** Indicates how a view was changed by a `_ViewRepeater`. */\nexport enum _ViewRepeaterOperation {\n  /** The content of an existing view was replaced with another item. */\n  REPLACED,\n  /** A new view was created with `createEmbeddedView`. */\n  INSERTED,\n  /** The position of a view changed, but the content remains the same. */\n  MOVED,\n  /** A view was detached from the view container. */\n  REMOVED,\n}\n\n/**\n * Meta data describing the state of a view after it was updated by a `_ViewRepeater`.\n *\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport interface _ViewRepeaterItemChange<R, C> {\n  /** The view's context after it was changed. */\n  context?: C;\n  /** Indicates how the view was changed. */\n  operation: _ViewRepeaterOperation;\n  /** The view's corresponding change record. */\n  record: IterableChangeRecord<R>;\n}\n\n/**\n * Type for a callback to be executed after a view has changed.\n *\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport type _ViewRepeaterItemChanged<R, C> = (change: _ViewRepeaterItemChange<R, C>) => void;\n\n/**\n * Describes a strategy for rendering items in a `ViewContainerRef`.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport interface _ViewRepeater<T, R, C extends _ViewRepeaterItemContext<T>> {\n  applyChanges(\n    changes: IterableChanges<R>,\n    viewContainerRef: ViewContainerRef,\n    itemContextFactory: _ViewRepeaterItemContextFactory<T, R, C>,\n    itemValueResolver: _ViewRepeaterItemValueResolver<T, R>,\n    itemViewChanged?: _ViewRepeaterItemChanged<R, C>,\n  ): void;\n\n  detach(): void;\n}\n\n/**\n * Injection token for `_ViewRepeater`. This token is for use by Angular Material only.\n * @docs-private\n */\nexport const _VIEW_REPEATER_STRATEGY = new InjectionToken<\n  _ViewRepeater<unknown, unknown, _ViewRepeaterItemContext<unknown>>\n>('_ViewRepeater');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  EmbeddedViewRef,\n  IterableChangeRecord,\n  IterableChanges,\n  ViewContainerRef,\n} from '@angular/core';\nimport {\n  _ViewRepeater,\n  _ViewRepeaterItemChanged,\n  _ViewRepeaterItemContext,\n  _ViewRepeaterItemContextFactory,\n  _ViewRepeaterItemInsertArgs,\n  _ViewRepeaterItemValueResolver,\n  _ViewRepeaterOperation,\n} from './view-repeater';\n\n/**\n * A repeater that caches views when they are removed from a\n * `ViewContainerRef`. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport class _RecycleViewRepeaterStrategy<T, R, C extends _ViewRepeaterItemContext<T>>\n  implements _ViewRepeater<T, R, C>\n{\n  /**\n   * The size of the cache used to store unused views.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n   */\n  viewCacheSize: number = 20;\n\n  /**\n   * View cache that stores embedded view instances that have been previously stamped out,\n   * but don't are not currently rendered. The view repeater will reuse these views rather than\n   * creating brand new ones.\n   *\n   * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n   */\n  private _viewCache: EmbeddedViewRef<C>[] = [];\n\n  /** Apply changes to the DOM. */\n  applyChanges(\n    changes: IterableChanges<R>,\n    viewContainerRef: ViewContainerRef,\n    itemContextFactory: _ViewRepeaterItemContextFactory<T, R, C>,\n    itemValueResolver: _ViewRepeaterItemValueResolver<T, R>,\n    itemViewChanged?: _ViewRepeaterItemChanged<R, C>,\n  ) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation(\n      (\n        record: IterableChangeRecord<R>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        let view: EmbeddedViewRef<C> | undefined;\n        let operation: _ViewRepeaterOperation;\n        if (record.previousIndex == null) {\n          // Item added.\n          const viewArgsFactory = () =>\n            itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n          view = this._insertView(\n            viewArgsFactory,\n            currentIndex!,\n            viewContainerRef,\n            itemValueResolver(record),\n          );\n          operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n        } else if (currentIndex == null) {\n          // Item removed.\n          this._detachAndCacheView(adjustedPreviousIndex!, viewContainerRef);\n          operation = _ViewRepeaterOperation.REMOVED;\n        } else {\n          // Item moved.\n          view = this._moveView(\n            adjustedPreviousIndex!,\n            currentIndex!,\n            viewContainerRef,\n            itemValueResolver(record),\n          );\n          operation = _ViewRepeaterOperation.MOVED;\n        }\n\n        if (itemViewChanged) {\n          itemViewChanged({\n            context: view?.context,\n            operation,\n            record,\n          });\n        }\n      },\n    );\n  }\n\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  private _insertView(\n    viewArgsFactory: () => _ViewRepeaterItemInsertArgs<C>,\n    currentIndex: number,\n    viewContainerRef: ViewContainerRef,\n    value: T,\n  ): EmbeddedViewRef<C> | undefined {\n    const cachedView = this._insertViewFromCache(currentIndex!, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(\n      viewArgs.templateRef,\n      viewArgs.context,\n      viewArgs.index,\n    );\n  }\n\n  /** Detaches the view at the given index and inserts into the view cache. */\n  private _detachAndCacheView(index: number, viewContainerRef: ViewContainerRef) {\n    const detachedView = viewContainerRef.detach(index) as EmbeddedViewRef<C>;\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n\n  /** Moves view at the previous index to the current index. */\n  private _moveView(\n    adjustedPreviousIndex: number,\n    currentIndex: number,\n    viewContainerRef: ViewContainerRef,\n    value: T,\n  ): EmbeddedViewRef<C> {\n    const view = viewContainerRef.get(adjustedPreviousIndex!) as EmbeddedViewRef<C>;\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  private _maybeCacheView(view: EmbeddedViewRef<C>, viewContainerRef: ViewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n\n  /** Inserts a recycled view from the cache at the given index. */\n  private _insertViewFromCache(\n    index: number,\n    viewContainerRef: ViewContainerRef,\n  ): EmbeddedViewRef<C> | null {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\n"], "names": ["observableOf"], "mappings": ";;;;AAWA;AACM,MAAO,eAAmB,SAAQ,UAAa,CAAA;AAC/B,IAAA,KAAA;AAApB,IAAA,WAAA,CAAoB,KAA8C,EAAA;AAChE,QAAA,KAAK,EAAE;QADW,IAAK,CAAA,KAAA,GAAL,KAAK;;IAIzB,OAAO,GAAA;QACL,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,GAAGA,EAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGzE,IAAA,UAAU;AACX;;ACqCD;IACY;AAAZ,CAAA,UAAY,sBAAsB,EAAA;;AAEhC,IAAA,sBAAA,CAAA,sBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;;AAER,IAAA,sBAAA,CAAA,sBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;;AAER,IAAA,sBAAA,CAAA,sBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;;AAEL,IAAA,sBAAA,CAAA,sBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACT,CAAC,EATW,sBAAsB,KAAtB,sBAAsB,GASjC,EAAA,CAAA,CAAA;AA4CD;;;AAGG;MACU,uBAAuB,GAAG,IAAI,cAAc,CAEvD,eAAe;;AC/FjB;;;;;;;;;;AAUG;MACU,4BAA4B,CAAA;AAGvC;;;AAGG;IACH,aAAa,GAAW,EAAE;AAE1B;;;;;;AAMG;IACK,UAAU,GAAyB,EAAE;;IAG7C,YAAY,CACV,OAA2B,EAC3B,gBAAkC,EAClC,kBAA4D,EAC5D,iBAAuD,EACvD,eAAgD,EAAA;;QAGhD,OAAO,CAAC,gBAAgB,CACtB,CACE,MAA+B,EAC/B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAoC;AACxC,YAAA,IAAI,SAAiC;AACrC,YAAA,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;;AAEhC,gBAAA,MAAM,eAAe,GAAG,MACtB,kBAAkB,CAAC,MAAM,EAAE,qBAAqB,EAAE,YAAY,CAAC;AACjE,gBAAA,IAAI,GAAG,IAAI,CAAC,WAAW,CACrB,eAAe,EACf,YAAa,EACb,gBAAgB,EAChB,iBAAiB,CAAC,MAAM,CAAC,CAC1B;AACD,gBAAA,SAAS,GAAG,IAAI,GAAG,sBAAsB,CAAC,QAAQ,GAAG,sBAAsB,CAAC,QAAQ;;AAC/E,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;;AAE/B,gBAAA,IAAI,CAAC,mBAAmB,CAAC,qBAAsB,EAAE,gBAAgB,CAAC;AAClE,gBAAA,SAAS,GAAG,sBAAsB,CAAC,OAAO;;iBACrC;;AAEL,gBAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CACnB,qBAAsB,EACtB,YAAa,EACb,gBAAgB,EAChB,iBAAiB,CAAC,MAAM,CAAC,CAC1B;AACD,gBAAA,SAAS,GAAG,sBAAsB,CAAC,KAAK;;YAG1C,IAAI,eAAe,EAAE;AACnB,gBAAA,eAAe,CAAC;oBACd,OAAO,EAAE,IAAI,EAAE,OAAO;oBACtB,SAAS;oBACT,MAAM;AACP,iBAAA,CAAC;;AAEN,SAAC,CACF;;IAGH,MAAM,GAAA;AACJ,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;YAClC,IAAI,CAAC,OAAO,EAAE;;AAEhB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB;;;AAGG;AACK,IAAA,WAAW,CACjB,eAAqD,EACrD,YAAoB,EACpB,gBAAkC,EAClC,KAAQ,EAAA;QAER,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAa,EAAE,gBAAgB,CAAC;QAC7E,IAAI,UAAU,EAAE;AACd,YAAA,UAAU,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK;AACpC,YAAA,OAAO,SAAS;;AAGlB,QAAA,MAAM,QAAQ,GAAG,eAAe,EAAE;AAClC,QAAA,OAAO,gBAAgB,CAAC,kBAAkB,CACxC,QAAQ,CAAC,WAAW,EACpB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,KAAK,CACf;;;IAIK,mBAAmB,CAAC,KAAa,EAAE,gBAAkC,EAAA;QAC3E,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAuB;AACzE,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,gBAAgB,CAAC;;;AAI9C,IAAA,SAAS,CACf,qBAA6B,EAC7B,YAAoB,EACpB,gBAAkC,EAClC,KAAQ,EAAA;QAER,MAAM,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,qBAAsB,CAAuB;AAC/E,QAAA,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;AACzC,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK;AAC9B,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACK,eAAe,CAAC,IAAwB,EAAE,gBAAkC,EAAA;QAClF,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;AAC/C,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;aACrB;YACL,MAAM,KAAK,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;;;;;AAM5C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,IAAI,CAAC,OAAO,EAAE;;iBACT;AACL,gBAAA,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC;;;;;IAM5B,oBAAoB,CAC1B,KAAa,EACb,gBAAkC,EAAA;QAElC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;QACxC,IAAI,UAAU,EAAE;AACd,YAAA,gBAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC;;QAE5C,OAAO,UAAU,IAAI,IAAI;;AAE5B;;;;"}