{"version": 3, "file": "tree-key-manager-KnCoIkIC.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/key-manager/tree-key-manager.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken, QueryList} from '@angular/core';\nimport {coerceObservable} from '../../coercion/private';\nimport {Observable, Subject, Subscription, isObservable, of as observableOf} from 'rxjs';\nimport {take} from 'rxjs/operators';\nimport {\n  TreeKeyManagerFactory,\n  TreeKeyManagerItem,\n  TreeKeyManagerOptions,\n  TreeKeyManagerStrategy,\n} from './tree-key-manager-strategy';\nimport {Typeahead} from './typeahead';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nexport class TreeKeyManager<T extends TreeKeyManagerItem> implements TreeKeyManagerStrategy<T> {\n  /** The index of the currently active (focused) item. */\n  private _activeItemIndex = -1;\n  /** The currently active (focused) item. */\n  private _activeItem: T | null = null;\n  /** Whether or not we activate the item when it's focused. */\n  private _shouldActivationFollowFocus = false;\n  /**\n   * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n   * Right arrow are switched.\n   */\n  private _horizontalOrientation: 'ltr' | 'rtl' = 'ltr';\n\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager.\n   *\n   * The default value for this doesn't skip any elements in order to keep tree items focusable\n   * when disabled. This aligns with ARIA guidelines:\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n   */\n  private _skipPredicateFn = (_item: T) => false;\n\n  /** Function to determine equivalent items. */\n  private _trackByFn: (item: T) => unknown = (item: T) => item;\n\n  /** Synchronous cache of the items to manage. */\n  private _items: T[] = [];\n\n  private _typeahead?: Typeahead<T>;\n  private _typeaheadSubscription = Subscription.EMPTY;\n\n  private _hasInitialFocused = false;\n\n  private _initializeFocus(): void {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n\n    const activeItem = this._items[activeIndex];\n\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n\n    this._hasInitialFocused = true;\n  }\n\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items: Observable<T[]> | QueryList<T> | T[], config: TreeKeyManagerOptions<T>) {\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe((newItems: QueryList<T>) => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n\n  /** Stream that emits any time the focused item changes. */\n  readonly change = new Subject<T | null>();\n\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event: KeyboardEvent) {\n    const key = event.key;\n\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl'\n          ? this._collapseCurrentItem()\n          : this._expandCurrentItem();\n        break;\n\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl'\n          ? this._expandCurrentItem()\n          : this._collapseCurrentItem();\n        break;\n\n      case 'Home':\n        this._focusFirstItem();\n        break;\n\n      case 'End':\n        this._focusLastItem();\n        break;\n\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  getActiveItemIndex(): number | null {\n    return this._activeItemIndex;\n  }\n\n  /** The currently active item. */\n  getActiveItem(): T | null {\n    return this._activeItem;\n  }\n\n  /** Focus the first available item. */\n  private _focusFirstItem(): void {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n\n  /** Focus the last available item. */\n  private _focusLastItem(): void {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n\n  /** Focus the next available item. */\n  private _focusNextItem(): void {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n\n  /** Focus the previous available item. */\n  private _focusPreviousItem(): void {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n\n  /**\n   * Focus the provided item by index.\n   * @param index The index of the item to focus.\n   * @param options Additional focusing options.\n   */\n  focusItem(index: number, options?: {emitChangeEvent?: boolean}): void;\n  focusItem(item: T, options?: {emitChangeEvent?: boolean}): void;\n  focusItem(itemOrIndex: number | T, options?: {emitChangeEvent?: boolean}): void;\n  focusItem(itemOrIndex: number | T, options: {emitChangeEvent?: boolean} = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n\n    let index =\n      typeof itemOrIndex === 'number'\n        ? itemOrIndex\n        : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n\n    // If we're just setting the same item, don't re-call activate or focus\n    if (\n      this._activeItem !== null &&\n      this._trackByFn(activeItem) === this._trackByFn(this._activeItem)\n    ) {\n      return;\n    }\n\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n\n  private _updateActiveItemIndex(newItems: T[]) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n\n    const newIndex = newItems.findIndex(\n      item => this._trackByFn(item) === this._trackByFn(activeItem),\n    );\n\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n\n  private _setTypeAhead(debounceInterval: number | boolean) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item),\n    });\n\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n\n  private _findNextAvailableItemIndex(startingIndex: number) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n\n  private _findPreviousAvailableItemIndex(startingIndex: number) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  private _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent as T)) {\n        return;\n      }\n      this.focusItem(parent as T);\n    }\n  }\n\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  private _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren())\n        .pipe(take(1))\n        .subscribe(children => {\n          const firstChild = children.find(child => !this._skipPredicateFn(child as T));\n          if (!firstChild) {\n            return;\n          }\n          this.focusItem(firstChild as T);\n        });\n    }\n  }\n\n  private _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean'\n      ? this._activeItem.isExpanded\n      : this._activeItem.isExpanded();\n  }\n\n  private _isItemDisabled(item: TreeKeyManagerItem) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n\n  /** For all items that are the same level as the current item, we expand those items. */\n  private _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = observableOf(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n\n  private _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function TREE_KEY_MANAGER_FACTORY<T extends TreeKeyManagerItem>(): TreeKeyManagerFactory<T> {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n\n/** Injection token that determines the key manager to use. */\nexport const TREE_KEY_MANAGER = new InjectionToken<TreeKeyManagerFactory<any>>('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY,\n});\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY,\n};\n"], "names": ["observableOf"], "mappings": ";;;;;;AAoBA;;;;AAIG;MACU,cAAc,CAAA;;IAEjB,gBAAgB,GAAG,CAAC,CAAC;;IAErB,WAAW,GAAa,IAAI;;IAE5B,4BAA4B,GAAG,KAAK;AAC5C;;;AAGG;IACK,sBAAsB,GAAkB,KAAK;AAErD;;;;;;;AAOG;AACK,IAAA,gBAAgB,GAAG,CAAC,KAAQ,KAAK,KAAK;;AAGtC,IAAA,UAAU,GAAyB,CAAC,IAAO,KAAK,IAAI;;IAGpD,MAAM,GAAQ,EAAE;AAEhB,IAAA,UAAU;AACV,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK;IAE3C,kBAAkB,GAAG,KAAK;IAE1B,gBAAgB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvD;;QAGF,IAAI,WAAW,GAAG,CAAC;AACnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnF,WAAW,GAAG,CAAC;gBACf;;;QAIJ,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;;;AAI3C,QAAA,IAAI,UAAU,CAAC,aAAa,EAAE;AAC5B,YAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;AACnC,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU;AAC7B,YAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,WAAW,CAAC;YACzD,UAAU,CAAC,aAAa,EAAE;;aACrB;;AAEL,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;AAG7B,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;AAGhC;;;;;;;AAOG;IACH,WAAY,CAAA,KAA2C,EAAE,MAAgC,EAAA;;;;AAIvF,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,EAAE;YAC7B,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAsB,KAAI;AACjD,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE;gBAChC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC,gBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC;gBACxC,IAAI,CAAC,gBAAgB,EAAE;AACzB,aAAC,CAAC;;AACG,aAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAC9B,YAAA,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAG;AACzB,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;AACtB,gBAAA,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC;AACnC,gBAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC;gBACrC,IAAI,CAAC,gBAAgB,EAAE;AACzB,aAAC,CAAC;;aACG;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;YACnB,IAAI,CAAC,gBAAgB,EAAE;;AAGzB,QAAA,IAAI,OAAO,MAAM,CAAC,2BAA2B,KAAK,SAAS,EAAE;AAC3D,YAAA,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,2BAA2B;;AAExE,QAAA,IAAI,MAAM,CAAC,qBAAqB,EAAE;AAChC,YAAA,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,qBAAqB;;AAE5D,QAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,aAAa;;AAE9C,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO;;AAElC,QAAA,IAAI,OAAO,MAAM,CAAC,yBAAyB,KAAK,WAAW,EAAE;AAC3D,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,yBAAyB,CAAC;;;;AAK/C,IAAA,MAAM,GAAG,IAAI,OAAO,EAAY;;IAGzC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;AACzC,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;AAGxB;;;AAGG;AACH,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG;QAErB,QAAQ,GAAG;AACT,YAAA,KAAK,KAAK;;gBAER;AAEF,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,cAAc,EAAE;gBACrB;AAEF,YAAA,KAAK,SAAS;gBACZ,IAAI,CAAC,kBAAkB,EAAE;gBACzB;AAEF,YAAA,KAAK,YAAY;gBACf,IAAI,CAAC,sBAAsB,KAAK;AAC9B,sBAAE,IAAI,CAAC,oBAAoB;AAC3B,sBAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7B;AAEF,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,sBAAsB,KAAK;AAC9B,sBAAE,IAAI,CAAC,kBAAkB;AACzB,sBAAE,IAAI,CAAC,oBAAoB,EAAE;gBAC/B;AAEF,YAAA,KAAK,MAAM;gBACT,IAAI,CAAC,eAAe,EAAE;gBACtB;AAEF,YAAA,KAAK,KAAK;gBACR,IAAI,CAAC,cAAc,EAAE;gBACrB;AAEF,YAAA,KAAK,OAAO;AACZ,YAAA,KAAK,GAAG;gBACN,IAAI,CAAC,oBAAoB,EAAE;gBAC3B;AAEF,YAAA;AACE,gBAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;oBACrB,IAAI,CAAC,iCAAiC,EAAE;oBACxC;;AAGF,gBAAA,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC;;;gBAGjC;;;AAIJ,QAAA,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE;QACxB,KAAK,CAAC,cAAc,EAAE;;;IAIxB,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB;;;IAI9B,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,WAAW;;;IAIjB,eAAe,GAAA;QACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAI9C,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;;IAIlE,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;IAIjE,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;AAW7E,IAAA,SAAS,CAAC,WAAuB,EAAE,OAAA,GAAuC,EAAE,EAAA;;AAE1E,QAAA,OAAO,CAAC,eAAe,KAAK,IAAI;AAEhC,QAAA,IAAI,KAAK,GACP,OAAO,WAAW,KAAK;AACrB,cAAE;cACA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC3F,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC5C;;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGrC,QAAA,IACE,IAAI,CAAC,WAAW,KAAK,IAAI;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EACjE;YACA;;AAGF,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW;AAC3C,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI;AACrC,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;AAC7B,QAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,KAAK,CAAC;AAEnD,QAAA,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE;QACzB,kBAAkB,EAAE,OAAO,EAAE;AAE7B,QAAA,IAAI,OAAO,CAAC,eAAe,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;;AAGpC,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACrC,IAAI,CAAC,oBAAoB,EAAE;;;AAIvB,IAAA,sBAAsB,CAAC,QAAa,EAAA;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;QACnC,IAAI,CAAC,UAAU,EAAE;YACf;;QAGF,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CACjC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAC9D;QAED,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACvD,YAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ;AAChC,YAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,QAAQ,CAAC;;;AAIlD,IAAA,aAAa,CAAC,gBAAkC,EAAA;QACtD,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;AAC3C,YAAA,gBAAgB,EAAE,OAAO,gBAAgB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,SAAS;YACrF,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnD,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG;AAC1E,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACtB,SAAC,CAAC;;AAGI,IAAA,2BAA2B,CAAC,aAAqB,EAAA;AACvD,QAAA,KAAK,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3D,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAC1C,gBAAA,OAAO,CAAC;;;AAGZ,QAAA,OAAO,aAAa;;AAGd,IAAA,+BAA+B,CAAC,aAAqB,EAAA;AAC3D,QAAA,KAAK,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC3C,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAC1C,gBAAA,OAAO,CAAC;;;AAGZ,QAAA,OAAO,aAAa;;AAGtB;;AAEG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB;;AAGF,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE;AACjC,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;;aACtB;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAC3C,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAW,CAAC,EAAE;gBACjD;;AAEF,YAAA,IAAI,CAAC,SAAS,CAAC,MAAW,CAAC;;;AAI/B;;AAEG;IACK,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE;AAClC,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;;aACpB;AACL,YAAA,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;AAC5C,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;iBACZ,SAAS,CAAC,QAAQ,IAAG;AACpB,gBAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAU,CAAC,CAAC;gBAC7E,IAAI,CAAC,UAAU,EAAE;oBACf;;AAEF,gBAAA,IAAI,CAAC,SAAS,CAAC,UAAe,CAAC;AACjC,aAAC,CAAC;;;IAIA,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,KAAK;AAC5C,cAAE,IAAI,CAAC,WAAW,CAAC;AACnB,cAAE,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;;AAG3B,IAAA,eAAe,CAAC,IAAwB,EAAA;QAC9C,OAAO,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI;;;IAI7E,iCAAiC,GAAA;AACvC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB;;QAGF,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;AAC3C,QAAA,IAAI,aAAa;QACjB,IAAI,CAAC,MAAM,EAAE;YACX,aAAa,GAAGA,EAAY,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,CAAC;;aAC9E;YACL,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;;AAGxD,QAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC5C,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC,CAAC;;IAGI,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE;;AAE/B;AAED;;;;AAIG;SACa,wBAAwB,GAAA;AACtC,IAAA,OAAO,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC;AAC/D;AAEA;MACa,gBAAgB,GAAG,IAAI,cAAc,CAA6B,kBAAkB,EAAE;AACjG,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,wBAAwB;AAClC,CAAA;AAED;;;;AAIG;AACU,MAAA,iCAAiC,GAAG;AAC/C,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,UAAU,EAAE,wBAAwB;;;;;"}