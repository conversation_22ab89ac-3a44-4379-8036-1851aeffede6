{"version": 3, "file": "bidi.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/bidi/dir.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/bidi/bidi-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentInit,\n  Directive,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  signal,\n} from '@angular/core';\n\nimport {Direction, Directionality, _resolveDirectionality} from './directionality';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n@Directive({\n  selector: '[dir]',\n  providers: [{provide: Directionality, useExisting: Dir}],\n  host: {'[attr.dir]': '_rawDir'},\n  exportAs: 'dir',\n})\nexport class Dir implements Directionality, AfterContentInit, OnDestroy {\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Direction as passed in by the consumer. */\n  _rawDir: string;\n\n  /** Event emitted when the direction changes. */\n  @Output('dirChange') readonly change = new EventEmitter<Direction>();\n\n  /** @docs-private */\n  @Input()\n  get dir(): Direction {\n    return this.valueSignal();\n  }\n  set dir(value: Direction | 'auto') {\n    const previousValue = this.valueSignal();\n\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this.valueSignal.set(_resolveDirectionality(value));\n    this._rawDir = value;\n\n    if (previousValue !== this.valueSignal() && this._isInitialized) {\n      this.change.emit(this.valueSignal());\n    }\n  }\n\n  /** Current layout direction of the element. */\n  get value(): Direction {\n    return this.dir;\n  }\n\n  readonly valueSignal = signal<Direction>('ltr');\n\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {Dir} from './dir';\n\n@NgModule({\n  imports: [Dir],\n  exports: [Dir],\n})\nexport class BidiModule {}\n"], "names": [], "mappings": ";;;;;AAoBA;;;;;AAKG;MAOU,GAAG,CAAA;;IAEN,cAAc,GAAY,KAAK;;AAGvC,IAAA,OAAO;;AAGuB,IAAA,MAAM,GAAG,IAAI,YAAY,EAAa;;AAGpE,IAAA,IACI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;IAE3B,IAAI,GAAG,CAAC,KAAyB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,EAAE;;;;QAKxC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK;QAEpB,IAAI,aAAa,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE;YAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;;;;AAKxC,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,GAAG;;AAGR,IAAA,WAAW,GAAG,MAAM,CAAY,KAAK,CAAC;;IAG/C,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAG5B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;uGA1Cb,GAAG,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAH,GAAG,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,OAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,SAAA,EAAA,EAAA,EAAA,SAAA,EAJH,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,KAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAI7C,GAAG,EAAA,UAAA,EAAA,CAAA;kBANf,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAK,GAAA,EAAC,CAAC;AACxD,oBAAA,IAAI,EAAE,EAAC,YAAY,EAAE,SAAS,EAAC;AAC/B,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;8BAS+B,MAAM,EAAA,CAAA;sBAAnC,MAAM;uBAAC,WAAW;gBAIf,GAAG,EAAA,CAAA;sBADN;;;MC5BU,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAV,UAAU,EAAA,OAAA,EAAA,CAHX,GAAG,CAAA,EAAA,OAAA,EAAA,CACH,GAAG,CAAA,EAAA,CAAA;wGAEF,UAAU,EAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,GAAG,CAAC;oBACd,OAAO,EAAE,CAAC,GAAG,CAAC;AACf,iBAAA;;;;;"}