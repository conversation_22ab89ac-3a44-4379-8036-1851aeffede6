{"version": 3, "file": "data-source-D34wiQZj.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/collections/data-source.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ConnectableObservable, Observable} from 'rxjs';\nimport {CollectionViewer} from './collection-viewer';\n\nexport abstract class DataSource<T> {\n  /**\n   * Connects a collection viewer (such as a data-table) to this data source. Note that\n   * the stream provided will be accessed during change detection and should not directly change\n   * values that are bound in template views.\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   * @returns Observable that emits a new value when the data changes.\n   */\n  abstract connect(collectionViewer: CollectionViewer): Observable<readonly T[]>;\n\n  /**\n   * Disconnects a collection viewer (such as a data-table) from this data source. Can be used\n   * to perform any clean-up or tear-down operations when a view is being destroyed.\n   *\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   */\n  abstract disconnect(collectionViewer: CollectionViewer): void;\n}\n\n/** Checks whether an object is a data source. */\nexport function isDataSource(value: any): value is DataSource<any> {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n  // here, because of some internal apps.\n  return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n"], "names": [], "mappings": ";;MAWsB,UAAU,CAAA;AAmB/B;AAED;AACM,SAAU,YAAY,CAAC,KAAU,EAAA;;;;;AAKrC,IAAA,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,UAAU,IAAI,EAAE,KAAK,YAAY,qBAAqB,CAAC;AAClG;;;;"}