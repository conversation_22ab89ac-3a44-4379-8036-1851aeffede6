export { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-Cewa_Eg3.mjs';
export { A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy, b as _VIEW_REPEATER_STRATEGY, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-SfuyU210.mjs';
export { D as DataSource, i as isDataSource } from './data-source-D34wiQZj.mjs';
export { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-Cvpav0PR.mjs';
export { S as SelectionModel, g as getMultipleValuesInSingleSelectionError } from './selection-model-BCgC8uEN.mjs';
import '@angular/core';
import 'rxjs';
//# sourceMappingURL=collections.mjs.map
