{"version": 3, "file": "unique-selection-dispatcher-Cewa_Eg3.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/collections/unique-selection-dispatcher.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, OnDestroy} from '@angular/core';\n\n// Users of the Dispatcher never need to see this type, but TypeScript requires it to be exported.\nexport type UniqueSelectionDispatcherListener = (id: string, name: string) => void;\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\n@Injectable({providedIn: 'root'})\nexport class UniqueSelectionDispatcher implements OnDestroy {\n  private _listeners: UniqueSelectionDispatcherListener[] = [];\n\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id: string, name: string) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener: UniqueSelectionDispatcherListener): () => void {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter((registered: UniqueSelectionDispatcherListener) => {\n        return listener !== registered;\n      });\n    };\n  }\n\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n}\n"], "names": [], "mappings": ";;;AAaA;;;;;;;;AAQG;MAEU,yBAAyB,CAAA;IAC5B,UAAU,GAAwC,EAAE;AAE5D;;;;AAIG;IACH,MAAM,CAAC,EAAU,EAAE,IAAY,EAAA;AAC7B,QAAA,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;AACpC,YAAA,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC;;;AAItB;;;AAGG;AACH,IAAA,MAAM,CAAC,QAA2C,EAAA;AAChD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9B,QAAA,OAAO,MAAK;AACV,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,UAA6C,KAAI;gBACzF,OAAO,QAAQ,KAAK,UAAU;AAChC,aAAC,CAAC;AACJ,SAAC;;IAGH,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;uGA5BX,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAzB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,cADb,MAAM,EAAA,CAAA;;2FAClB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}