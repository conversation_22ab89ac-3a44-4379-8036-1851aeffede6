{"version": 3, "file": "drag-drop.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/dom/clone-node.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/dom/dom-rect.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/dom/parent-position-tracker.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/dom/root-node.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/dom/styling.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/dom/transition-duration.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/preview-ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drag-ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drag-utils.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/sorting/single-axis-sort-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/sorting/mixed-sort-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drop-list-ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drag-drop-registry.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drag-drop.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drag-parent.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/assertions.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/drag-handle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/drag.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/drop-list-group.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/drop-list.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/drag-preview.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/directives/drag-placeholder.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/drag-drop/drag-drop-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Creates a deep clone of an element. */\nexport function deepCloneNode(node: HTMLElement): HTMLElement {\n  const clone = node.cloneNode(true) as HTMLElement;\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n\n  if (nodeName === 'canvas') {\n    transferCanvasData(node as HTMLCanvasElement, clone as HTMLCanvasElement);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node as HTMLInputElement, clone as HTMLInputElement);\n  }\n\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData<T extends Element>(\n  selector: string,\n  node: HTMLElement,\n  clone: HTMLElement,\n  callback: (source: T, clone: T) => void,\n) {\n  const descendantElements = node.querySelectorAll<T>(selector);\n\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll<T>(selector);\n\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n\n/** Transfers the data of one input element to another. */\nfunction transferInputData(\n  source: Element & {value: string},\n  clone: Element & {value: string; name: string; type: string},\n) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source: HTMLCanvasElement, clone: HTMLCanvasElement) {\n  const context = clone.getContext('2d');\n\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nexport function getMutableClientRect(element: Element): DOMRect {\n  const rect = element.getBoundingClientRect();\n\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `DOMRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    x: rect.x,\n    y: rect.y,\n  } as DOMRect;\n}\n\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nexport function isInsideClientRect(clientRect: DOMRect, x: number, y: number) {\n  const {top, bottom, left, right} = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n\n/**\n * Checks if the child element is overflowing from its parent.\n * @param parentRect - The bounding rect of the parent element.\n * @param childRect - The bounding rect of the child element.\n */\nexport function isOverflowingParent(parentRect: DOMRect, childRect: DOMRect): boolean {\n  // check for horizontal overflow (left and right)\n  const isLeftOverflowing = childRect.left < parentRect.left;\n  const isRightOverflowing = childRect.left + childRect.width > parentRect.right;\n\n  // check for vertical overflow (top and bottom)\n  const isTopOverflowing = childRect.top < parentRect.top;\n  const isBottomOverflowing = childRect.top + childRect.height > parentRect.bottom;\n\n  return isLeftOverflowing || isRightOverflowing || isTopOverflowing || isBottomOverflowing;\n}\n\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nexport function adjustDomRect(\n  domRect: {\n    top: number;\n    bottom: number;\n    left: number;\n    right: number;\n    width: number;\n    height: number;\n  },\n  top: number,\n  left: number,\n) {\n  domRect.top += top;\n  domRect.bottom = domRect.top + domRect.height;\n\n  domRect.left += left;\n  domRect.right = domRect.left + domRect.width;\n}\n\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nexport function isPointerNearDomRect(\n  rect: DOMRect,\n  threshold: number,\n  pointerX: number,\n  pointerY: number,\n): boolean {\n  const {top, right, bottom, left, width, height} = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n\n  return (\n    pointerY > top - yThreshold &&\n    pointerY < bottom + yThreshold &&\n    pointerX > left - xThreshold &&\n    pointerX < right + xThreshold\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_getEventTarget} from '../../platform';\nimport {getMutableClientRect, adjustDomRect} from './dom-rect';\n\n/** Object holding the scroll position of something. */\ninterface ScrollPosition {\n  top: number;\n  left: number;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nexport class ParentPositionTracker {\n  /** Cached positions of the scrollable parent elements. */\n  readonly positions = new Map<\n    Document | HTMLElement,\n    {\n      scrollPosition: ScrollPosition;\n      clientRect?: DOMRect;\n    }\n  >();\n\n  constructor(private _document: Document) {}\n\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements: readonly HTMLElement[]) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition(),\n    });\n\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {top: element.scrollTop, left: element.scrollLeft},\n        clientRect: getMutableClientRect(element),\n      });\n    });\n  }\n\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event: Event): ScrollPosition | null {\n    const target = _getEventTarget<HTMLElement | Document>(event)!;\n    const cachedPosition = this.positions.get(target);\n\n    if (!cachedPosition) {\n      return null;\n    }\n\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop: number;\n    let newLeft: number;\n\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = (target as HTMLElement).scrollTop;\n      newLeft = (target as HTMLElement).scrollLeft;\n    }\n\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustDomRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n\n    return {top: topDifference, left: leftDifference};\n  }\n\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {top: window.scrollY, left: window.scrollX};\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EmbeddedViewRef} from '@angular/core';\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nexport function getRootNode(viewRef: EmbeddedViewRef<any>, _document: Document): HTMLElement {\n  const rootNodes: Node[] = viewRef.rootNodes;\n\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0] as HTMLElement;\n  }\n\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Extended CSSStyleDeclaration that includes a couple of drag-related\n * properties that aren't in the built-in TS typings.\n */\nexport interface DragCSSStyleDeclaration extends CSSStyleDeclaration {\n  msScrollSnapType: string;\n  scrollSnapType: string;\n  webkitTapHighlightColor: string;\n}\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nexport function extendStyles(\n  dest: CSSStyleDeclaration,\n  source: Record<string, string>,\n  importantProperties?: Set<string>,\n) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n\n  return dest;\n}\n\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nexport function toggleNativeDragInteractions(element: HTMLElement, enable: boolean) {\n  const userSelect = enable ? '' : 'none';\n\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect,\n  });\n}\n\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nexport function toggleVisibility(\n  element: HTMLElement,\n  enable: boolean,\n  importantProperties?: Set<string>,\n) {\n  extendStyles(\n    element.style,\n    {\n      position: enable ? '' : 'fixed',\n      top: enable ? '' : '0',\n      opacity: enable ? '' : '0',\n      left: enable ? '' : '-999em',\n    },\n    importantProperties,\n  );\n}\n\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nexport function combineTransforms(transform: string, initialTransform?: string): string {\n  return initialTransform && initialTransform != 'none'\n    ? transform + ' ' + initialTransform\n    : transform;\n}\n\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nexport function matchElementSize(target: HTMLElement, sourceRect: DOMRect): void {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nexport function getTransform(x: number, y: number): string {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value: string): number {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nexport function getTransformTransitionDurationInMs(element: HTMLElement): number {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n\n  return (\n    parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +\n    parseCssTimeUnitsToMs(rawDelays[propertyIndex])\n  );\n}\n\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle: CSSStyleDeclaration, name: string): string[] {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EmbeddedViewRef, Renderer2, TemplateRef, ViewContainerRef} from '@angular/core';\nimport {Direction} from '../bidi';\nimport {\n  extendStyles,\n  getTransform,\n  matchElementSize,\n  toggleNativeDragInteractions,\n} from './dom/styling';\nimport {deepCloneNode} from './dom/clone-node';\nimport {getRootNode} from './dom/root-node';\nimport {getTransformTransitionDurationInMs} from './dom/transition-duration';\n\n/** Template that can be used to create a drag preview element. */\nexport interface DragPreviewTemplate<T = any> {\n  matchSize?: boolean;\n  template: TemplateRef<T> | null;\n  viewContainer: ViewContainerRef;\n  context: T;\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n  // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n  'position',\n]);\n\nexport class PreviewRef {\n  /** Reference to the view of the preview element. */\n  private _previewEmbeddedView: EmbeddedViewRef<any> | null;\n\n  /** Reference to the preview element. */\n  private _preview: HTMLElement;\n\n  get element(): HTMLElement {\n    return this._preview;\n  }\n\n  constructor(\n    private _document: Document,\n    private _rootElement: HTMLElement,\n    private _direction: Direction,\n    private _initialDomRect: DOMRect,\n    private _previewTemplate: DragPreviewTemplate | null,\n    private _previewClass: string | string[] | null,\n    private _pickupPositionOnPage: {\n      x: number;\n      y: number;\n    },\n    private _initialTransform: string | null,\n    private _zIndex: number,\n    private _renderer: Renderer2,\n  ) {}\n\n  attach(parent: HTMLElement): void {\n    this._preview = this._createPreview();\n    parent.appendChild(this._preview);\n\n    // The null check is necessary for browsers that don't support the popover API.\n    // Note that we use a string access for compatibility with Closure.\n    if (supportsPopover(this._preview)) {\n      this._preview['showPopover']();\n    }\n  }\n\n  destroy(): void {\n    this._preview.remove();\n    this._previewEmbeddedView?.destroy();\n    this._preview = this._previewEmbeddedView = null!;\n  }\n\n  setTransform(value: string): void {\n    this._preview.style.transform = value;\n  }\n\n  getBoundingClientRect(): DOMRect {\n    return this._preview.getBoundingClientRect();\n  }\n\n  addClass(className: string): void {\n    this._preview.classList.add(className);\n  }\n\n  getTransitionDuration(): number {\n    return getTransformTransitionDurationInMs(this._preview);\n  }\n\n  addEventListener(name: string, handler: (event: any) => void): () => void {\n    return this._renderer.listen(this._preview, name, handler);\n  }\n\n  private _createPreview(): HTMLElement {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this._previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview: HTMLElement;\n\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(\n        previewTemplate,\n        previewConfig.context,\n      );\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewEmbeddedView = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect!);\n      } else {\n        preview.style.transform = getTransform(\n          this._pickupPositionOnPage.x,\n          this._pickupPositionOnPage.y,\n        );\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialDomRect!);\n\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n\n    extendStyles(\n      preview.style,\n      {\n        // It's important that we disable the pointer events on the preview, because\n        // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n        'pointer-events': 'none',\n        // If the preview has a margin, it can throw off our positioning so we reset it. The reset\n        // value for `margin-right` needs to be `auto` when opened as a popover, because our\n        // positioning is always top/left based, but native popover seems to position itself\n        // to the top/right if `<html>` or `<body>` have `dir=\"rtl\"` (see #29604). Setting it\n        // to `auto` pushed it to the top/left corner in RTL and is a noop in LTR.\n        'margin': supportsPopover(preview) ? '0 auto 0 0' : '0',\n        'position': 'fixed',\n        'top': '0',\n        'left': '0',\n        'z-index': this._zIndex + '',\n      },\n      importantProperties,\n    );\n\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('popover', 'manual');\n    preview.setAttribute('dir', this._direction);\n\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n\n    return preview;\n  }\n}\n\n/** Checks whether a specific element supports the popover API. */\nfunction supportsPopover(element: HTMLElement): boolean {\n  return 'showPopover' in element;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader} from '../a11y';\nimport {Direction} from '../bidi';\nimport {coerceElement} from '../coercion';\nimport {_getEventTarget, _getShadowRoot} from '../platform';\nimport {ViewportRuler} from '../scrolling';\nimport {\n  ElementRef,\n  EmbeddedViewRef,\n  NgZone,\n  Renderer2,\n  TemplateRef,\n  ViewContainerRef,\n  signal,\n} from '@angular/core';\nimport {Observable, Subject, Subscription} from 'rxjs';\nimport {deepCloneNode} from './dom/clone-node';\nimport {adjustDomRect, getMutableClientRect, isOverflowingParent} from './dom/dom-rect';\nimport {ParentPositionTracker} from './dom/parent-position-tracker';\nimport {getRootNode} from './dom/root-node';\nimport {\n  DragCSSStyleDeclaration,\n  combineTransforms,\n  getTransform,\n  toggleNativeDragInteractions,\n  toggleVisibility,\n} from './dom/styling';\nimport {DragDropRegistry} from './drag-drop-registry';\nimport type {DropListRef} from './drop-list-ref';\nimport {DragPreviewTemplate, PreviewRef} from './preview-ref';\n\n/** Object that can be used to configure the behavior of DragRef. */\nexport interface DragRefConfig {\n  /**\n   * Minimum amount of pixels that the user should\n   * drag, before the CDK initiates a drag sequence.\n   */\n  dragStartThreshold: number;\n\n  /**\n   * Amount the pixels the user should drag before the CDK\n   * considers them to have changed the drag direction.\n   */\n  pointerDirectionChangeThreshold: number;\n\n  /** `z-index` for the absolutely-positioned elements that are created by the drag item. */\n  zIndex?: number;\n\n  /** Ref that the current drag item is nested in. */\n  parentDragRef?: DragRef;\n}\n\n/** Function that can be used to constrain the position of a dragged element. */\nexport type DragConstrainPosition = (\n  userPointerPosition: Point,\n  dragRef: DragRef,\n  dimensions: DOMRect,\n  pickupPositionInElement: Point,\n) => Point;\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = {passive: true};\n\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = {passive: false};\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = {\n  passive: false,\n  capture: true,\n};\n\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n\n/** Class applied to the drag placeholder. */\nconst PLACEHOLDER_CLASS = 'cdk-drag-placeholder';\n\n// TODO(crisbeto): add an API for moving a draggable up/down the\n// list programmatically. Useful for keyboard controls.\n\n/** Template that can be used to create a drag helper element (e.g. a preview or a placeholder). */\ninterface DragHelperTemplate<T = any> {\n  template: TemplateRef<T> | null;\n  viewContainer: ViewContainerRef;\n  context: T;\n}\n\n/** Point on the page or within an element. */\nexport interface Point {\n  x: number;\n  y: number;\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n  // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n  'position',\n]);\n\n/**\n * Possible places into which the preview of a drag item can be inserted.\n * - `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\n * you don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\n * its inherited styles.\n * - `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\n * inherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\n * visible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\n * like `:nth-child` and some flexbox configurations.\n * - `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\n * Same advantages and disadvantages as `parent`.\n */\nexport type PreviewContainer = 'global' | 'parent' | ElementRef<HTMLElement> | HTMLElement;\n\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nexport class DragRef<T = any> {\n  private _rootElementCleanups: (() => void)[] | undefined;\n  private _cleanupShadowRootSelectStart: (() => void) | undefined;\n\n  /** Element displayed next to the user's pointer while the element is dragged. */\n  private _preview: PreviewRef | null;\n\n  /** Container into which to insert the preview. */\n  private _previewContainer: PreviewContainer | undefined;\n\n  /** Reference to the view of the placeholder element. */\n  private _placeholderRef: EmbeddedViewRef<any> | null;\n\n  /** Element that is rendered instead of the draggable item while it is being sorted. */\n  private _placeholder: HTMLElement;\n\n  /** Coordinates within the element at which the user picked up the element. */\n  private _pickupPositionInElement: Point;\n\n  /** Coordinates on the page at which the user picked up the element. */\n  private _pickupPositionOnPage: Point;\n\n  /**\n   * Marker node used to save the place in the DOM where the element was\n   * picked up so that it can be restored at the end of the drag sequence.\n   */\n  private _marker: Comment;\n\n  /**\n   * Element indicating the position from which the item was picked up initially.\n   */\n  private _anchor: HTMLElement | null = null;\n\n  /**\n   * CSS `transform` applied to the element when it isn't being dragged. We need a\n   * passive transform in order for the dragged element to retain its new position\n   * after the user has stopped dragging and because we need to know the relative\n   * position in case they start dragging again. This corresponds to `element.style.transform`.\n   */\n  private _passiveTransform: Point = {x: 0, y: 0};\n\n  /** CSS `transform` that is applied to the element while it's being dragged. */\n  private _activeTransform: Point = {x: 0, y: 0};\n\n  /** Inline `transform` value that the element had before the first dragging sequence. */\n  private _initialTransform?: string;\n\n  /**\n   * Whether the dragging sequence has been started. Doesn't\n   * necessarily mean that the element has been moved.\n   */\n  private _hasStartedDragging = signal(false);\n\n  /** Whether the element has moved since the user started dragging it. */\n  private _hasMoved: boolean;\n\n  /** Drop container in which the DragRef resided when dragging began. */\n  private _initialContainer: DropListRef;\n\n  /** Index at which the item started in its initial container. */\n  private _initialIndex: number;\n\n  /** Cached positions of scrollable parent elements. */\n  private _parentPositions: ParentPositionTracker;\n\n  /** Emits when the item is being moved. */\n  private readonly _moveEvents = new Subject<{\n    source: DragRef;\n    pointerPosition: {x: number; y: number};\n    event: MouseEvent | TouchEvent;\n    distance: Point;\n    delta: {x: -1 | 0 | 1; y: -1 | 0 | 1};\n  }>();\n\n  /** Keeps track of the direction in which the user is dragging along each axis. */\n  private _pointerDirectionDelta: {x: -1 | 0 | 1; y: -1 | 0 | 1};\n\n  /** Pointer position at which the last change in the delta occurred. */\n  private _pointerPositionAtLastDirectionChange: Point;\n\n  /** Position of the pointer at the last pointer event. */\n  private _lastKnownPointerPosition: Point;\n\n  /**\n   * Root DOM node of the drag instance. This is the element that will\n   * be moved around as the user is dragging.\n   */\n  private _rootElement: HTMLElement;\n\n  /**\n   * Nearest ancestor SVG, relative to which coordinates are calculated if dragging SVGElement\n   */\n  private _ownerSVGElement: SVGSVGElement | null;\n\n  /**\n   * Inline style value of `-webkit-tap-highlight-color` at the time the\n   * dragging was started. Used to restore the value once we're done dragging.\n   */\n  private _rootElementTapHighlight: string;\n\n  /** Subscription to pointer movement events. */\n  private _pointerMoveSubscription = Subscription.EMPTY;\n\n  /** Subscription to the event that is dispatched when the user lifts their pointer. */\n  private _pointerUpSubscription = Subscription.EMPTY;\n\n  /** Subscription to the viewport being scrolled. */\n  private _scrollSubscription = Subscription.EMPTY;\n\n  /** Subscription to the viewport being resized. */\n  private _resizeSubscription = Subscription.EMPTY;\n\n  /**\n   * Time at which the last touch event occurred. Used to avoid firing the same\n   * events multiple times on touch devices where the browser will fire a fake\n   * mouse event for each touch event, after a certain time.\n   */\n  private _lastTouchEventTime: number;\n\n  /** Time at which the last dragging sequence was started. */\n  private _dragStartTime: number;\n\n  /** Cached reference to the boundary element. */\n  private _boundaryElement: HTMLElement | null = null;\n\n  /** Whether the native dragging interactions have been enabled on the root element. */\n  private _nativeInteractionsEnabled = true;\n\n  /** Client rect of the root element when the dragging sequence has started. */\n  private _initialDomRect?: DOMRect;\n\n  /** Cached dimensions of the preview element. Should be read via `_getPreviewRect`. */\n  private _previewRect?: DOMRect;\n\n  /** Cached dimensions of the boundary element. */\n  private _boundaryRect?: DOMRect;\n\n  /** Element that will be used as a template to create the draggable item's preview. */\n  private _previewTemplate?: DragPreviewTemplate | null;\n\n  /** Template for placeholder element rendered to show where a draggable would be dropped. */\n  private _placeholderTemplate?: DragHelperTemplate | null;\n\n  /** Elements that can be used to drag the draggable item. */\n  private _handles: HTMLElement[] = [];\n\n  /** Registered handles that are currently disabled. */\n  private _disabledHandles = new Set<HTMLElement>();\n\n  /** Droppable container that the draggable is a part of. */\n  private _dropContainer?: DropListRef;\n\n  /** Layout direction of the item. */\n  private _direction: Direction = 'ltr';\n\n  /** Ref that the current drag item is nested in. */\n  private _parentDragRef: DragRef<unknown> | null;\n\n  /**\n   * Cached shadow root that the element is placed in. `null` means that the element isn't in\n   * the shadow DOM and `undefined` means that it hasn't been resolved yet. Should be read via\n   * `_getShadowRoot`, not directly.\n   */\n  private _cachedShadowRoot: ShadowRoot | null | undefined;\n\n  /** Axis along which dragging is locked. */\n  lockAxis: 'x' | 'y';\n\n  /**\n   * Amount of milliseconds to wait after the user has put their\n   * pointer down before starting to drag the element.\n   */\n  dragStartDelay: number | {touch: number; mouse: number} = 0;\n\n  /** Class to be added to the preview element. */\n  previewClass: string | string[] | undefined;\n\n  /**\n   * If the parent of the dragged element has a `scale` transform, it can throw off the\n   * positioning when the user starts dragging. Use this input to notify the CDK of the scale.\n   */\n  scale: number = 1;\n\n  /** Whether starting to drag this element is disabled. */\n  get disabled(): boolean {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value: boolean) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n    }\n  }\n  private _disabled = false;\n\n  /** Emits as the drag sequence is being prepared. */\n  readonly beforeStarted = new Subject<void>();\n\n  /** Emits when the user starts dragging the item. */\n  readonly started = new Subject<{source: DragRef; event: MouseEvent | TouchEvent}>();\n\n  /** Emits when the user has released a drag item, before any animations have started. */\n  readonly released = new Subject<{source: DragRef; event: MouseEvent | TouchEvent}>();\n\n  /** Emits when the user stops dragging an item in the container. */\n  readonly ended = new Subject<{\n    source: DragRef;\n    distance: Point;\n    dropPoint: Point;\n    event: MouseEvent | TouchEvent;\n  }>();\n\n  /** Emits when the user has moved the item into a new container. */\n  readonly entered = new Subject<{container: DropListRef; item: DragRef; currentIndex: number}>();\n\n  /** Emits when the user removes the item its container by dragging it into another container. */\n  readonly exited = new Subject<{container: DropListRef; item: DragRef}>();\n\n  /** Emits when the user drops the item inside a container. */\n  readonly dropped = new Subject<{\n    previousIndex: number;\n    currentIndex: number;\n    item: DragRef;\n    container: DropListRef;\n    previousContainer: DropListRef;\n    distance: Point;\n    dropPoint: Point;\n    isPointerOverContainer: boolean;\n    event: MouseEvent | TouchEvent;\n  }>();\n\n  /**\n   * Emits as the user is dragging the item. Use with caution,\n   * because this event will fire for every pixel that the user has dragged.\n   */\n  readonly moved: Observable<{\n    source: DragRef;\n    pointerPosition: {x: number; y: number};\n    event: MouseEvent | TouchEvent;\n    distance: Point;\n    delta: {x: -1 | 0 | 1; y: -1 | 0 | 1};\n  }> = this._moveEvents;\n\n  /** Arbitrary data that can be attached to the drag item. */\n  data: T;\n\n  /**\n   * Function that can be used to customize the logic of how the position of the drag item\n   * is limited while it's being dragged. Gets called with a point containing the current position\n   * of the user's pointer on the page, a reference to the item being dragged and its dimensions.\n   * Should return a point describing where the item should be rendered.\n   */\n  constrainPosition?: DragConstrainPosition;\n\n  constructor(\n    element: ElementRef<HTMLElement> | HTMLElement,\n    private _config: DragRefConfig,\n    private _document: Document,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n    private _dragDropRegistry: DragDropRegistry,\n    private _renderer: Renderer2,\n  ) {\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement(): HTMLElement {\n    return this._placeholder;\n  }\n\n  /** Returns the root draggable element. */\n  getRootElement(): HTMLElement {\n    return this._rootElement;\n  }\n\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement(): HTMLElement {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles: (HTMLElement | ElementRef<HTMLElement>)[]): this {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set<HTMLElement>();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template: DragPreviewTemplate | null): this {\n    this._previewTemplate = template;\n    return this;\n  }\n\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template: DragHelperTemplate | null): this {\n    this._placeholderTemplate = template;\n    return this;\n  }\n\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement: ElementRef<HTMLElement> | HTMLElement): this {\n    const element = coerceElement(rootElement);\n\n    if (element !== this._rootElement) {\n      this._removeRootElementListeners();\n      const renderer = this._renderer;\n      this._rootElementCleanups = this._ngZone.runOutsideAngular(() => [\n        renderer.listen(element, 'mousedown', this._pointerDown, activeEventListenerOptions),\n        renderer.listen(element, 'touchstart', this._pointerDown, passiveEventListenerOptions),\n        renderer.listen(element, 'dragstart', this._nativeDragStart, activeEventListenerOptions),\n      ]);\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n\n    return this;\n  }\n\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement: ElementRef<HTMLElement> | HTMLElement | null): this {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler\n        .change(10)\n        .subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent: DragRef<unknown> | null): this {\n    this._parentDragRef = parent;\n    return this;\n  }\n\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners();\n\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n\n    this._marker?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeListeners();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement =\n      this._rootElement =\n      this._ownerSVGElement =\n      this._placeholderTemplate =\n      this._previewTemplate =\n      this._marker =\n      this._parentDragRef =\n        null!;\n  }\n\n  /** Checks whether the element is currently being dragged. */\n  isDragging(): boolean {\n    return this._hasStartedDragging() && this._dragDropRegistry.isDragging(this);\n  }\n\n  /** Resets a standalone drag item to its initial position. */\n  reset(): void {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {x: 0, y: 0};\n    this._passiveTransform = {x: 0, y: 0};\n  }\n\n  /** Resets drag item to end of boundary element. */\n  resetToBoundary(): void {\n    if (\n      // can be null if the drag item was never dragged.\n      this._boundaryElement &&\n      this._rootElement &&\n      // check if we are overflowing off our boundary element\n      isOverflowingParent(\n        this._boundaryElement.getBoundingClientRect(),\n        this._rootElement.getBoundingClientRect(),\n      )\n    ) {\n      const parentRect = this._boundaryElement.getBoundingClientRect();\n      const childRect = this._rootElement.getBoundingClientRect();\n\n      let offsetX = 0;\n      let offsetY = 0;\n\n      // check if we are overflowing from left or right\n      if (childRect.left < parentRect.left) {\n        offsetX = parentRect.left - childRect.left;\n      } else if (childRect.right > parentRect.right) {\n        offsetX = parentRect.right - childRect.right;\n      }\n\n      // check if we are overflowing from top or bottom\n      if (childRect.top < parentRect.top) {\n        offsetY = parentRect.top - childRect.top;\n      } else if (childRect.bottom > parentRect.bottom) {\n        offsetY = parentRect.bottom - childRect.bottom;\n      }\n\n      const currentLeft = this._activeTransform.x;\n      const currentTop = this._activeTransform.y;\n\n      let x = currentLeft + offsetX,\n        y = currentTop + offsetY;\n\n      this._rootElement.style.transform = getTransform(x, y);\n      this._activeTransform = {x, y};\n      this._passiveTransform = {x, y};\n    }\n  }\n\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle: HTMLElement) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle: HTMLElement) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction: Direction): this {\n    this._direction = direction;\n    return this;\n  }\n\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container: DropListRef) {\n    this._dropContainer = container;\n  }\n\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition(): Readonly<Point> {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {x: position.x, y: position.y};\n  }\n\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value: Point): this {\n    this._activeTransform = {x: 0, y: 0};\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n\n    return this;\n  }\n\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value: PreviewContainer): this {\n    this._previewContainer = value;\n    return this;\n  }\n\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n\n  /** Unsubscribes from the global subscriptions. */\n  private _removeListeners() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n    this._cleanupShadowRootSelectStart?.();\n    this._cleanupShadowRootSelectStart = undefined;\n  }\n\n  /** Destroys the preview element and its ViewRef. */\n  private _destroyPreview() {\n    this._preview?.destroy();\n    this._preview = null;\n  }\n\n  /** Destroys the placeholder element and its ViewRef. */\n  private _destroyPlaceholder() {\n    this._anchor?.remove();\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._anchor = this._placeholderRef = null!;\n  }\n\n  /** Handler for the `mousedown`/`touchstart` events. */\n  private _pointerDown = (event: MouseEvent | TouchEvent) => {\n    this.beforeStarted.next();\n\n    // Delegate the event based on whether it started from a handle or the element itself.\n    if (this._handles.length) {\n      const targetHandle = this._getTargetHandle(event);\n\n      if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n        this._initializeDragSequence(targetHandle, event);\n      }\n    } else if (!this.disabled) {\n      this._initializeDragSequence(this._rootElement, event);\n    }\n  };\n\n  /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n  private _pointerMove = (event: MouseEvent | TouchEvent) => {\n    const pointerPosition = this._getPointerPositionOnPage(event);\n\n    if (!this._hasStartedDragging()) {\n      const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n      const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n      const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n\n      // Only start dragging after the user has moved more than the minimum distance in either\n      // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n      // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n      // per pixel of movement (e.g. if the user moves their pointer quickly).\n      if (isOverThreshold) {\n        const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n        const container = this._dropContainer;\n\n        if (!isDelayElapsed) {\n          this._endDragSequence(event);\n          return;\n        }\n\n        // Prevent other drag sequences from starting while something in the container is still\n        // being dragged. This can happen while we're waiting for the drop animation to finish\n        // and can cause errors, because some elements might still be moving around.\n        if (!container || (!container.isDragging() && !container.isReceiving())) {\n          // Prevent the default action as soon as the dragging sequence is considered as\n          // \"started\" since waiting for the next event can allow the device to begin scrolling.\n          if (event.cancelable) {\n            event.preventDefault();\n          }\n          this._hasStartedDragging.set(true);\n          this._ngZone.run(() => this._startDragSequence(event));\n        }\n      }\n\n      return;\n    }\n\n    // We prevent the default action down here so that we know that dragging has started. This is\n    // important for touch devices where doing this too early can unnecessarily block scrolling,\n    // if there's a dragging delay.\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n    this._hasMoved = true;\n    this._lastKnownPointerPosition = pointerPosition;\n    this._updatePointerDirectionDelta(constrainedPointerPosition);\n\n    if (this._dropContainer) {\n      this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n    } else {\n      // If there's a position constraint function, we want the element's top/left to be at the\n      // specific position on the page. Use the initial position as a reference if that's the case.\n      const offset = this.constrainPosition ? this._initialDomRect! : this._pickupPositionOnPage;\n      const activeTransform = this._activeTransform;\n      activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n      activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n      this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n    }\n\n    // Since this event gets fired for every pixel while dragging, we only\n    // want to fire it if the consumer opted into it. Also we have to\n    // re-enter the zone because we run all of the events on the outside.\n    if (this._moveEvents.observers.length) {\n      this._ngZone.run(() => {\n        this._moveEvents.next({\n          source: this,\n          pointerPosition: constrainedPointerPosition,\n          event,\n          distance: this._getDragDistance(constrainedPointerPosition),\n          delta: this._pointerDirectionDelta,\n        });\n      });\n    }\n  };\n\n  /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n  private _pointerUp = (event: MouseEvent | TouchEvent) => {\n    this._endDragSequence(event);\n  };\n\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  private _endDragSequence(event: MouseEvent | TouchEvent) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n\n    this._removeListeners();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n\n    if (this._handles) {\n      (this._rootElement.style as DragCSSStyleDeclaration).webkitTapHighlightColor =\n        this._rootElementTapHighlight;\n    }\n\n    if (!this._hasStartedDragging()) {\n      return;\n    }\n\n    this.released.next({source: this, event});\n\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event,\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n\n  /** Starts the dragging sequence. */\n  private _startDragSequence(event: MouseEvent | TouchEvent) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n\n    this._toggleNativeDragInteractions();\n\n    // Needs to happen before the root element is moved.\n    const shadowRoot = this._getShadowRoot();\n    const dropContainer = this._dropContainer;\n\n    if (shadowRoot) {\n      // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n      // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupShadowRootSelectStart = this._renderer.listen(\n          shadowRoot,\n          'selectstart',\n          shadowDomSelectStart,\n          activeCapturingEventOptions,\n        );\n      });\n    }\n\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode as HTMLElement;\n      const placeholder = (this._placeholder = this._createPlaceholderElement());\n      const marker = (this._marker =\n        this._marker ||\n        this._document.createComment(\n          typeof ngDevMode === 'undefined' || ngDevMode ? 'cdk-drag-marker' : '',\n        ));\n\n      // Insert a marker node so that we can restore the element's position in the DOM.\n      parent.insertBefore(marker, element);\n\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = new PreviewRef(\n        this._document,\n        this._rootElement,\n        this._direction,\n        this._initialDomRect!,\n        this._previewTemplate || null,\n        this.previewClass || null,\n        this._pickupPositionOnPage,\n        this._initialTransform,\n        this._config.zIndex || 1000,\n        this._renderer,\n      );\n      this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this.started.next({source: this, event}); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({source: this, event});\n      this._initialContainer = this._initialIndex = undefined!;\n    }\n\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  private _initializeDragSequence(referenceElement: HTMLElement, event: MouseEvent | TouchEvent) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && (event as MouseEvent).button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent =\n      !isTouchSequence &&\n      this._lastTouchEventTime &&\n      this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence\n      ? isFakeTouchstartFromScreenReader(event as TouchEvent)\n      : isFakeMousedownFromScreenReader(event as MouseEvent);\n\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && (target as HTMLElement).draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style as DragCSSStyleDeclaration;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n\n    this._hasMoved = false;\n    this._hasStartedDragging.set(this._hasMoved);\n\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeListeners();\n    this._initialDomRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry\n      .scrolled(this._getShadowRoot())\n      .subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement =\n      previewTemplate && previewTemplate.template && !previewTemplate.matchSize\n        ? {x: 0, y: 0}\n        : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n    const pointerPosition =\n      (this._pickupPositionOnPage =\n      this._lastKnownPointerPosition =\n        this._getPointerPositionOnPage(event));\n    this._pointerDirectionDelta = {x: 0, y: 0};\n    this._pointerPositionAtLastDirectionChange = {x: pointerPosition.x, y: pointerPosition.y};\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  private _cleanupDragArtifacts(event: MouseEvent | TouchEvent) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._marker.parentNode!.replaceChild(this._rootElement, this._marker);\n\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialDomRect =\n      this._boundaryRect =\n      this._previewRect =\n      this._initialTransform =\n        undefined;\n\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer!;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(\n        pointerPosition.x,\n        pointerPosition.y,\n      );\n\n      this.ended.next({source: this, distance, dropPoint: pointerPosition, event});\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event,\n      });\n      container.drop(\n        this,\n        currentIndex,\n        this._initialIndex,\n        this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        pointerPosition,\n        event,\n      );\n      this._dropContainer = this._initialContainer;\n    });\n  }\n\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  private _updateActiveDropContainer({x, y}: Point, {x: rawX, y: rawY}: Point) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (\n      !newContainer &&\n      this._dropContainer !== this._initialContainer &&\n      this._initialContainer._isOverContainer(x, y)\n    ) {\n      newContainer = this._initialContainer;\n    }\n\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        const exitIndex = this._dropContainer!.getItemIndex(this);\n        const nextItemElement =\n          this._dropContainer!.getItemAtIndex(exitIndex + 1)?.getVisibleElement() || null;\n\n        // Notify the old container that the item has left.\n        this.exited.next({item: this, container: this._dropContainer!});\n        this._dropContainer!.exit(this);\n        this._conditionallyInsertAnchor(newContainer, this._dropContainer!, nextItemElement);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer!;\n        this._dropContainer.enter(\n          this,\n          x,\n          y,\n          // If we're re-entering the initial container and sorting is disabled,\n          // put item the into its starting index to begin with.\n          newContainer === this._initialContainer && newContainer.sortingDisabled\n            ? this._initialIndex\n            : undefined,\n        );\n        this.entered.next({\n          item: this,\n          container: newContainer!,\n          currentIndex: newContainer!.getItemIndex(this),\n        });\n      });\n    }\n\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer!._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer!._sortItem(this, x, y, this._pointerDirectionDelta);\n\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(\n          x - this._pickupPositionInElement.x,\n          y - this._pickupPositionInElement.y,\n        );\n      }\n    }\n  }\n\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  private _animatePreviewToPlaceholder(): Promise<void> {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n\n    // Apply the class that adds a transition to the preview.\n    this._preview!.addClass('cdk-drag-animating');\n\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = this._preview!.getTransitionDuration();\n\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = (event: TransitionEvent) => {\n          if (\n            !event ||\n            (this._preview &&\n              _getEventTarget(event) === this._preview.element &&\n              event.propertyName === 'transform')\n          ) {\n            cleanupListener();\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler as Function, duration * 1.5);\n        const cleanupListener = this._preview!.addEventListener('transitionend', handler);\n      });\n    });\n  }\n\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  private _createPlaceholderElement(): HTMLElement {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder: HTMLElement;\n\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig!.viewContainer.createEmbeddedView(\n        placeholderTemplate,\n        placeholderConfig!.context,\n      );\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add(PLACEHOLDER_CLASS);\n    return placeholder;\n  }\n\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  private _getPointerPositionInElement(\n    elementRect: DOMRect,\n    referenceElement: HTMLElement,\n    event: MouseEvent | TouchEvent,\n  ): Point {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y,\n    };\n  }\n\n  /** Determines the point of the page that was touched by the user. */\n  private _getPointerPositionOnPage(event: MouseEvent | TouchEvent): Point {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event)\n      ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n        // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n        // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n        // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n        // throwing an error. The value returned here will be incorrect, but since this only\n        // breaks inside a developer tool and the value is only used for secondary information,\n        // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n        event.touches[0] || event.changedTouches[0] || {pageX: 0, pageY: 0}\n      : event;\n\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n\n    return {x, y};\n  }\n\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  private _getConstrainedPointerPosition(point: Point): Point {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {x, y} = this.constrainPosition\n      ? this.constrainPosition(point, this, this._initialDomRect!, this._pickupPositionInElement)\n      : point;\n\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y =\n        this._pickupPositionOnPage.y -\n        (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x =\n        this._pickupPositionOnPage.x -\n        (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {x: pickupX, y: pickupY} = !this.constrainPosition\n        ? this._pickupPositionInElement\n        : {x: 0, y: 0};\n\n      const boundaryRect = this._boundaryRect;\n      const {width: previewWidth, height: previewHeight} = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n\n      x = clamp(x, minX, maxX);\n      y = clamp(y, minY, maxY);\n    }\n\n    return {x, y};\n  }\n\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  private _updatePointerDirectionDelta(pointerPositionOnPage: Point) {\n    const {x, y} = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n\n    return delta;\n  }\n\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  private _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n\n  /** Removes the manually-added event listeners from the root element. */\n  private _removeRootElementListeners() {\n    this._rootElementCleanups?.forEach(cleanup => cleanup());\n    this._rootElementCleanups = undefined;\n  }\n\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  private _applyRootElementTransform(x: number, y: number) {\n    const scale = 1 / this.scale;\n    const transform = getTransform(x * scale, y * scale);\n    const styles = this._rootElement.style;\n\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform =\n        styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  private _applyPreviewTransform(x: number, y: number) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview!.setTransform(combineTransforms(transform, initialTransform));\n  }\n\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  private _getDragDistance(currentPosition: Point): Point {\n    const pickupPosition = this._pickupPositionOnPage;\n\n    if (pickupPosition) {\n      return {x: currentPosition.x - pickupPosition.x, y: currentPosition.y - pickupPosition.y};\n    }\n\n    return {x: 0, y: 0};\n  }\n\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  private _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  private _containInsideBoundaryOnResize() {\n    let {x, y} = this._passiveTransform;\n\n    if ((x === 0 && y === 0) || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (\n      (boundaryRect.width === 0 && boundaryRect.height === 0) ||\n      (elementRect.width === 0 && elementRect.height === 0)\n    ) {\n      return;\n    }\n\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({y, x});\n    }\n  }\n\n  /** Gets the drag start delay, based on the event type. */\n  private _getDragStartDelay(event: MouseEvent | TouchEvent): number {\n    const value = this.dragStartDelay;\n\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n\n    return value ? value.mouse : 0;\n  }\n\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  private _updateOnScroll(event: Event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n\n    if (scrollDifference) {\n      const target = _getEventTarget<HTMLElement | Document>(event)!;\n\n      // DOMRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary DOMRect if the user has scrolled.\n      if (\n        this._boundaryRect &&\n        target !== this._boundaryElement &&\n        target.contains(this._boundaryElement)\n      ) {\n        adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n\n  /** Gets the scroll position of the viewport. */\n  private _getViewportScrollPosition() {\n    return (\n      this._parentPositions.positions.get(this._document)?.scrollPosition ||\n      this._parentPositions.getViewportScrollPosition()\n    );\n  }\n\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  private _getShadowRoot(): ShadowRoot | null {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n\n    return this._cachedShadowRoot;\n  }\n\n  /** Gets the element into which the drag preview should be inserted. */\n  private _getPreviewInsertionPoint(\n    initialParent: HTMLElement,\n    shadowRoot: ShadowRoot | null,\n  ): HTMLElement {\n    const previewContainer = this._previewContainer || 'global';\n\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return (\n        shadowRoot ||\n        documentRef.fullscreenElement ||\n        (documentRef as any).webkitFullscreenElement ||\n        (documentRef as any).mozFullScreenElement ||\n        (documentRef as any).msFullscreenElement ||\n        documentRef.body\n      );\n    }\n\n    return coerceElement(previewContainer);\n  }\n\n  /** Lazily resolves and returns the dimensions of the preview. */\n  private _getPreviewRect(): DOMRect {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || (!this._previewRect.width && !this._previewRect.height)) {\n      this._previewRect = this._preview\n        ? this._preview!.getBoundingClientRect()\n        : this._initialDomRect!;\n    }\n\n    return this._previewRect;\n  }\n\n  /** Handles a native `dragstart` event. */\n  private _nativeDragStart = (event: DragEvent) => {\n    if (this._handles.length) {\n      const targetHandle = this._getTargetHandle(event);\n\n      if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n        event.preventDefault();\n      }\n    } else if (!this.disabled) {\n      // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n      // but some cases like dragging of links can slip through (see #24403).\n      event.preventDefault();\n    }\n  };\n\n  /** Gets a handle that is the target of an event. */\n  private _getTargetHandle(event: Event): HTMLElement | undefined {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target as Node));\n    });\n  }\n\n  /** Inserts the anchor element, if it's valid. */\n  private _conditionallyInsertAnchor(\n    newContainer: DropListRef,\n    exitContainer: DropListRef,\n    nextItemElement: HTMLElement | null,\n  ) {\n    // Remove the anchor when returning to the initial container.\n    if (newContainer === this._initialContainer) {\n      this._anchor?.remove();\n      this._anchor = null;\n    } else if (exitContainer === this._initialContainer && exitContainer.hasAnchor) {\n      // Insert the anchor when leaving the initial container.\n      const anchor = (this._anchor ??= deepCloneNode(this._placeholder));\n      anchor.classList.remove(PLACEHOLDER_CLASS);\n      anchor.classList.add('cdk-drag-anchor');\n\n      // Clear the transform since the single-axis strategy uses transforms to sort the items.\n      anchor.style.transform = '';\n\n      // When the item leaves the initial container, the container's DOM will be restored to\n      // its original state, except for the dragged item which is removed. Insert the anchor in\n      // the position from which the item left so that the list looks consistent.\n      if (nextItemElement) {\n        nextItemElement.before(anchor);\n      } else {\n        coerceElement(exitContainer.element).appendChild(anchor);\n      }\n    }\n  }\n}\n\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event: MouseEvent | TouchEvent): event is TouchEvent {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event: Event) {\n  event.preventDefault();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nexport function moveItemInArray<T = any>(array: T[], fromIndex: number, toIndex: number): void {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n\n  if (from === to) {\n    return;\n  }\n\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n\n  array[to] = target;\n}\n\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nexport function transferArrayItem<T = any>(\n  currentArray: T[],\n  targetArray: T[],\n  currentIndex: number,\n  targetIndex: number,\n): void {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nexport function copyArrayItem<T = any>(\n  currentArray: T[],\n  targetArray: T[],\n  currentIndex: number,\n  targetIndex: number,\n): void {\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value: number, max: number): number {\n  return Math.max(0, Math.min(max, value));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Direction} from '../../bidi';\nimport {DragDropRegistry} from '../drag-drop-registry';\nimport {moveItemInArray} from '../drag-utils';\nimport {combineTransforms} from '../dom/styling';\nimport {adjustDomRect, getMutableClientRect, isInsideClientRect} from '../dom/dom-rect';\nimport {DropListSortStrategy, SortPredicate} from './drop-list-sort-strategy';\nimport type {DragRef} from '../drag-ref';\n\n/**\n * Entry in the position cache for draggable items.\n * @docs-private\n */\ninterface CachedItemPosition<T> {\n  /** Instance of the drag item. */\n  drag: T;\n  /** Dimensions of the item. */\n  clientRect: DOMRect;\n  /** Amount by which the item has been moved since dragging started. */\n  offset: number;\n  /** Inline transform that the drag item had when dragging started. */\n  initialTransform: string;\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nexport class SingleAxisSortStrategy implements DropListSortStrategy {\n  /** Root element container of the drop list. */\n  private _element: HTMLElement;\n\n  /** Function used to determine if an item can be sorted into a specific index. */\n  private _sortPredicate: SortPredicate<DragRef>;\n\n  /** Cache of the dimensions of all the items inside the container. */\n  private _itemPositions: CachedItemPosition<DragRef>[] = [];\n\n  /**\n   * Draggable items that are currently active inside the container. Includes the items\n   * that were there at the start of the sequence, as well as any items that have been dragged\n   * in, but haven't been dropped yet.\n   */\n  private _activeDraggables: DragRef[];\n\n  /** Direction in which the list is oriented. */\n  orientation: 'vertical' | 'horizontal' = 'vertical';\n\n  /** Layout direction of the drop list. */\n  direction: Direction;\n\n  constructor(private _dragDropRegistry: DragDropRegistry) {}\n\n  /**\n   * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n   * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n   * overlap with the swapped item after the swapping occurred.\n   */\n  private _previousSwap = {\n    drag: null as DragRef | null,\n    delta: 0,\n    overlaps: false,\n  };\n\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items: readonly DragRef[]) {\n    this.withItems(items);\n  }\n\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item: DragRef, pointerX: number, pointerY: number, pointerDelta: {x: number; y: number}) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem\n        ? item.getPlaceholderElement()\n        : sibling.drag.getRootElement();\n\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n\n      const transformAmount = Math.round(sibling.offset * (1 / sibling.drag.scale));\n\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(\n          `translate3d(${transformAmount}px, 0, 0)`,\n          sibling.initialTransform,\n        );\n        adjustDomRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(\n          `translate3d(0, ${transformAmount}px, 0)`,\n          sibling.initialTransform,\n        );\n        adjustDomRect(sibling.clientRect, offset, 0);\n      }\n    });\n\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n\n    return {previousIndex: currentIndex, currentIndex: newIndex};\n  }\n\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item: DragRef, pointerX: number, pointerY: number, index?: number): void {\n    const newIndex =\n      index == null || index < 0\n        ? // We use the coordinates of where the item entered the drop\n          // zone to figure out at which index it should be inserted.\n          this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n        : index;\n\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference: DragRef | undefined = activeDraggables[newIndex];\n\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (\n      !newPositionReference &&\n      (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) &&\n      this._shouldEnterAsFirstChild(pointerX, pointerY)\n    ) {\n      newPositionReference = activeDraggables[0];\n    }\n\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement!.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      this._element.appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n\n  /** Sets the items that are currently part of the list. */\n  withItems(items: readonly DragRef[]): void {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate: SortPredicate<DragRef>): void {\n    this._sortPredicate = predicate;\n  }\n\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables?.forEach(item => {\n      const rootElement = item.getRootElement();\n\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot(): readonly DragRef[] {\n    return this._activeDraggables;\n  }\n\n  /** Gets the index of a specific item. */\n  getItemIndex(item: DragRef): number {\n    return this._getVisualItemPositions().findIndex(currentItem => currentItem.drag === item);\n  }\n\n  /** Gets the item at a specific index. */\n  getItemAtIndex(index: number): DragRef | null {\n    return this._getVisualItemPositions()[index]?.drag || null;\n  }\n\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference: number, leftDifference: number) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({clientRect}) => {\n      adjustDomRect(clientRect, topDifference, leftDifference);\n    });\n\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({drag}) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n\n  withElementContainer(container: HTMLElement): void {\n    this._element = container;\n  }\n\n  /** Refreshes the position cache of the items and sibling containers. */\n  private _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n\n    this._itemPositions = this._activeDraggables\n      .map(drag => {\n        const elementToMeasure = drag.getVisibleElement();\n        return {\n          drag,\n          offset: 0,\n          initialTransform: elementToMeasure.style.transform || '',\n          clientRect: getMutableClientRect(elementToMeasure),\n        };\n      })\n      .sort((a, b) => {\n        return isHorizontal\n          ? a.clientRect.left - b.clientRect.left\n          : a.clientRect.top - b.clientRect.top;\n      });\n  }\n\n  private _getVisualItemPositions() {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    return this.orientation === 'horizontal' && this.direction === 'rtl'\n      ? this._itemPositions.slice().reverse()\n      : this._itemPositions;\n  }\n\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  private _getItemOffsetPx(currentPosition: DOMRect, newPosition: DOMRect, delta: 1 | -1) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal\n      ? newPosition.left - currentPosition.left\n      : newPosition.top - currentPosition.top;\n\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal\n        ? newPosition.width - currentPosition.width\n        : newPosition.height - currentPosition.height;\n    }\n\n    return itemOffset;\n  }\n\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  private _getSiblingOffsetPx(\n    currentIndex: number,\n    siblings: CachedItemPosition<DragRef>[],\n    delta: 1 | -1,\n  ) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n\n    return siblingOffset;\n  }\n\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  private _shouldEnterAsFirstChild(pointerX: number, pointerY: number) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  private _getItemIndexFromPointerPosition(\n    item: DragRef,\n    pointerX: number,\n    pointerY: number,\n    delta?: {x: number; y: number},\n  ): number {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({drag, clientRect}) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (\n          drag === this._previousSwap.drag &&\n          this._previousSwap.overlaps &&\n          direction === this._previousSwap.delta\n        ) {\n          return false;\n        }\n      }\n\n      return isHorizontal\n        ? // Round these down since most browsers report client rects with\n          // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n          pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right)\n        : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_getShadowRoot} from '../../platform';\nimport {moveItemInArray} from '../drag-utils';\nimport {DropListSortStrategy, SortPredicate} from './drop-list-sort-strategy';\nimport {DragDropRegistry} from '../drag-drop-registry';\nimport type {DragRef} from '../drag-ref';\n\n/**\n * Strategy that only supports sorting on a list that might wrap.\n * Items are reordered by moving their DOM nodes around.\n * @docs-private\n */\nexport class MixedSortStrategy implements DropListSortStrategy {\n  /** Root element container of the drop list. */\n  private _element: HTMLElement;\n\n  /** Function used to determine if an item can be sorted into a specific index. */\n  private _sortPredicate: SortPredicate<DragRef>;\n\n  /** Lazily-resolved root node containing the list. Use `_getRootNode` to read this. */\n  private _rootNode: DocumentOrShadowRoot | undefined;\n\n  /**\n   * Draggable items that are currently active inside the container. Includes the items\n   * that were there at the start of the sequence, as well as any items that have been dragged\n   * in, but haven't been dropped yet.\n   */\n  private _activeItems: DragRef[];\n\n  /**\n   * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n   * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n   * overlap with the swapped item after the swapping occurred.\n   */\n  private _previousSwap = {\n    drag: null as DragRef | null,\n    deltaX: 0,\n    deltaY: 0,\n    overlaps: false,\n  };\n\n  /**\n   * Keeps track of the relationship between a node and its next sibling. This information\n   * is used to restore the DOM to the order it was in before dragging started.\n   */\n  private _relatedNodes: [node: Node, nextSibling: Node | null][] = [];\n\n  constructor(\n    private _document: Document,\n    private _dragDropRegistry: DragDropRegistry,\n  ) {}\n\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items: readonly DragRef[]): void {\n    const childNodes = this._element.childNodes;\n    this._relatedNodes = [];\n\n    for (let i = 0; i < childNodes.length; i++) {\n      const node = childNodes[i];\n      this._relatedNodes.push([node, node.nextSibling]);\n    }\n\n    this.withItems(items);\n  }\n\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(\n    item: DragRef,\n    pointerX: number,\n    pointerY: number,\n    pointerDelta: {x: number; y: number},\n  ): {previousIndex: number; currentIndex: number} | null {\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY);\n    const previousSwap = this._previousSwap;\n\n    if (newIndex === -1 || this._activeItems[newIndex] === item) {\n      return null;\n    }\n\n    const toSwapWith = this._activeItems[newIndex];\n\n    // Prevent too many swaps over the same item.\n    if (\n      previousSwap.drag === toSwapWith &&\n      previousSwap.overlaps &&\n      previousSwap.deltaX === pointerDelta.x &&\n      previousSwap.deltaY === pointerDelta.y\n    ) {\n      return null;\n    }\n\n    const previousIndex = this.getItemIndex(item);\n    const current = item.getPlaceholderElement();\n    const overlapElement = toSwapWith.getRootElement();\n\n    if (newIndex > previousIndex) {\n      overlapElement.after(current);\n    } else {\n      overlapElement.before(current);\n    }\n\n    moveItemInArray(this._activeItems, previousIndex, newIndex);\n\n    const newOverlapElement = this._getRootNode().elementFromPoint(pointerX, pointerY);\n    // Note: it's tempting to save the entire `pointerDelta` object here, however that'll\n    // break this functionality, because the same object is passed for all `sort` calls.\n    previousSwap.deltaX = pointerDelta.x;\n    previousSwap.deltaY = pointerDelta.y;\n    previousSwap.drag = toSwapWith;\n    previousSwap.overlaps =\n      overlapElement === newOverlapElement || overlapElement.contains(newOverlapElement);\n\n    return {\n      previousIndex,\n      currentIndex: newIndex,\n    };\n  }\n\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item: DragRef, pointerX: number, pointerY: number, index?: number): void {\n    let enterIndex =\n      index == null || index < 0\n        ? this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n        : index;\n\n    // In some cases (e.g. when the container has padding) we might not be able to figure\n    // out which item to insert the dragged item next to, because the pointer didn't overlap\n    // with anything. In that case we find the item that's closest to the pointer.\n    if (enterIndex === -1) {\n      enterIndex = this._getClosestItemIndexToPointer(item, pointerX, pointerY);\n    }\n\n    const targetItem = this._activeItems[enterIndex] as DragRef | undefined;\n    const currentIndex = this._activeItems.indexOf(item);\n\n    if (currentIndex > -1) {\n      this._activeItems.splice(currentIndex, 1);\n    }\n\n    if (targetItem && !this._dragDropRegistry.isDragging(targetItem)) {\n      this._activeItems.splice(enterIndex, 0, item);\n      targetItem.getRootElement().before(item.getPlaceholderElement());\n    } else {\n      this._activeItems.push(item);\n      this._element.appendChild(item.getPlaceholderElement());\n    }\n  }\n\n  /** Sets the items that are currently part of the list. */\n  withItems(items: readonly DragRef[]): void {\n    this._activeItems = items.slice();\n  }\n\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate: SortPredicate<DragRef>): void {\n    this._sortPredicate = predicate;\n  }\n\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset(): void {\n    const root = this._element;\n    const previousSwap = this._previousSwap;\n\n    // Moving elements around in the DOM can break things like the `@for` loop, because it\n    // uses comment nodes to know where to insert elements. To avoid such issues, we restore\n    // the DOM nodes in the list to their original order when the list is reset.\n    // Note that this could be simpler if we just saved all the nodes, cleared the root\n    // and then appended them in the original order. We don't do it, because it can break\n    // down depending on when the snapshot was taken. E.g. we may end up snapshotting the\n    // placeholder element which is removed after dragging.\n    for (let i = this._relatedNodes.length - 1; i > -1; i--) {\n      const [node, nextSibling] = this._relatedNodes[i];\n      if (node.parentNode === root && node.nextSibling !== nextSibling) {\n        if (nextSibling === null) {\n          root.appendChild(node);\n        } else if (nextSibling.parentNode === root) {\n          root.insertBefore(node, nextSibling);\n        }\n      }\n    }\n\n    this._relatedNodes = [];\n    this._activeItems = [];\n    previousSwap.drag = null;\n    previousSwap.deltaX = previousSwap.deltaY = 0;\n    previousSwap.overlaps = false;\n  }\n\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot(): readonly DragRef[] {\n    return this._activeItems;\n  }\n\n  /** Gets the index of a specific item. */\n  getItemIndex(item: DragRef): number {\n    return this._activeItems.indexOf(item);\n  }\n\n  /** Gets the item at a specific index. */\n  getItemAtIndex(index: number): DragRef | null {\n    return this._activeItems[index] || null;\n  }\n\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(): void {\n    this._activeItems.forEach(item => {\n      if (this._dragDropRegistry.isDragging(item)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        item._sortFromLastPointerPosition();\n      }\n    });\n  }\n\n  withElementContainer(container: HTMLElement): void {\n    if (container !== this._element) {\n      this._element = container;\n      this._rootNode = undefined;\n    }\n  }\n\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  private _getItemIndexFromPointerPosition(\n    item: DragRef,\n    pointerX: number,\n    pointerY: number,\n  ): number {\n    const elementAtPoint = this._getRootNode().elementFromPoint(\n      Math.floor(pointerX),\n      Math.floor(pointerY),\n    );\n    const index = elementAtPoint\n      ? this._activeItems.findIndex(item => {\n          const root = item.getRootElement();\n          return elementAtPoint === root || root.contains(elementAtPoint);\n        })\n      : -1;\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n\n  /** Lazily resolves the list's root node. */\n  private _getRootNode(): DocumentOrShadowRoot {\n    // Resolve the root node lazily to ensure that the drop list is in its final place in the DOM.\n    if (!this._rootNode) {\n      this._rootNode = _getShadowRoot(this._element) || this._document;\n    }\n    return this._rootNode;\n  }\n\n  /**\n   * Finds the index of the item that's closest to the item being dragged.\n   * @param item Item being dragged.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  private _getClosestItemIndexToPointer(item: DragRef, pointerX: number, pointerY: number): number {\n    if (this._activeItems.length === 0) {\n      return -1;\n    }\n\n    if (this._activeItems.length === 1) {\n      return 0;\n    }\n\n    let minDistance = Infinity;\n    let minIndex = -1;\n\n    // Find the Euclidean distance (https://en.wikipedia.org/wiki/Euclidean_distance) between each\n    // item and the pointer, and return the smallest one. Note that this is a bit flawed in that DOM\n    // nodes are rectangles, not points, so we use the top/left coordinates. It should be enough\n    // for our purposes.\n    for (let i = 0; i < this._activeItems.length; i++) {\n      const current = this._activeItems[i];\n      if (current !== item) {\n        const {x, y} = current.getRootElement().getBoundingClientRect();\n        const distance = Math.hypot(pointerX - x, pointerY - y);\n\n        if (distance < minDistance) {\n          minDistance = distance;\n          minIndex = i;\n        }\n      }\n    }\n\n    return minIndex;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ElementRef, NgZone} from '@angular/core';\nimport {Direction} from '../bidi';\nimport {coerceElement} from '../coercion';\nimport {ViewportRuler} from '../scrolling';\nimport {_getShadowRoot} from '../platform';\nimport {Subject, Subscription, interval, animationFrameScheduler} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {DragDropRegistry} from './drag-drop-registry';\nimport type {DragRef, Point} from './drag-ref';\nimport {isPointerNearDomRect, isInsideClientRect} from './dom/dom-rect';\nimport {ParentPositionTracker} from './dom/parent-position-tracker';\nimport {DragCSSStyleDeclaration} from './dom/styling';\nimport {DropListSortStrategy} from './sorting/drop-list-sort-strategy';\nimport {SingleAxisSortStrategy} from './sorting/single-axis-sort-strategy';\nimport {MixedSortStrategy} from './sorting/mixed-sort-strategy';\nimport {DropListOrientation} from './directives/config';\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n\n/** Vertical direction in which we can auto-scroll. */\nenum AutoScrollVerticalDirection {\n  NONE,\n  UP,\n  DOWN,\n}\n\n/** Horizontal direction in which we can auto-scroll. */\nenum AutoScrollHorizontalDirection {\n  NONE,\n  LEFT,\n  RIGHT,\n}\n\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nexport class DropListRef<T = any> {\n  /** Element that the drop list is attached to. */\n  element: HTMLElement | ElementRef<HTMLElement>;\n\n  /** Whether starting a dragging sequence from this container is disabled. */\n  disabled: boolean = false;\n\n  /** Whether sorting items within the list is disabled. */\n  sortingDisabled: boolean = false;\n\n  /** Locks the position of the draggable elements inside the container along the specified axis. */\n  lockAxis: 'x' | 'y';\n\n  /**\n   * Whether auto-scrolling the view when the user\n   * moves their pointer close to the edges is disabled.\n   */\n  autoScrollDisabled: boolean = false;\n\n  /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n  autoScrollStep: number = 2;\n\n  /**\n   * Whether the items in the list should leave an anchor node when leaving the initial container.\n   */\n  hasAnchor: boolean = false;\n\n  /**\n   * Function that is used to determine whether an item\n   * is allowed to be moved into a drop container.\n   */\n  enterPredicate: (drag: DragRef, drop: DropListRef) => boolean = () => true;\n\n  /** Function that is used to determine whether an item can be sorted into a particular index. */\n  sortPredicate: (index: number, drag: DragRef, drop: DropListRef) => boolean = () => true;\n\n  /** Emits right before dragging has started. */\n  readonly beforeStarted = new Subject<void>();\n\n  /**\n   * Emits when the user has moved a new drag item into this container.\n   */\n  readonly entered = new Subject<{item: DragRef; container: DropListRef; currentIndex: number}>();\n\n  /**\n   * Emits when the user removes an item from the container\n   * by dragging it into another container.\n   */\n  readonly exited = new Subject<{item: DragRef; container: DropListRef}>();\n\n  /** Emits when the user drops an item inside the container. */\n  readonly dropped = new Subject<{\n    item: DragRef;\n    currentIndex: number;\n    previousIndex: number;\n    container: DropListRef;\n    previousContainer: DropListRef;\n    isPointerOverContainer: boolean;\n    distance: Point;\n    dropPoint: Point;\n    event: MouseEvent | TouchEvent;\n  }>();\n\n  /** Emits as the user is swapping items while actively dragging. */\n  readonly sorted = new Subject<{\n    previousIndex: number;\n    currentIndex: number;\n    container: DropListRef;\n    item: DragRef;\n  }>();\n\n  /** Emits when a dragging sequence is started in a list connected to the current one. */\n  readonly receivingStarted = new Subject<{\n    receiver: DropListRef;\n    initiator: DropListRef;\n    items: DragRef[];\n  }>();\n\n  /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n  readonly receivingStopped = new Subject<{\n    receiver: DropListRef;\n    initiator: DropListRef;\n  }>();\n\n  /** Arbitrary data that can be attached to the drop list. */\n  data: T;\n\n  /** Element that is the direct parent of the drag items. */\n  private _container: HTMLElement;\n\n  /** Whether an item in the list is being dragged. */\n  private _isDragging = false;\n\n  /** Keeps track of the positions of any parent scrollable elements. */\n  private _parentPositions: ParentPositionTracker;\n\n  /** Strategy being used to sort items within the list. */\n  private _sortStrategy: DropListSortStrategy;\n\n  /** Cached `DOMRect` of the drop list. */\n  private _domRect: DOMRect | undefined;\n\n  /** Draggable items in the container. */\n  private _draggables: readonly DragRef[] = [];\n\n  /** Drop lists that are connected to the current one. */\n  private _siblings: readonly DropListRef[] = [];\n\n  /** Connected siblings that currently have a dragged item. */\n  private _activeSiblings = new Set<DropListRef>();\n\n  /** Subscription to the window being scrolled. */\n  private _viewportScrollSubscription = Subscription.EMPTY;\n\n  /** Vertical direction in which the list is currently scrolling. */\n  private _verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n\n  /** Horizontal direction in which the list is currently scrolling. */\n  private _horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n\n  /** Node that is being auto-scrolled. */\n  private _scrollNode: HTMLElement | Window;\n\n  /** Used to signal to the current auto-scroll sequence when to stop. */\n  private readonly _stopScrollTimers = new Subject<void>();\n\n  /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n  private _cachedShadowRoot: DocumentOrShadowRoot | null = null;\n\n  /** Reference to the document. */\n  private _document: Document;\n\n  /** Elements that can be scrolled while the user is dragging. */\n  private _scrollableElements: HTMLElement[] = [];\n\n  /** Initial value for the element's `scroll-snap-type` style. */\n  private _initialScrollSnap: string;\n\n  /** Direction of the list's layout. */\n  private _direction: Direction = 'ltr';\n\n  constructor(\n    element: ElementRef<HTMLElement> | HTMLElement,\n    private _dragDropRegistry: DragDropRegistry,\n    _document: any,\n    private _ngZone: NgZone,\n    private _viewportRuler: ViewportRuler,\n  ) {\n    const coercedElement = (this.element = coerceElement(element));\n    this._document = _document;\n    this.withOrientation('vertical').withElementContainer(coercedElement);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n  }\n\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null!;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n\n  /** Starts dragging an item. */\n  start(): void {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item: DragRef, pointerX: number, pointerY: number, index?: number): void {\n    this._draggingStarted();\n\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({item, container: this, currentIndex: this.getItemIndex(item)});\n  }\n\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item: DragRef): void {\n    this._reset();\n    this.exited.next({item, container: this});\n  }\n\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(\n    item: DragRef,\n    currentIndex: number,\n    previousIndex: number,\n    previousContainer: DropListRef,\n    isPointerOverContainer: boolean,\n    distance: Point,\n    dropPoint: Point,\n    event: MouseEvent | TouchEvent = {} as any,\n  ): void {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event,\n    });\n  }\n\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items: DragRef[]): this {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n\n    return this;\n  }\n\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction: Direction): this {\n    this._direction = direction;\n    if (this._sortStrategy instanceof SingleAxisSortStrategy) {\n      this._sortStrategy.direction = direction;\n    }\n    return this;\n  }\n\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo: DropListRef[]): this {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation: DropListOrientation): this {\n    if (orientation === 'mixed') {\n      this._sortStrategy = new MixedSortStrategy(this._document, this._dragDropRegistry);\n    } else {\n      const strategy = new SingleAxisSortStrategy(this._dragDropRegistry);\n      strategy.direction = this._direction;\n      strategy.orientation = orientation;\n      this._sortStrategy = strategy;\n    }\n    this._sortStrategy.withElementContainer(this._container);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    return this;\n  }\n\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements: HTMLElement[]): this {\n    const element = this._container;\n\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements =\n      elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n\n  /**\n   * Configures the drop list so that a different element is used as the container for the\n   * dragged items. This is useful for the cases when one might not have control over the\n   * full DOM that sets up the dragging.\n   * Note that the alternate container needs to be a descendant of the drop list.\n   * @param container New element container to be assigned.\n   */\n  withElementContainer(container: HTMLElement): this {\n    if (container === this._container) {\n      return this;\n    }\n\n    const element = coerceElement(this.element);\n\n    if (\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n      container !== element &&\n      !element.contains(container)\n    ) {\n      throw new Error(\n        'Invalid DOM structure for drop list. Alternate container element must be a descendant of the drop list.',\n      );\n    }\n\n    const oldContainerIndex = this._scrollableElements.indexOf(this._container);\n    const newContainerIndex = this._scrollableElements.indexOf(container);\n\n    if (oldContainerIndex > -1) {\n      this._scrollableElements.splice(oldContainerIndex, 1);\n    }\n\n    if (newContainerIndex > -1) {\n      this._scrollableElements.splice(newContainerIndex, 1);\n    }\n\n    if (this._sortStrategy) {\n      this._sortStrategy.withElementContainer(container);\n    }\n\n    this._cachedShadowRoot = null;\n    this._scrollableElements.unshift(container);\n    this._container = container;\n    return this;\n  }\n\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents(): readonly HTMLElement[] {\n    return this._scrollableElements;\n  }\n\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item: DragRef): number {\n    return this._isDragging\n      ? this._sortStrategy.getItemIndex(item)\n      : this._draggables.indexOf(item);\n  }\n\n  /**\n   * Gets the item at a specific index.\n   * @param index Index at which to retrieve the item.\n   */\n  getItemAtIndex(index: number): DragRef | null {\n    return this._isDragging\n      ? this._sortStrategy.getItemAtIndex(index)\n      : this._draggables[index] || null;\n  }\n\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving(): boolean {\n    return this._activeSiblings.size > 0;\n  }\n\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(\n    item: DragRef,\n    pointerX: number,\n    pointerY: number,\n    pointerDelta: {x: number; y: number},\n  ): void {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (\n      this.sortingDisabled ||\n      !this._domRect ||\n      !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)\n    ) {\n      return;\n    }\n\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item,\n      });\n    }\n  }\n\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX: number, pointerY: number) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n\n    let scrollNode: HTMLElement | Window | undefined;\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n\n      if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(\n          element as HTMLElement,\n          position.clientRect,\n          this._direction,\n          pointerX,\n          pointerY,\n        );\n\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element as HTMLElement;\n        }\n      }\n    });\n\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {width, height} = this._viewportRuler.getViewportSize();\n      const domRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0,\n      } as DOMRect;\n      verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n      scrollNode = window;\n    }\n\n    if (\n      scrollNode &&\n      (verticalScrollDirection !== this._verticalScrollDirection ||\n        horizontalScrollDirection !== this._horizontalScrollDirection ||\n        scrollNode !== this._scrollNode)\n    ) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n\n  /** Starts the dragging sequence within the list. */\n  private _draggingStarted() {\n    const styles = this._container.style as DragCSSStyleDeclaration;\n    this.beforeStarted.next();\n    this._isDragging = true;\n\n    if (\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n      // Prevent the check from running on apps not using an alternate container. Ideally we\n      // would always run it, but introducing it at this stage would be a breaking change.\n      this._container !== coerceElement(this.element)\n    ) {\n      for (const drag of this._draggables) {\n        if (!drag.isDragging() && drag.getVisibleElement().parentNode !== this._container) {\n          throw new Error(\n            'Invalid DOM structure for drop list. All items must be placed directly inside of the element container.',\n          );\n        }\n      }\n    }\n\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n\n  /** Caches the positions of the configured scrollable parents. */\n  private _cacheParentPositions() {\n    this._parentPositions.cache(this._scrollableElements);\n\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `DOMRect`.\n    this._domRect = this._parentPositions.positions.get(this._container)!.clientRect!;\n  }\n\n  /** Resets the container to its initial state. */\n  private _reset() {\n    this._isDragging = false;\n    const styles = this._container.style as DragCSSStyleDeclaration;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n\n  /** Starts the interval that'll auto-scroll the element. */\n  private _startScrollInterval = () => {\n    this._stopScrolling();\n\n    interval(0, animationFrameScheduler)\n      .pipe(takeUntil(this._stopScrollTimers))\n      .subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n\n        if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n          node.scrollBy(0, scrollStep);\n        }\n\n        if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n  };\n\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x: number, y: number): boolean {\n    return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n  }\n\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item: DragRef, x: number, y: number): DropListRef | undefined {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item: DragRef, x: number, y: number): boolean {\n    if (\n      !this._domRect ||\n      !isInsideClientRect(this._domRect, x, y) ||\n      !this.enterPredicate(item, this)\n    ) {\n      return false;\n    }\n\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y) as HTMLElement | null;\n\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n\n    // The `DOMRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === this._container || this._container.contains(elementFromPoint);\n  }\n\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling: DropListRef, items: DragRef[]) {\n    const activeSiblings = this._activeSiblings;\n\n    if (\n      !activeSiblings.has(sibling) &&\n      items.every(item => {\n        // Note that we have to add an exception to the `enterPredicate` for items that started off\n        // in this drop list. The drag ref has logic that allows an item to return to its initial\n        // container, if it has left the initial container and none of the connected containers\n        // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n        return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n      })\n    ) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items,\n      });\n    }\n  }\n\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling: DropListRef) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({initiator: sibling, receiver: this});\n  }\n\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  private _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry\n      .scrolled(this._getShadowRoot())\n      .subscribe(event => {\n        if (this.isDragging()) {\n          const scrollDifference = this._parentPositions.handleScroll(event);\n\n          if (scrollDifference) {\n            this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n          }\n        } else if (this.isReceiving()) {\n          this._cacheParentPositions();\n        }\n      });\n  }\n\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  private _getShadowRoot(): DocumentOrShadowRoot {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(this._container);\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n\n    return this._cachedShadowRoot;\n  }\n\n  /** Notifies any siblings that may potentially receive the item. */\n  private _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy\n      .getActiveItemsSnapshot()\n      .filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect: DOMRect, pointerY: number) {\n  const {top, bottom, height} = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return AutoScrollVerticalDirection.UP;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return AutoScrollVerticalDirection.DOWN;\n  }\n\n  return AutoScrollVerticalDirection.NONE;\n}\n\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect: DOMRect, pointerX: number) {\n  const {left, right, width} = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return AutoScrollHorizontalDirection.LEFT;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return AutoScrollHorizontalDirection.RIGHT;\n  }\n\n  return AutoScrollHorizontalDirection.NONE;\n}\n\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(\n  element: HTMLElement,\n  clientRect: DOMRect,\n  direction: Direction,\n  pointerX: number,\n  pointerY: number,\n): [AutoScrollVerticalDirection, AutoScrollHorizontalDirection] {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n\n    if (computedVertical === AutoScrollVerticalDirection.UP) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = AutoScrollVerticalDirection.UP;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n    }\n  }\n\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n\n    if (direction === 'rtl') {\n      if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n        // In RTL `scrollLeft` will be negative when scrolled.\n        if (scrollLeft < 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n        }\n      } else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n      }\n    } else {\n      if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n        if (scrollLeft > 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n        }\n      } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n      }\n    }\n  }\n\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Injectable,\n  ListenerOptions,\n  NgZone,\n  OnDestroy,\n  RendererFactory2,\n  ViewEncapsulation,\n  WritableSignal,\n  inject,\n  signal,\n  DOCUMENT,\n} from '@angular/core';\n\nimport {_CdkPrivateStyleLoader} from '../private';\nimport {Observable, Observer, Subject, merge} from 'rxjs';\nimport type {DropListRef} from './drop-list-ref';\nimport type {DragRef} from './drag-ref';\nimport type {CdkDrag} from './directives/drag';\n\n/** Event options that can be used to bind a capturing event. */\nconst capturingEventOptions = {\n  capture: true,\n};\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = {\n  passive: false,\n  capture: true,\n};\n\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\n@Component({\n  styleUrl: 'resets.css',\n  encapsulation: ViewEncapsulation.None,\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'cdk-drag-resets-container': ''},\n})\nexport class _ResetsLoader {}\n\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n@Injectable({providedIn: 'root'})\nexport class DragDropRegistry implements OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _document = inject(DOCUMENT);\n  private _styleLoader = inject(_CdkPrivateStyleLoader);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n  private _cleanupDocumentTouchmove: (() => void) | undefined;\n  private _scroll: Subject<Event> = new Subject<Event>();\n\n  /** Registered drop container instances. */\n  private _dropInstances = new Set<DropListRef>();\n\n  /** Registered drag item instances. */\n  private _dragInstances = new Set<DragRef>();\n\n  /** Drag item instances that are currently being dragged. */\n  private _activeDragInstances: WritableSignal<DragRef[]> = signal([]);\n\n  /** Keeps track of the event listeners that we've bound to the `document`. */\n  private _globalListeners: (() => void)[] | undefined;\n\n  /**\n   * Predicate function to check if an item is being dragged.  Moved out into a property,\n   * because it'll be called a lot and we don't want to create a new function every time.\n   */\n  private _draggingPredicate = (item: DragRef) => item.isDragging();\n\n  /**\n   * Map tracking DOM nodes and their corresponding drag directives. Note that this is different\n   * from looking through the `_dragInstances` and getting their root node, because the root node\n   * isn't necessarily the node that the directive is set on.\n   */\n  private _domNodesToDirectives: WeakMap<Node, CdkDrag> | null = null;\n\n  /**\n   * Emits the `touchmove` or `mousemove` events that are dispatched\n   * while the user is dragging a drag item instance.\n   */\n  readonly pointerMove: Subject<TouchEvent | MouseEvent> = new Subject<TouchEvent | MouseEvent>();\n\n  /**\n   * Emits the `touchend` or `mouseup` events that are dispatched\n   * while the user is dragging a drag item instance.\n   */\n  readonly pointerUp: Subject<TouchEvent | MouseEvent> = new Subject<TouchEvent | MouseEvent>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop: DropListRef) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag: DragRef) {\n    this._dragInstances.add(drag);\n\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._cleanupDocumentTouchmove?.();\n        this._cleanupDocumentTouchmove = this._renderer.listen(\n          this._document,\n          'touchmove',\n          this._persistentTouchmoveListener,\n          activeCapturingEventOptions,\n        );\n      });\n    }\n  }\n\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop: DropListRef) {\n    this._dropInstances.delete(drop);\n  }\n\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag: DragRef) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n\n    if (this._dragInstances.size === 0) {\n      this._cleanupDocumentTouchmove?.();\n    }\n  }\n\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag: DragRef, event: TouchEvent | MouseEvent) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances().indexOf(drag) > -1) {\n      return;\n    }\n\n    this._styleLoader.load(_ResetsLoader);\n    this._activeDragInstances.update(instances => [...instances, drag]);\n\n    if (this._activeDragInstances().length === 1) {\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      const isTouchEvent = event.type.startsWith('touch');\n      const endEventHandler = (e: Event) => this.pointerUp.next(e as TouchEvent | MouseEvent);\n\n      const toBind: [name: string, handler: (event: Event) => void, options: ListenerOptions][] = [\n        // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n        // the document. See https://github.com/angular/components/issues/17144.\n        ['scroll', (e: Event) => this._scroll.next(e), capturingEventOptions],\n\n        // Preventing the default action on `mousemove` isn't enough to disable text selection\n        // on Safari so we need to prevent the selection event as well. Alternatively this can\n        // be done by setting `user-select: none` on the `body`, however it has causes a style\n        // recalculation which can be expensive on pages with a lot of elements.\n        ['selectstart', this._preventDefaultWhileDragging, activeCapturingEventOptions],\n      ];\n\n      if (isTouchEvent) {\n        toBind.push(\n          ['touchend', endEventHandler, capturingEventOptions],\n          ['touchcancel', endEventHandler, capturingEventOptions],\n        );\n      } else {\n        toBind.push(['mouseup', endEventHandler, capturingEventOptions]);\n      }\n\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        toBind.push([\n          'mousemove',\n          (e: Event) => this.pointerMove.next(e as MouseEvent),\n          activeCapturingEventOptions,\n        ]);\n      }\n\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners = toBind.map(([name, handler, options]) =>\n          this._renderer.listen(this._document, name, handler, options),\n        );\n      });\n    }\n  }\n\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag: DragRef) {\n    this._activeDragInstances.update(instances => {\n      const index = instances.indexOf(drag);\n      if (index > -1) {\n        instances.splice(index, 1);\n        return [...instances];\n      }\n      return instances;\n    });\n\n    if (this._activeDragInstances().length === 0) {\n      this._clearGlobalListeners();\n    }\n  }\n\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag: DragRef) {\n    return this._activeDragInstances().indexOf(drag) > -1;\n  }\n\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot?: DocumentOrShadowRoot | null): Observable<Event> {\n    const streams: Observable<Event>[] = [this._scroll];\n\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(\n        new Observable((observer: Observer<Event>) => {\n          return this._ngZone.runOutsideAngular(() => {\n            const cleanup = this._renderer.listen(\n              shadowRoot as ShadowRoot,\n              'scroll',\n              (event: Event) => {\n                if (this._activeDragInstances().length) {\n                  observer.next(event);\n                }\n              },\n              capturingEventOptions,\n            );\n\n            return () => {\n              cleanup();\n            };\n          });\n        }),\n      );\n    }\n\n    return merge(...streams);\n  }\n\n  /**\n   * Tracks the DOM node which has a draggable directive.\n   * @param node Node to track.\n   * @param dragRef Drag directive set on the node.\n   */\n  registerDirectiveNode(node: Node, dragRef: CdkDrag): void {\n    this._domNodesToDirectives ??= new WeakMap();\n    this._domNodesToDirectives.set(node, dragRef);\n  }\n\n  /**\n   * Stops tracking a draggable directive node.\n   * @param node Node to stop tracking.\n   */\n  removeDirectiveNode(node: Node): void {\n    this._domNodesToDirectives?.delete(node);\n  }\n\n  /**\n   * Gets the drag directive corresponding to a specific DOM node, if any.\n   * @param node Node for which to do the lookup.\n   */\n  getDragDirectiveForNode(node: Node): CdkDrag | null {\n    return this._domNodesToDirectives?.get(node) || null;\n  }\n\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._domNodesToDirectives = null;\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n\n  /**\n   * Event listener that will prevent the default browser action while the user is dragging.\n   * @param event Event whose default action should be prevented.\n   */\n  private _preventDefaultWhileDragging = (event: Event) => {\n    if (this._activeDragInstances().length > 0) {\n      event.preventDefault();\n    }\n  };\n\n  /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n  private _persistentTouchmoveListener = (event: TouchEvent) => {\n    if (this._activeDragInstances().length > 0) {\n      // Note that we only want to prevent the default action after dragging has actually started.\n      // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n      // but it could be pushed back if the user has set up a drag delay or threshold.\n      if (this._activeDragInstances().some(this._draggingPredicate)) {\n        event.preventDefault();\n      }\n\n      this.pointerMove.next(event);\n    }\n  };\n\n  /** Clears out the global event listeners from the `document`. */\n  private _clearGlobalListeners() {\n    this._globalListeners?.forEach(cleanup => cleanup());\n    this._globalListeners = undefined;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, NgZone, ElementRef, inject, RendererFactory2, DOCUMENT} from '@angular/core';\n\nimport {ViewportRuler} from '../scrolling';\nimport {DragRef, DragRefConfig} from './drag-ref';\nimport {DropListRef} from './drop-list-ref';\nimport {DragDropRegistry} from './drag-drop-registry';\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5,\n};\n\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\n@Injectable({providedIn: 'root'})\nexport class DragDrop {\n  private _document = inject(DOCUMENT);\n  private _ngZone = inject(NgZone);\n  private _viewportRuler = inject(ViewportRuler);\n  private _dragDropRegistry = inject(DragDropRegistry);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag<T = any>(\n    element: ElementRef<HTMLElement> | HTMLElement,\n    config: DragRefConfig = DEFAULT_CONFIG,\n  ): DragRef<T> {\n    return new DragRef<T>(\n      element,\n      config,\n      this._document,\n      this._ngZone,\n      this._viewportRuler,\n      this._dragDropRegistry,\n      this._renderer,\n    );\n  }\n\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList<T = any>(element: ElementRef<HTMLElement> | HTMLElement): DropListRef<T> {\n    return new DropListRef<T>(\n      element,\n      this._dragDropRegistry,\n      this._document,\n      this._ngZone,\n      this._viewportRuler,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport type {CdkDrag} from './directives/drag';\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nexport const CDK_DRAG_PARENT = new InjectionToken<CdkDrag>('CDK_DRAG_PARENT');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nexport function assertElementNode(node: Node, name: string): asserts node is HTMLElement {\n  if (node.nodeType !== 1) {\n    throw Error(\n      `${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterViewInit,\n  Directive,\n  ElementRef,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\nimport type {CdkDrag} from './drag';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\nimport {assertElementNode} from './assertions';\nimport {DragDropRegistry} from '../drag-drop-registry';\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DRAG_HANDLE = new InjectionToken<CdkDragHandle>('CdkDragHandle');\n\n/** Handle that can be used to drag a CdkDrag instance. */\n@Directive({\n  selector: '[cdkDragHandle]',\n  host: {\n    'class': 'cdk-drag-handle',\n  },\n  providers: [{provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle}],\n})\nexport class CdkDragHandle implements AfterViewInit, OnDestroy {\n  element = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  private _parentDrag = inject<CdkDrag>(CDK_DRAG_PARENT, {optional: true, skipSelf: true});\n  private _dragDropRegistry = inject(DragDropRegistry);\n\n  /** Emits when the state of the handle has changed. */\n  readonly _stateChanges = new Subject<CdkDragHandle>();\n\n  /** Whether starting to drag through this handle is disabled. */\n  @Input({alias: 'cdkDragHandleDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._stateChanges.next(this);\n  }\n  private _disabled = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(this.element.nativeElement, 'cdkDragHandle');\n    }\n\n    this._parentDrag?._addHandle(this);\n  }\n\n  ngAfterViewInit() {\n    if (!this._parentDrag) {\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        const ref = this._dragDropRegistry.getDragDirectiveForNode(parent);\n        if (ref) {\n          this._parentDrag = ref;\n          ref._addHandle(this);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._parentDrag?._removeHandle(this);\n    this._stateChanges.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {DragRefConfig, DragConstrainPosition} from '../drag-ref';\n\n/** Possible values that can be used to configure the drag start delay. */\nexport type DragStartDelay = number | {touch: number; mouse: number};\n\n/** Possible axis along which dragging can be locked. */\nexport type DragAxis = 'x' | 'y';\n\n/** Possible orientations for a drop list. */\nexport type DropListOrientation = 'horizontal' | 'vertical' | 'mixed';\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nexport const CDK_DRAG_CONFIG = new InjectionToken<DragDropConfig>('CDK_DRAG_CONFIG');\n\n/**\n * Object that can be used to configure the drag\n * items and drop lists within a module or a component.\n */\nexport interface DragDropConfig extends Partial<DragRefConfig> {\n  lockAxis?: DragAxis;\n  dragStartDelay?: DragStartDelay;\n  constrainPosition?: DragConstrainPosition;\n  previewClass?: string | string[];\n  boundaryElement?: string;\n  rootElementSelector?: string;\n  draggingDisabled?: boolean;\n  sortingDisabled?: boolean;\n  listAutoScrollDisabled?: boolean;\n  listOrientation?: DropListOrientation;\n  zIndex?: number;\n  previewContainer?: 'global' | 'parent';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directionality} from '../../bidi';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnDestroy,\n  Output,\n  ViewContainerRef,\n  OnChanges,\n  SimpleChanges,\n  ChangeDetectorRef,\n  InjectionToken,\n  booleanAttribute,\n  afterNextRender,\n  AfterViewInit,\n  inject,\n  Injector,\n  numberAttribute,\n} from '@angular/core';\nimport {coerceElement, coerceNumberProperty} from '../../coercion';\nimport {BehaviorSubject, Observable, Observer, Subject, merge} from 'rxjs';\nimport {startWith, take, map, takeUntil, switchMap, tap} from 'rxjs/operators';\nimport type {\n  CdkDragDrop,\n  CdkDragEnd,\n  CdkDragEnter,\n  CdkDragExit,\n  CdkDragMove,\n  CdkDragStart,\n  CdkDragRelease,\n} from '../drag-events';\nimport {CDK_DRAG_HANDLE, CdkDragHandle} from './drag-handle';\nimport {CdkDragPlaceholder} from './drag-placeholder';\nimport {CdkDragPreview} from './drag-preview';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\nimport {DragRef, Point, PreviewContainer, DragConstrainPosition} from '../drag-ref';\nimport type {CdkDropList} from './drop-list';\nimport {DragDrop} from '../drag-drop';\nimport {CDK_DRAG_CONFIG, DragDropConfig, DragStartDelay, DragAxis} from './config';\nimport {assertElementNode} from './assertions';\nimport {DragDropRegistry} from '../drag-drop-registry';\n\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DROP_LIST = new InjectionToken<CdkDropList>('CdkDropList');\n\n/** Element that can be moved inside a CdkDropList container. */\n@Directive({\n  selector: '[cdkDrag]',\n  exportAs: 'cdkDrag',\n  host: {\n    'class': 'cdk-drag',\n    '[class.cdk-drag-disabled]': 'disabled',\n    '[class.cdk-drag-dragging]': '_dragRef.isDragging()',\n  },\n  providers: [{provide: CDK_DRAG_PARENT, useExisting: CdkDrag}],\n})\nexport class CdkDrag<T = any> implements AfterViewInit, OnChanges, OnDestroy {\n  element = inject<ElementRef<HTMLElement>>(ElementRef);\n  dropContainer = inject<CdkDropList>(CDK_DROP_LIST, {optional: true, skipSelf: true})!;\n  private _ngZone = inject(NgZone);\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _dir = inject(Directionality, {optional: true});\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _selfHandle = inject<CdkDragHandle>(CDK_DRAG_HANDLE, {optional: true, self: true});\n  private _parentDrag = inject<CdkDrag>(CDK_DRAG_PARENT, {optional: true, skipSelf: true});\n  private _dragDropRegistry = inject(DragDropRegistry);\n\n  private readonly _destroyed = new Subject<void>();\n  private _handles = new BehaviorSubject<CdkDragHandle[]>([]);\n  private _previewTemplate: CdkDragPreview | null;\n  private _placeholderTemplate: CdkDragPlaceholder | null;\n\n  /** Reference to the underlying drag instance. */\n  _dragRef: DragRef<CdkDrag<T>>;\n\n  /** Arbitrary data to attach to this drag instance. */\n  @Input('cdkDragData') data: T;\n\n  /** Locks the position of the dragged element along the specified axis. */\n  @Input('cdkDragLockAxis') lockAxis: DragAxis;\n\n  /**\n   * Selector that will be used to determine the root draggable element, starting from\n   * the `cdkDrag` element and going up the DOM. Passing an alternate root element is useful\n   * when trying to enable dragging on an element that you might not have access to.\n   */\n  @Input('cdkDragRootElement') rootElementSelector: string;\n\n  /**\n   * Node or selector that will be used to determine the element to which the draggable's\n   * position will be constrained. If a string is passed in, it'll be used as a selector that\n   * will be matched starting from the element's parent and going up the DOM until a match\n   * has been found.\n   */\n  @Input('cdkDragBoundary') boundaryElement: string | ElementRef<HTMLElement> | HTMLElement;\n\n  /**\n   * Amount of milliseconds to wait after the user has put their\n   * pointer down before starting to drag the element.\n   */\n  @Input('cdkDragStartDelay') dragStartDelay: DragStartDelay;\n\n  /**\n   * Sets the position of a `CdkDrag` that is outside of a drop container.\n   * Can be used to restore the element's position for a returning user.\n   */\n  @Input('cdkDragFreeDragPosition') freeDragPosition: Point;\n\n  /** Whether starting to drag this element is disabled. */\n  @Input({alias: 'cdkDragDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || !!(this.dropContainer && this.dropContainer.disabled);\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._dragRef.disabled = this._disabled;\n  }\n  private _disabled: boolean;\n\n  /**\n   * Function that can be used to customize the logic of how the position of the drag item\n   * is limited while it's being dragged. Gets called with a point containing the current position\n   * of the user's pointer on the page, a reference to the item being dragged and its dimensions.\n   * Should return a point describing where the item should be rendered.\n   */\n  @Input('cdkDragConstrainPosition') constrainPosition?: DragConstrainPosition;\n\n  /** Class to be added to the preview element. */\n  @Input('cdkDragPreviewClass') previewClass: string | string[];\n\n  /**\n   * Configures the place into which the preview of the item will be inserted. Can be configured\n   * globally through `CDK_DROP_LIST`. Possible values:\n   * - `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\n   * you don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\n   * its inherited styles.\n   * - `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\n   * inherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\n   * visible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\n   * like `:nth-child` and some flexbox configurations.\n   * - `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\n   * Same advantages and disadvantages as `parent`.\n   */\n  @Input('cdkDragPreviewContainer') previewContainer: PreviewContainer;\n\n  /**\n   * If the parent of the dragged element has a `scale` transform, it can throw off the\n   * positioning when the user starts dragging. Use this input to notify the CDK of the scale.\n   */\n  @Input({alias: 'cdkDragScale', transform: numberAttribute})\n  scale: number = 1;\n\n  /** Emits when the user starts dragging the item. */\n  @Output('cdkDragStarted') readonly started: EventEmitter<CdkDragStart> =\n    new EventEmitter<CdkDragStart>();\n\n  /** Emits when the user has released a drag item, before any animations have started. */\n  @Output('cdkDragReleased') readonly released: EventEmitter<CdkDragRelease> =\n    new EventEmitter<CdkDragRelease>();\n\n  /** Emits when the user stops dragging an item in the container. */\n  @Output('cdkDragEnded') readonly ended: EventEmitter<CdkDragEnd> = new EventEmitter<CdkDragEnd>();\n\n  /** Emits when the user has moved the item into a new container. */\n  @Output('cdkDragEntered') readonly entered: EventEmitter<CdkDragEnter<any>> = new EventEmitter<\n    CdkDragEnter<any>\n  >();\n\n  /** Emits when the user removes the item its container by dragging it into another container. */\n  @Output('cdkDragExited') readonly exited: EventEmitter<CdkDragExit<any>> = new EventEmitter<\n    CdkDragExit<any>\n  >();\n\n  /** Emits when the user drops the item inside a container. */\n  @Output('cdkDragDropped') readonly dropped: EventEmitter<CdkDragDrop<any>> = new EventEmitter<\n    CdkDragDrop<any>\n  >();\n\n  /**\n   * Emits as the user is dragging the item. Use with caution,\n   * because this event will fire for every pixel that the user has dragged.\n   */\n  @Output('cdkDragMoved')\n  readonly moved: Observable<CdkDragMove<T>> = new Observable(\n    (observer: Observer<CdkDragMove<T>>) => {\n      const subscription = this._dragRef.moved\n        .pipe(\n          map(movedEvent => ({\n            source: this,\n            pointerPosition: movedEvent.pointerPosition,\n            event: movedEvent.event,\n            delta: movedEvent.delta,\n            distance: movedEvent.distance,\n          })),\n        )\n        .subscribe(observer);\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    },\n  );\n\n  private _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const dropContainer = this.dropContainer;\n    const config = inject<DragDropConfig>(CDK_DRAG_CONFIG, {optional: true});\n    const dragDrop = inject(DragDrop);\n\n    this._dragRef = dragDrop.createDrag(this.element, {\n      dragStartThreshold:\n        config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold:\n        config && config.pointerDirectionChangeThreshold != null\n          ? config.pointerDirectionChangeThreshold\n          : 5,\n      zIndex: config?.zIndex,\n    });\n    this._dragRef.data = this;\n    this._dragDropRegistry.registerDirectiveNode(this.element.nativeElement, this);\n\n    if (config) {\n      this._assignDefaults(config);\n    }\n\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      dropContainer.addItem(this);\n\n      // The drop container reads this so we need to sync it here.\n      dropContainer._dropListRef.beforeStarted.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._dragRef.scale = this.scale;\n      });\n    }\n\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement(): HTMLElement {\n    return this._dragRef.getPlaceholderElement();\n  }\n\n  /** Returns the root draggable element. */\n  getRootElement(): HTMLElement {\n    return this._dragRef.getRootElement();\n  }\n\n  /** Resets a standalone drag item to its initial position. */\n  reset(): void {\n    this._dragRef.reset();\n  }\n\n  /** Resets drag item to end of boundary element. */\n  resetToBoundary() {\n    this._dragRef.resetToBoundary();\n  }\n\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition(): Readonly<Point> {\n    return this._dragRef.getFreeDragPosition();\n  }\n\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value: Point): void {\n    this._dragRef.setFreeDragPosition(value);\n  }\n\n  ngAfterViewInit() {\n    // We need to wait until after render, in order for the reference\n    // element to be in the proper place in the DOM. This is mostly relevant\n    // for draggable elements inside portals since they get stamped out in\n    // their original DOM position, and then they get transferred to the portal.\n    afterNextRender(\n      () => {\n        this._updateRootElement();\n        this._setupHandlesListener();\n        this._dragRef.scale = this.scale;\n\n        if (this.freeDragPosition) {\n          this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n      },\n      {injector: this._injector},\n    );\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n\n    // We don't have to react to the first change since it's being\n    // handled in the `afterNextRender` queued up in the constructor.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n\n    // Scale affects the free drag position so we need to sync it up here.\n    this._dragRef.scale = this.scale;\n\n    // Skip the first change since it's being handled in the `afterNextRender` queued up in the\n    // constructor.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n\n    this._dragDropRegistry.removeDirectiveNode(this.element.nativeElement);\n\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._handles.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n\n  _addHandle(handle: CdkDragHandle) {\n    const handles = this._handles.getValue();\n    handles.push(handle);\n    this._handles.next(handles);\n  }\n\n  _removeHandle(handle: CdkDragHandle) {\n    const handles = this._handles.getValue();\n    const index = handles.indexOf(handle);\n\n    if (index > -1) {\n      handles.splice(index, 1);\n      this._handles.next(handles);\n    }\n  }\n\n  _setPreviewTemplate(preview: CdkDragPreview) {\n    this._previewTemplate = preview;\n  }\n\n  _resetPreviewTemplate(preview: CdkDragPreview) {\n    if (preview === this._previewTemplate) {\n      this._previewTemplate = null;\n    }\n  }\n\n  _setPlaceholderTemplate(placeholder: CdkDragPlaceholder) {\n    this._placeholderTemplate = placeholder;\n  }\n\n  _resetPlaceholderTemplate(placeholder: CdkDragPlaceholder) {\n    if (placeholder === this._placeholderTemplate) {\n      this._placeholderTemplate = null;\n    }\n  }\n\n  /** Syncs the root element with the `DragRef`. */\n  private _updateRootElement() {\n    const element = this.element.nativeElement as HTMLElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement =\n        element.closest !== undefined\n          ? (element.closest(this.rootElementSelector) as HTMLElement)\n          : // Comment tag doesn't have closest method, so use parent's one.\n            (element.parentElement?.closest(this.rootElementSelector) as HTMLElement);\n    }\n\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n\n    this._dragRef.withRootElement(rootElement || element);\n  }\n\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  private _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n\n    if (!boundary) {\n      return null;\n    }\n\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest<HTMLElement>(boundary);\n    }\n\n    return coerceElement(boundary);\n  }\n\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  private _syncInputs(ref: DragRef<CdkDrag<T>>) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate\n          ? {\n              template: this._placeholderTemplate.templateRef,\n              context: this._placeholderTemplate.data,\n              viewContainer: this._viewContainerRef,\n            }\n          : null;\n        const preview = this._previewTemplate\n          ? {\n              template: this._previewTemplate.templateRef,\n              context: this._previewTemplate.data,\n              matchSize: this._previewTemplate.matchSize,\n              viewContainer: this._viewContainerRef,\n            }\n          : null;\n\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.scale = this.scale;\n        ref.dragStartDelay =\n          typeof dragStartDelay === 'object' && dragStartDelay\n            ? dragStartDelay\n            : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref\n          .withBoundaryElement(this._getBoundaryElement())\n          .withPlaceholderTemplate(placeholder)\n          .withPreviewTemplate(preview)\n          .withPreviewContainer(this.previewContainer || 'global');\n\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        const parentDrag = this._dragDropRegistry.getDragDirectiveForNode(parent);\n        if (parentDrag) {\n          ref.withParent(parentDrag._dragRef);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n\n  /** Handles the events from the underlying `DragRef`. */\n  private _handleEvents(ref: DragRef<CdkDrag<T>>) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({source: this, event: startEvent.event});\n\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({source: this, event: releaseEvent.event});\n    });\n\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event,\n      });\n\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex,\n      });\n    });\n\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this,\n      });\n    });\n\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event,\n      });\n    });\n  }\n\n  /** Assigns the default input values based on a provided config object. */\n  private _assignDefaults(config: DragDropConfig) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer,\n    } = config;\n\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  private _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles\n      .pipe(\n        // Sync the new handles with the DragRef.\n        tap(handles => {\n          const handleElements = handles.map(handle => handle.element);\n\n          // Usually handles are only allowed to be a descendant of the drag element, but if\n          // the consumer defined a different drag root, we should allow the drag element\n          // itself to be a handle too.\n          if (this._selfHandle && this.rootElementSelector) {\n            handleElements.push(this.element);\n          }\n\n          this._dragRef.withHandles(handleElements);\n        }),\n        // Listen if the state of any of the handles changes.\n        switchMap((handles: CdkDragHandle[]) => {\n          return merge(\n            ...handles.map(item => item._stateChanges.pipe(startWith(item))),\n          ) as Observable<CdkDragHandle>;\n        }),\n        takeUntil(this._destroyed),\n      )\n      .subscribe(handleInstance => {\n        // Enabled/disable the handle that changed in the DragRef.\n        const dragRef = this._dragRef;\n        const handle = handleInstance.element.nativeElement;\n        handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n      });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, OnDestroy, Input, InjectionToken, booleanAttribute} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DROP_LIST_GROUP = new InjectionToken<CdkDropListGroup<unknown>>(\n  'CdkDropListGroup',\n);\n\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\n@Directive({\n  selector: '[cdkDropListGroup]',\n  exportAs: 'cdkDropListGroup',\n  providers: [{provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup}],\n})\nexport class CdkDropListGroup<T> implements OnDestroy {\n  /** Drop lists registered inside the group. */\n  readonly _items = new Set<T>();\n\n  /** Whether starting a dragging sequence from inside this group is disabled. */\n  @Input({alias: 'cdkDropListGroupDisabled', transform: booleanAttribute})\n  disabled: boolean = false;\n\n  ngOnDestroy() {\n    this._items.clear();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NumberInput, coerceArray, coerceNumberProperty} from '../../coercion';\nimport {\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  Directive,\n  ChangeDetectorRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {Directionality} from '../../bidi';\nimport {_IdGenerator} from '../../a11y';\nimport {ScrollDispatcher} from '../../scrolling';\nimport {CDK_DROP_LIST, CdkDrag} from './drag';\nimport {CdkDragDrop, CdkDragEnter, CdkDragExit, CdkDragSortEvent} from '../drag-events';\nimport {CDK_DROP_LIST_GROUP, CdkDropListGroup} from './drop-list-group';\nimport {DropListRef} from '../drop-list-ref';\nimport {DragRef} from '../drag-ref';\nimport {DragDrop} from '../drag-drop';\nimport {DropListOrientation, DragAxis, DragDropConfig, CDK_DRAG_CONFIG} from './config';\nimport {merge, Subject} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\nimport {assertElementNode} from './assertions';\n\n/** Container that wraps a set of draggable items. */\n@Directive({\n  selector: '[cdkDropList], cdk-drop-list',\n  exportAs: 'cdkDropList',\n  providers: [\n    // Prevent child drop lists from picking up the same group as their parent.\n    {provide: CDK_DROP_LIST_GROUP, useValue: undefined},\n    {provide: CDK_DROP_LIST, useExisting: CdkDropList},\n  ],\n  host: {\n    'class': 'cdk-drop-list',\n    '[attr.id]': 'id',\n    '[class.cdk-drop-list-disabled]': 'disabled',\n    '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n    '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()',\n  },\n})\nexport class CdkDropList<T = any> implements OnDestroy {\n  element = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _scrollDispatcher = inject(ScrollDispatcher);\n  private _dir = inject(Directionality, {optional: true});\n  private _group = inject<CdkDropListGroup<CdkDropList>>(CDK_DROP_LIST_GROUP, {\n    optional: true,\n    skipSelf: true,\n  });\n\n  /** Refs that have been synced with the drop ref most recently. */\n  private _latestSortedRefs: DragRef[] | undefined;\n\n  /** Emits when the list has been destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** Whether the element's scrollable parents have been resolved. */\n  private _scrollableParentsResolved: boolean;\n\n  /** Keeps track of the drop lists that are currently on the page. */\n  private static _dropLists: CdkDropList[] = [];\n\n  /** Reference to the underlying drop list instance. */\n  _dropListRef: DropListRef<CdkDropList<T>>;\n\n  /**\n   * Other draggable containers that this container is connected to and into which the\n   * container's items can be transferred. Can either be references to other drop containers,\n   * or their unique IDs.\n   */\n  @Input('cdkDropListConnectedTo')\n  connectedTo: (CdkDropList | string)[] | CdkDropList | string = [];\n\n  /** Arbitrary data to attach to this container. */\n  @Input('cdkDropListData') data: T;\n\n  /** Direction in which the list is oriented. */\n  @Input('cdkDropListOrientation') orientation: DropListOrientation;\n\n  /**\n   * Unique ID for the drop zone. Can be used as a reference\n   * in the `connectedTo` of another `CdkDropList`.\n   */\n  @Input() id: string = inject(_IdGenerator).getId('cdk-drop-list-');\n\n  /** Locks the position of the draggable elements inside the container along the specified axis. */\n  @Input('cdkDropListLockAxis') lockAxis: DragAxis;\n\n  /** Whether starting a dragging sequence from this container is disabled. */\n  @Input({alias: 'cdkDropListDisabled', transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (!!this._group && this._group.disabled);\n  }\n  set disabled(value: boolean) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = value;\n  }\n  private _disabled: boolean;\n\n  /** Whether sorting within this drop list is disabled. */\n  @Input({alias: 'cdkDropListSortingDisabled', transform: booleanAttribute})\n  sortingDisabled: boolean;\n\n  /**\n   * Function that is used to determine whether an item\n   * is allowed to be moved into a drop container.\n   */\n  @Input('cdkDropListEnterPredicate')\n  enterPredicate: (drag: CdkDrag, drop: CdkDropList) => boolean = () => true;\n\n  /** Functions that is used to determine whether an item can be sorted into a particular index. */\n  @Input('cdkDropListSortPredicate')\n  sortPredicate: (index: number, drag: CdkDrag, drop: CdkDropList) => boolean = () => true;\n\n  /** Whether to auto-scroll the view when the user moves their pointer close to the edges. */\n  @Input({alias: 'cdkDropListAutoScrollDisabled', transform: booleanAttribute})\n  autoScrollDisabled: boolean;\n\n  /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n  @Input('cdkDropListAutoScrollStep')\n  autoScrollStep: NumberInput;\n\n  /**\n   * Selector that will be used to resolve an alternate element container for the drop list.\n   * Passing an alternate container is useful for the cases where one might not have control\n   * over the parent node of the draggable items within the list (e.g. due to content projection).\n   * This allows for usages like:\n   *\n   * ```\n   * <div cdkDropList cdkDropListElementContainer=\".inner\">\n   *   <div class=\"inner\">\n   *     <div cdkDrag></div>\n   *   </div>\n   * </div>\n   * ```\n   */\n  @Input('cdkDropListElementContainer') elementContainerSelector: string | null;\n\n  /**\n   * By default when an item leaves its initial container, its placeholder will be transferred\n   * to the new container. If that's not desirable for your use case, you can enable this option\n   * which will clone the placeholder and leave it inside the original container. If the item is\n   * returned to the initial container, the anchor element will be removed automatically.\n   *\n   * The cloned placeholder can be styled by targeting the `cdk-drag-anchor` class.\n   *\n   * This option is useful in combination with `cdkDropListSortingDisabled` to implement copying\n   * behavior in a drop list.\n   */\n  @Input({alias: 'cdkDropListHasAnchor', transform: booleanAttribute})\n  hasAnchor: boolean;\n\n  /** Emits when the user drops an item inside the container. */\n  @Output('cdkDropListDropped')\n  readonly dropped: EventEmitter<CdkDragDrop<T, any>> = new EventEmitter<CdkDragDrop<T, any>>();\n\n  /**\n   * Emits when the user has moved a new drag item into this container.\n   */\n  @Output('cdkDropListEntered')\n  readonly entered: EventEmitter<CdkDragEnter<T>> = new EventEmitter<CdkDragEnter<T>>();\n\n  /**\n   * Emits when the user removes an item from the container\n   * by dragging it into another container.\n   */\n  @Output('cdkDropListExited')\n  readonly exited: EventEmitter<CdkDragExit<T>> = new EventEmitter<CdkDragExit<T>>();\n\n  /** Emits as the user is swapping items while actively dragging. */\n  @Output('cdkDropListSorted')\n  readonly sorted: EventEmitter<CdkDragSortEvent<T>> = new EventEmitter<CdkDragSortEvent<T>>();\n\n  /**\n   * Keeps track of the items that are registered with this container. Historically we used to\n   * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n   * well which means that we can't handle cases like dragging the headers of a `mat-table`\n   * correctly. What we do instead is to have the items register themselves with the container\n   * and then we sort them based on their position in the DOM.\n   */\n  private _unsortedItems = new Set<CdkDrag>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const dragDrop = inject(DragDrop);\n    const config = inject<DragDropConfig>(CDK_DRAG_CONFIG, {optional: true});\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(this.element.nativeElement, 'cdkDropList');\n    }\n\n    this._dropListRef = dragDrop.createDropList(this.element);\n    this._dropListRef.data = this;\n\n    if (config) {\n      this._assignDefaults(config);\n    }\n\n    this._dropListRef.enterPredicate = (drag: DragRef<CdkDrag>, drop: DropListRef<CdkDropList>) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n\n    this._dropListRef.sortPredicate = (\n      index: number,\n      drag: DragRef<CdkDrag>,\n      drop: DropListRef<CdkDropList>,\n    ) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n\n    if (this._group) {\n      this._group._items.add(this);\n    }\n  }\n\n  /** Registers an items with the drop list. */\n  addItem(item: CdkDrag): void {\n    this._unsortedItems.add(item);\n    item._dragRef._withDropContainer(this._dropListRef);\n\n    // Only sync the items while dragging since this method is\n    // called when items are being initialized one-by-one.\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef(this.getSortedItems().map(item => item._dragRef));\n    }\n  }\n\n  /** Removes an item from the drop list. */\n  removeItem(item: CdkDrag): void {\n    this._unsortedItems.delete(item);\n\n    // This method might be called on destroy so we always want to sync with the ref.\n    // Note that we reuse the last set of synced items, rather than re-sorting the whole\n    // list, because it can slow down re-renders of large lists (see #30737).\n    if (this._latestSortedRefs) {\n      const index = this._latestSortedRefs.indexOf(item._dragRef);\n\n      if (index > -1) {\n        this._latestSortedRefs.splice(index, 1);\n        this._syncItemsWithRef(this._latestSortedRefs);\n      }\n    }\n  }\n\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems(): CdkDrag[] {\n    return Array.from(this._unsortedItems).sort((a: CdkDrag, b: CdkDrag) => {\n      const documentPosition = a._dragRef\n        .getVisibleElement()\n        .compareDocumentPosition(b._dragRef.getVisibleElement());\n\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n\n    this._latestSortedRefs = undefined;\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  private _setupInputSyncSubscription(ref: DropListRef<CdkDropList>) {\n    if (this._dir) {\n      this._dir.change\n        .pipe(startWith(this._dir.value), takeUntil(this._destroyed))\n        .subscribe(value => ref.withDirection(value));\n    }\n\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n\n          return correspondingDropList!;\n        }\n\n        return drop;\n      });\n\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher\n          .getAncestorScrollContainers(this.element)\n          .map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n\n      if (this.elementContainerSelector) {\n        const container = this.element.nativeElement.querySelector(this.elementContainerSelector);\n\n        if (!container && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw new Error(\n            `CdkDropList could not find an element container matching the selector \"${this.elementContainerSelector}\"`,\n          );\n        }\n\n        ref.withElementContainer(container as HTMLElement);\n      }\n\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = this.sortingDisabled;\n      ref.autoScrollDisabled = this.autoScrollDisabled;\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.hasAnchor = this.hasAnchor;\n      ref\n        .connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef))\n        .withOrientation(this.orientation);\n    });\n  }\n\n  /** Handles events from the underlying DropListRef. */\n  private _handleEvents(ref: DropListRef<CdkDropList>) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef(this.getSortedItems().map(item => item._dragRef));\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex,\n      });\n    });\n\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data,\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data,\n      });\n    });\n\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event,\n      });\n\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() =>\n      this._changeDetectorRef.markForCheck(),\n    );\n  }\n\n  /** Assigns the default input values based on a provided config object. */\n  private _assignDefaults(config: DragDropConfig) {\n    const {lockAxis, draggingDisabled, sortingDisabled, listAutoScrollDisabled, listOrientation} =\n      config;\n\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  private _syncItemsWithRef(items: DragRef[]) {\n    this._latestSortedRefs = items;\n    this._dropListRef.withItems(items);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  TemplateRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DRAG_PREVIEW = new InjectionToken<CdkDragPreview>('CdkDragPreview');\n\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\n@Directive({\n  selector: 'ng-template[cdkDragPreview]',\n  providers: [{provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview}],\n})\nexport class CdkDragPreview<T = any> implements OnDestroy {\n  templateRef = inject<TemplateRef<T>>(TemplateRef);\n\n  private _drag = inject(CDK_DRAG_PARENT, {optional: true});\n\n  /** Context data to be added to the preview template instance. */\n  @Input() data: T;\n\n  /** Whether the preview should preserve the same size as the item that is being dragged. */\n  @Input({transform: booleanAttribute}) matchSize: boolean = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._drag?._setPreviewTemplate(this);\n  }\n\n  ngOnDestroy(): void {\n    this._drag?._resetPreviewTemplate(this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, TemplateRef, Input, InjectionToken, inject, OnDestroy} from '@angular/core';\nimport {CDK_DRAG_PARENT} from '../drag-parent';\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_DRAG_PLACEHOLDER = new InjectionToken<CdkDragPlaceholder>('CdkDragPlaceholder');\n\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\n@Directive({\n  selector: 'ng-template[cdkDragPlaceholder]',\n  providers: [{provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder}],\n})\nexport class CdkDragPlaceholder<T = any> implements OnDestroy {\n  templateRef = inject<TemplateRef<T>>(TemplateRef);\n\n  private _drag = inject(CDK_DRAG_PARENT, {optional: true});\n\n  /** Context data to be added to the placeholder template instance. */\n  @Input() data: T;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._drag?._setPlaceholderTemplate(this);\n  }\n\n  ngOnDestroy(): void {\n    this._drag?._resetPlaceholderTemplate(this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkScrollableModule} from '../scrolling';\nimport {CdkDropList} from './directives/drop-list';\nimport {CdkDropListGroup} from './directives/drop-list-group';\nimport {CdkDrag} from './directives/drag';\nimport {CdkDragHandle} from './directives/drag-handle';\nimport {CdkDragPreview} from './directives/drag-preview';\nimport {CdkDragPlaceholder} from './directives/drag-placeholder';\nimport {DragDrop} from './drag-drop';\n\nconst DRAG_DROP_DIRECTIVES = [\n  CdkDropList,\n  CdkDropListGroup,\n  CdkDrag,\n  CdkDragHandle,\n  CdkDragPreview,\n  CdkDragPlaceholder,\n];\n\n@NgModule({\n  imports: DRAG_DROP_DIRECTIVES,\n  exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n  providers: [DragDrop],\n})\nexport class DragDropModule {}\n\n// Re-export needed by the Angular compiler.\n// See: https://github.com/angular/components/issues/30663.\n// Note: These exports need to be stable and shouldn't be renamed unnecessarily because\n// consuming libraries might have references to them in their own partial compilation output.\nexport {CdkScrollable as ɵɵCdkScrollable} from '../scrolling';\n"], "names": ["activeCapturingEventOptions", "clamp"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA;AACM,SAAU,aAAa,CAAC,IAAiB,EAAA;IAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAgB;IACjD,MAAM,iBAAiB,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC;IACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;;AAG5C,IAAA,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC;AAE3B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC;;AAG5C,IAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,QAAA,kBAAkB,CAAC,IAAyB,EAAE,KAA0B,CAAC;;AACpE,SAAA,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,UAAU,EAAE;AACnF,QAAA,iBAAiB,CAAC,IAAwB,EAAE,KAAyB,CAAC;;IAGxE,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,kBAAkB,CAAC;IACvD,YAAY,CAAC,yBAAyB,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,CAAC;AACvE,IAAA,OAAO,KAAK;AACd;AAEA;AACA,SAAS,YAAY,CACnB,QAAgB,EAChB,IAAiB,EACjB,KAAkB,EAClB,QAAuC,EAAA;IAEvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAI,QAAQ,CAAC;AAE7D,IAAA,IAAI,kBAAkB,CAAC,MAAM,EAAE;QAC7B,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,CAAI,QAAQ,CAAC;AAEzD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClD,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;;;AAGvD;AAEA;AACA,IAAI,aAAa,GAAG,CAAC;AAErB;AACA,SAAS,iBAAiB,CACxB,MAAiC,EACjC,KAA4D,EAAA;;AAG5D,IAAA,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;AACzB,QAAA,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;;;;;IAM5B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACxC,KAAK,CAAC,IAAI,GAAG,CAAa,UAAA,EAAA,KAAK,CAAC,IAAI,CAAI,CAAA,EAAA,aAAa,EAAE,CAAA,CAAE;;AAE7D;AAEA;AACA,SAAS,kBAAkB,CAAC,MAAyB,EAAE,KAAwB,EAAA;IAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;IAEtC,IAAI,OAAO,EAAE;;;AAGX,QAAA,IAAI;YACF,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;QAC/B,MAAM;;AAEZ;;AC1EA;AACM,SAAU,oBAAoB,CAAC,OAAgB,EAAA;AACnD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE;;;;;IAM5C,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,GAAG;QACb,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,CAAC,EAAE,IAAI,CAAC,CAAC;KACC;AACd;AAEA;;;;;AAKG;SACa,kBAAkB,CAAC,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAA;IAC1E,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,GAAG,UAAU;AAC7C,IAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK;AAC3D;AAEA;;;;AAIG;AACa,SAAA,mBAAmB,CAAC,UAAmB,EAAE,SAAkB,EAAA;;IAEzE,MAAM,iBAAiB,GAAG,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;AAC1D,IAAA,MAAM,kBAAkB,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;;IAG9E,MAAM,gBAAgB,GAAG,SAAS,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;AACvD,IAAA,MAAM,mBAAmB,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM;AAEhF,IAAA,OAAO,iBAAiB,IAAI,kBAAkB,IAAI,gBAAgB,IAAI,mBAAmB;AAC3F;AAEA;;;;;AAKG;SACa,aAAa,CAC3B,OAOC,EACD,GAAW,EACX,IAAY,EAAA;AAEZ,IAAA,OAAO,CAAC,GAAG,IAAI,GAAG;IAClB,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;AAE7C,IAAA,OAAO,CAAC,IAAI,IAAI,IAAI;IACpB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK;AAC9C;AAEA;;;;;;AAMG;AACG,SAAU,oBAAoB,CAClC,IAAa,EACb,SAAiB,EACjB,QAAgB,EAChB,QAAgB,EAAA;AAEhB,IAAA,MAAM,EAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI;AACtD,IAAA,MAAM,UAAU,GAAG,KAAK,GAAG,SAAS;AACpC,IAAA,MAAM,UAAU,GAAG,MAAM,GAAG,SAAS;AAErC,IAAA,QACE,QAAQ,GAAG,GAAG,GAAG,UAAU;QAC3B,QAAQ,GAAG,MAAM,GAAG,UAAU;QAC9B,QAAQ,GAAG,IAAI,GAAG,UAAU;AAC5B,QAAA,QAAQ,GAAG,KAAK,GAAG,UAAU;AAEjC;;ACvFA;MACa,qBAAqB,CAAA;AAUZ,IAAA,SAAA;;AARX,IAAA,SAAS,GAAG,IAAI,GAAG,EAMzB;AAEH,IAAA,WAAA,CAAoB,SAAmB,EAAA;QAAnB,IAAS,CAAA,SAAA,GAAT,SAAS;;;IAG7B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;;AAIxB,IAAA,KAAK,CAAC,QAAgC,EAAA;QACpC,IAAI,CAAC,KAAK,EAAE;QACZ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE;AACjC,YAAA,cAAc,EAAE,IAAI,CAAC,yBAAyB,EAAE;AACjD,SAAA,CAAC;AAEF,QAAA,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAG;AACzB,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE;AAC1B,gBAAA,cAAc,EAAE,EAAC,GAAG,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,UAAU,EAAC;AAClE,gBAAA,UAAU,EAAE,oBAAoB,CAAC,OAAO,CAAC;AAC1C,aAAA,CAAC;AACJ,SAAC,CAAC;;;AAIJ,IAAA,YAAY,CAAC,KAAY,EAAA;AACvB,QAAA,MAAM,MAAM,GAAG,eAAe,CAAyB,KAAK,CAAE;QAC9D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;QAEjD,IAAI,CAAC,cAAc,EAAE;AACnB,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,cAAc,GAAG,cAAc,CAAC,cAAc;AACpD,QAAA,IAAI,MAAc;AAClB,QAAA,IAAI,OAAe;AAEnB,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE;AAC7B,YAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,EAAE;AAC/D,YAAA,MAAM,GAAG,sBAAsB,CAAC,GAAG;AACnC,YAAA,OAAO,GAAG,sBAAsB,CAAC,IAAI;;aAChC;AACL,YAAA,MAAM,GAAI,MAAsB,CAAC,SAAS;AAC1C,YAAA,OAAO,GAAI,MAAsB,CAAC,UAAU;;AAG9C,QAAA,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,GAAG,MAAM;AACjD,QAAA,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,GAAG,OAAO;;;QAIpD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAI;AACxC,YAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACnE,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,EAAE,cAAc,CAAC;;AAErE,SAAC,CAAC;AAEF,QAAA,cAAc,CAAC,GAAG,GAAG,MAAM;AAC3B,QAAA,cAAc,CAAC,IAAI,GAAG,OAAO;QAE7B,OAAO,EAAC,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,cAAc,EAAC;;AAGnD;;;;;AAKG;IACH,yBAAyB,GAAA;AACvB,QAAA,OAAO,EAAC,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,EAAC;;AAErD;;ACxFD;;;AAGG;AACa,SAAA,WAAW,CAAC,OAA6B,EAAE,SAAmB,EAAA;AAC5E,IAAA,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS;AAE3C,IAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,YAAY,EAAE;AAC9E,QAAA,OAAO,SAAS,CAAC,CAAC,CAAgB;;IAGpC,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9C,IAAA,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACpD,IAAA,OAAO,OAAO;AAChB;;ACNA;;;;AAIG;SACa,YAAY,CAC1B,IAAyB,EACzB,MAA8B,EAC9B,mBAAiC,EAAA;AAEjC,IAAA,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AACtB,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC9B,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC;YAEzB,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;;iBACzE;AACL,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;;;;AAK9B,IAAA,OAAO,IAAI;AACb;AAEA;;;;;AAKG;AACa,SAAA,4BAA4B,CAAC,OAAoB,EAAE,MAAe,EAAA;IAChF,MAAM,UAAU,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM;AAEvC,IAAA,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE;QAC1B,cAAc,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM;QACpC,mBAAmB,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM;QACzC,6BAA6B,EAAE,MAAM,GAAG,EAAE,GAAG,aAAa;AAC1D,QAAA,aAAa,EAAE,UAAU;AACzB,QAAA,iBAAiB,EAAE,UAAU;AAC7B,QAAA,qBAAqB,EAAE,UAAU;AACjC,QAAA,kBAAkB,EAAE,UAAU;AAC/B,KAAA,CAAC;AACJ;AAEA;;;;;;AAMG;SACa,gBAAgB,CAC9B,OAAoB,EACpB,MAAe,EACf,mBAAiC,EAAA;AAEjC,IAAA,YAAY,CACV,OAAO,CAAC,KAAK,EACb;QACE,QAAQ,EAAE,MAAM,GAAG,EAAE,GAAG,OAAO;QAC/B,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG,GAAG;QACtB,OAAO,EAAE,MAAM,GAAG,EAAE,GAAG,GAAG;QAC1B,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,QAAQ;KAC7B,EACD,mBAAmB,CACpB;AACH;AAEA;;;AAGG;AACa,SAAA,iBAAiB,CAAC,SAAiB,EAAE,gBAAyB,EAAA;AAC5E,IAAA,OAAO,gBAAgB,IAAI,gBAAgB,IAAI;AAC7C,UAAE,SAAS,GAAG,GAAG,GAAG;UAClB,SAAS;AACf;AAEA;;;;AAIG;AACa,SAAA,gBAAgB,CAAC,MAAmB,EAAE,UAAmB,EAAA;IACvE,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,UAAU,CAAC,KAAK,CAAA,EAAA,CAAI;IAC5C,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAA,EAAA,CAAI;AAC9C,IAAA,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC;AACxE;AAEA;;;;AAIG;AACa,SAAA,YAAY,CAAC,CAAS,EAAE,CAAS,EAAA;;;AAG/C,IAAA,OAAO,CAAe,YAAA,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAO,IAAA,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ;AACjE;;AC7GA;AACA,SAAS,qBAAqB,CAAC,KAAa,EAAA;;IAE1C,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;AACpE,IAAA,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU;AACvC;AAEA;AACM,SAAU,kCAAkC,CAAC,OAAoB,EAAA;AACrE,IAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;IAC/C,MAAM,sBAAsB,GAAG,qBAAqB,CAAC,aAAa,EAAE,qBAAqB,CAAC;AAC1F,IAAA,MAAM,QAAQ,GAAG,sBAAsB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,CAAC;;IAG5F,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,OAAO,CAAC;;;;IAKV,MAAM,aAAa,GAAG,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC9D,MAAM,YAAY,GAAG,qBAAqB,CAAC,aAAa,EAAE,qBAAqB,CAAC;IAChF,MAAM,SAAS,GAAG,qBAAqB,CAAC,aAAa,EAAE,kBAAkB,CAAC;AAE1E,IAAA,QACE,qBAAqB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAClD,QAAA,qBAAqB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAEnD;AAEA;AACA,SAAS,qBAAqB,CAAC,aAAkC,EAAE,IAAY,EAAA;IAC7E,MAAM,KAAK,GAAG,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAClD,IAAA,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAClD;;ACdA;AACA,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;;IAElC,UAAU;AACX,CAAA,CAAC;MAEW,UAAU,CAAA;AAYX,IAAA,SAAA;AACA,IAAA,YAAA;AACA,IAAA,UAAA;AACA,IAAA,eAAA;AACA,IAAA,gBAAA;AACA,IAAA,aAAA;AACA,IAAA,qBAAA;AAIA,IAAA,iBAAA;AACA,IAAA,OAAA;AACA,IAAA,SAAA;;AAtBF,IAAA,oBAAoB;;AAGpB,IAAA,QAAQ;AAEhB,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;AAGtB,IAAA,WAAA,CACU,SAAmB,EACnB,YAAyB,EACzB,UAAqB,EACrB,eAAwB,EACxB,gBAA4C,EAC5C,aAAuC,EACvC,qBAGP,EACO,iBAAgC,EAChC,OAAe,EACf,SAAoB,EAAA;QAZpB,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAe,CAAA,eAAA,GAAf,eAAe;QACf,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAChB,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAqB,CAAA,qBAAA,GAArB,qBAAqB;QAIrB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAS,CAAA,SAAA,GAAT,SAAS;;AAGnB,IAAA,MAAM,CAAC,MAAmB,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE;AACrC,QAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;;;AAIjC,QAAA,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;;;IAIlC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACtB,QAAA,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE;QACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAK;;AAGnD,IAAA,YAAY,CAAC,KAAa,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK;;IAGvC,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;;AAG9C,IAAA,QAAQ,CAAC,SAAiB,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;;IAGxC,qBAAqB,GAAA;AACnB,QAAA,OAAO,kCAAkC,CAAC,IAAI,CAAC,QAAQ,CAAC;;IAG1D,gBAAgB,CAAC,IAAY,EAAE,OAA6B,EAAA;AAC1D,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC;;IAGpD,cAAc,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB;AAC3C,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;AACvC,QAAA,MAAM,eAAe,GAAG,aAAa,GAAG,aAAa,CAAC,QAAQ,GAAG,IAAI;AACrE,QAAA,IAAI,OAAoB;AAExB,QAAA,IAAI,eAAe,IAAI,aAAa,EAAE;;;AAGpC,YAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI;AACtE,YAAA,MAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,kBAAkB,CAC5D,eAAe,EACf,aAAa,CAAC,OAAO,CACtB;YACD,OAAO,CAAC,aAAa,EAAE;YACvB,OAAO,GAAG,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;AAC9C,YAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO;AACnC,YAAA,IAAI,aAAa,CAAC,SAAS,EAAE;AAC3B,gBAAA,gBAAgB,CAAC,OAAO,EAAE,QAAS,CAAC;;iBAC/B;AACL,gBAAA,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CACpC,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAC5B,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAC7B;;;aAEE;AACL,YAAA,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC;AAC1C,YAAA,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAgB,CAAC;AAEhD,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB;;;AAIpD,QAAA,YAAY,CACV,OAAO,CAAC,KAAK,EACb;;;AAGE,YAAA,gBAAgB,EAAE,MAAM;;;;;;AAMxB,YAAA,QAAQ,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,YAAY,GAAG,GAAG;AACvD,YAAA,UAAU,EAAE,OAAO;AACnB,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,MAAM,EAAE,GAAG;AACX,YAAA,SAAS,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE;SAC7B,EACD,mBAAmB,CACpB;AAED,QAAA,4BAA4B,CAAC,OAAO,EAAE,KAAK,CAAC;AAC5C,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACzC,QAAA,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC;QACzC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC;QAE5C,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AAC/B,gBAAA,YAAY,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;;iBAC9D;AACL,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;;;AAIvC,QAAA,OAAO,OAAO;;AAEjB;AAED;AACA,SAAS,eAAe,CAAC,OAAoB,EAAA;IAC3C,OAAO,aAAa,IAAI,OAAO;AACjC;;ACzGA;AACA,MAAM,2BAA2B,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC;AAEnD;AACA,MAAM,0BAA0B,GAAG,EAAC,OAAO,EAAE,KAAK,EAAC;AAEnD;AACA,MAAMA,6BAA2B,GAAG;AAClC,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,OAAO,EAAE,IAAI;CACd;AAED;;;;;AAKG;AACH,MAAM,uBAAuB,GAAG,GAAG;AAEnC;AACA,MAAM,iBAAiB,GAAG,sBAAsB;AAkBhD;AACA,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC;;IAEtC,UAAU;AACX,CAAA,CAAC;AAgBF;;AAEG;MACU,OAAO,CAAA;AAiQR,IAAA,OAAA;AACA,IAAA,SAAA;AACA,IAAA,OAAA;AACA,IAAA,cAAA;AACA,IAAA,iBAAA;AACA,IAAA,SAAA;AArQF,IAAA,oBAAoB;AACpB,IAAA,6BAA6B;;AAG7B,IAAA,QAAQ;;AAGR,IAAA,iBAAiB;;AAGjB,IAAA,eAAe;;AAGf,IAAA,YAAY;;AAGZ,IAAA,wBAAwB;;AAGxB,IAAA,qBAAqB;AAE7B;;;AAGG;AACK,IAAA,OAAO;AAEf;;AAEG;IACK,OAAO,GAAuB,IAAI;AAE1C;;;;;AAKG;IACK,iBAAiB,GAAU,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;;IAGvC,gBAAgB,GAAU,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;;AAGtC,IAAA,iBAAiB;AAEzB;;;AAGG;AACK,IAAA,mBAAmB,GAAG,MAAM,CAAC,KAAK,CAAC;;AAGnC,IAAA,SAAS;;AAGT,IAAA,iBAAiB;;AAGjB,IAAA,aAAa;;AAGb,IAAA,gBAAgB;;AAGP,IAAA,WAAW,GAAG,IAAI,OAAO,EAMtC;;AAGI,IAAA,sBAAsB;;AAGtB,IAAA,qCAAqC;;AAGrC,IAAA,yBAAyB;AAEjC;;;AAGG;AACK,IAAA,YAAY;AAEpB;;AAEG;AACK,IAAA,gBAAgB;AAExB;;;AAGG;AACK,IAAA,wBAAwB;;AAGxB,IAAA,wBAAwB,GAAG,YAAY,CAAC,KAAK;;AAG7C,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK;;AAG3C,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;;AAGxC,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AAEhD;;;;AAIG;AACK,IAAA,mBAAmB;;AAGnB,IAAA,cAAc;;IAGd,gBAAgB,GAAuB,IAAI;;IAG3C,0BAA0B,GAAG,IAAI;;AAGjC,IAAA,eAAe;;AAGf,IAAA,YAAY;;AAGZ,IAAA,aAAa;;AAGb,IAAA,gBAAgB;;AAGhB,IAAA,oBAAoB;;IAGpB,QAAQ,GAAkB,EAAE;;AAG5B,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAAe;;AAGzC,IAAA,cAAc;;IAGd,UAAU,GAAc,KAAK;;AAG7B,IAAA,cAAc;AAEtB;;;;AAIG;AACK,IAAA,iBAAiB;;AAGzB,IAAA,QAAQ;AAER;;;AAGG;IACH,cAAc,GAA4C,CAAC;;AAG3D,IAAA,YAAY;AAEZ;;;AAGG;IACH,KAAK,GAAW,CAAC;;AAGjB,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;;IAElF,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC,6BAA6B,EAAE;AACpC,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;;;IAGxE,SAAS,GAAG,KAAK;;AAGhB,IAAA,aAAa,GAAG,IAAI,OAAO,EAAQ;;AAGnC,IAAA,OAAO,GAAG,IAAI,OAAO,EAAqD;;AAG1E,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAqD;;AAG3E,IAAA,KAAK,GAAG,IAAI,OAAO,EAKxB;;AAGK,IAAA,OAAO,GAAG,IAAI,OAAO,EAAiE;;AAGtF,IAAA,MAAM,GAAG,IAAI,OAAO,EAA2C;;AAG/D,IAAA,OAAO,GAAG,IAAI,OAAO,EAU1B;AAEJ;;;AAGG;AACM,IAAA,KAAK,GAMT,IAAI,CAAC,WAAW;;AAGrB,IAAA,IAAI;AAEJ;;;;;AAKG;AACH,IAAA,iBAAiB;AAEjB,IAAA,WAAA,CACE,OAA8C,EACtC,OAAsB,EACtB,SAAmB,EACnB,OAAe,EACf,cAA6B,EAC7B,iBAAmC,EACnC,SAAoB,EAAA;QALpB,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAS,CAAA,SAAA,GAAT,SAAS;AAEjB,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC;QACvE,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,SAAS,CAAC;AAC5D,QAAA,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC;;AAG1C;;;AAGG;IACH,qBAAqB,GAAA;QACnB,OAAO,IAAI,CAAC,YAAY;;;IAI1B,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,YAAY;;AAG1B;;;AAGG;IACH,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE;;;AAIjF,IAAA,WAAW,CAAC,OAAkD,EAAA;AAC5D,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpF,IAAI,CAAC,6BAA6B,EAAE;;;;;AAMpC,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAe;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,IAAG;AACrC,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AACtC,gBAAA,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC;;AAE/B,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe;AACvC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,mBAAmB,CAAC,QAAoC,EAAA;AACtD,QAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ;AAChC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,uBAAuB,CAAC,QAAmC,EAAA;AACzD,QAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ;AACpC,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;AACH,IAAA,eAAe,CAAC,WAAkD,EAAA;AAChE,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC;AAE1C,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,YAAY,EAAE;YACjC,IAAI,CAAC,2BAA2B,EAAE;AAClC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS;YAC/B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM;AAC/D,gBAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,0BAA0B,CAAC;AACpF,gBAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,2BAA2B,CAAC;AACtF,gBAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,0BAA0B,CAAC;AACzF,aAAA,CAAC;AACF,YAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS;AAClC,YAAA,IAAI,CAAC,YAAY,GAAG,OAAO;;QAG7B,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,YAAY,UAAU,EAAE;YAChF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe;;AAG3D,QAAA,OAAO,IAAI;;AAGb;;AAEG;AACH,IAAA,mBAAmB,CAAC,eAA6D,EAAA;AAC/E,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI;AAC/E,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;QACtC,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;iBAC7B,MAAM,CAAC,EAAE;iBACT,SAAS,CAAC,MAAM,IAAI,CAAC,8BAA8B,EAAE,CAAC;;AAE3D,QAAA,OAAO,IAAI;;;AAIb,IAAA,UAAU,CAAC,MAA+B,EAAA;AACxC,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM;AAC5B,QAAA,OAAO,IAAI;;;IAIb,OAAO,GAAA;QACL,IAAI,CAAC,2BAA2B,EAAE;;;AAIlC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;AAGrB,YAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE;;AAG7B,QAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;QACtB,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,mBAAmB,EAAE;AAC1B,QAAA,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AAC7B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxB,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACrB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;AAC3B,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;AAClB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AAC7B,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS;AAC/B,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AAC7B,QAAA,IAAI,CAAC,gBAAgB;AACnB,YAAA,IAAI,CAAC,YAAY;AACjB,gBAAA,IAAI,CAAC,gBAAgB;AACrB,oBAAA,IAAI,CAAC,oBAAoB;AACzB,wBAAA,IAAI,CAAC,gBAAgB;AACrB,4BAAA,IAAI,CAAC,OAAO;AACZ,gCAAA,IAAI,CAAC,cAAc;AACjB,oCAAA,IAAK;;;IAIX,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;;;IAI9E,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE;AAChE,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;AACpC,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;;;IAIvC,eAAe,GAAA;AACb,QAAA;;AAEE,QAAA,IAAI,CAAC,gBAAgB;AACrB,YAAA,IAAI,CAAC,YAAY;;AAEjB,YAAA,mBAAmB,CACjB,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,EAC7C,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAC1C,EACD;YACA,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;YAChE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE;YAE3D,IAAI,OAAO,GAAG,CAAC;YACf,IAAI,OAAO,GAAG,CAAC;;YAGf,IAAI,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE;gBACpC,OAAO,GAAG,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;;iBACrC,IAAI,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,EAAE;gBAC7C,OAAO,GAAG,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;;;YAI9C,IAAI,SAAS,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,OAAO,GAAG,UAAU,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;;iBACnC,IAAI,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE;gBAC/C,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;;AAGhD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC3C,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE1C,IAAI,CAAC,GAAG,WAAW,GAAG,OAAO,EAC3B,CAAC,GAAG,UAAU,GAAG,OAAO;AAE1B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAC;YAC9B,IAAI,CAAC,iBAAiB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAC;;;AAInC;;;AAGG;AACH,IAAA,aAAa,CAAC,MAAmB,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AAC5E,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC;AACjC,YAAA,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC;;;AAI9C;;;AAGG;AACH,IAAA,YAAY,CAAC,MAAmB,EAAA;QAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC;AACpC,YAAA,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC;;;;AAKvD,IAAA,aAAa,CAAC,SAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,QAAA,OAAO,IAAI;;;AAIb,IAAA,kBAAkB,CAAC,SAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS;;AAGjC;;AAEG;IACH,mBAAmB,GAAA;AACjB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;AACnF,QAAA,OAAO,EAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAC;;AAGvC;;;AAGG;AACH,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAElC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;AAGnD,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,oBAAoB,CAAC,KAAuB,EAAA;AAC1C,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;AAC9B,QAAA,OAAO,IAAI;;;IAIb,4BAA4B,GAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB;AAE/C,QAAA,IAAI,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;AACnC,YAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC;;;;IAKpF,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE;AAC3C,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;AACzC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,6BAA6B,IAAI;AACtC,QAAA,IAAI,CAAC,6BAA6B,GAAG,SAAS;;;IAIxC,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;;IAId,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;AACtB,QAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE;AAC3B,QAAA,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,GAAG,IAAK;;;AAIzD,IAAA,YAAY,GAAG,CAAC,KAA8B,KAAI;AACxD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;AAGzB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;AAEjD,YAAA,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC9E,gBAAA,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,CAAC;;;AAE9C,aAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACzB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;;AAE1D,KAAC;;AAGO,IAAA,YAAY,GAAG,CAAC,KAA8B,KAAI;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;AAE7D,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE;AAC/B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAC5E,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAC5E,MAAM,eAAe,GAAG,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB;;;;;YAMhF,IAAI,eAAe,EAAE;AACnB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACzF,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc;gBAErC,IAAI,CAAC,cAAc,EAAE;AACnB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBAC5B;;;;;AAMF,gBAAA,IAAI,CAAC,SAAS,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE;;;AAGvE,oBAAA,IAAI,KAAK,CAAC,UAAU,EAAE;wBACpB,KAAK,CAAC,cAAc,EAAE;;AAExB,oBAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;AAClC,oBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;;;YAI1D;;;;;AAMF,QAAA,IAAI,KAAK,CAAC,UAAU,EAAE;YACpB,KAAK,CAAC,cAAc,EAAE;;QAGxB,MAAM,0BAA0B,GAAG,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC;AACvF,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,yBAAyB,GAAG,eAAe;AAChD,QAAA,IAAI,CAAC,4BAA4B,CAAC,0BAA0B,CAAC;AAE7D,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,0BAA0B,CAAC,0BAA0B,EAAE,eAAe,CAAC;;aACvE;;;AAGL,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAgB,GAAG,IAAI,CAAC,qBAAqB;AAC1F,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;AAC7C,YAAA,eAAe,CAAC,CAAC,GAAG,0BAA0B,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACtF,YAAA,eAAe,CAAC,CAAC,GAAG,0BAA0B,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtF,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC;;;;;QAMvE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACpB,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,eAAe,EAAE,0BAA0B;oBAC3C,KAAK;AACL,oBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC;oBAC3D,KAAK,EAAE,IAAI,CAAC,sBAAsB;AACnC,iBAAA,CAAC;AACJ,aAAC,CAAC;;AAEN,KAAC;;AAGO,IAAA,UAAU,GAAG,CAAC,KAA8B,KAAI;AACtD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;AAC9B,KAAC;AAED;;;AAGG;AACK,IAAA,gBAAgB,CAAC,KAA8B,EAAA;;;;;QAKrD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YAC5C;;QAGF,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC;QACzC,IAAI,CAAC,6BAA6B,EAAE;AAEpC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAiC,CAAC,uBAAuB;gBAC1E,IAAI,CAAC,wBAAwB;;AAGjC,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC/B;;AAGF,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;;AAEvB,YAAA,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;AACpC,YAAA,IAAI,CAAC,4BAA4B,EAAE,CAAC,IAAI,CAAC,MAAK;AAC5C,gBAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;gBACjC,IAAI,CAAC,wBAAwB,EAAE;AAC/B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC;AAC3C,aAAC,CAAC;;aACG;;;;YAIL,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAClD,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;YAC7D,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAClD,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACd,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;AAChD,oBAAA,SAAS,EAAE,eAAe;oBAC1B,KAAK;AACN,iBAAA,CAAC;AACJ,aAAC,CAAC;YACF,IAAI,CAAC,wBAAwB,EAAE;AAC/B,YAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC;;;;AAKrC,IAAA,kBAAkB,CAAC,KAA8B,EAAA;AACvD,QAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACvB,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE;;QAGvC,IAAI,CAAC,6BAA6B,EAAE;;AAGpC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE;AACxC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;QAEzC,IAAI,UAAU,EAAE;;;AAGd,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACxD,UAAU,EACV,aAAa,EACb,oBAAoB,EACpBA,6BAA2B,CAC5B;AACH,aAAC,CAAC;;QAGJ,IAAI,aAAa,EAAE;AACjB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY;AACjC,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,UAAyB;AAChD,YAAA,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAC1E,YAAA,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO;AAC1B,gBAAA,IAAI,CAAC,OAAO;oBACZ,IAAI,CAAC,SAAS,CAAC,aAAa,CAC1B,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,iBAAiB,GAAG,EAAE,CACvE,CAAC;;AAGJ,YAAA,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;;;YAIpC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE;;;YAItD,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAC5B,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,eAAgB,EACrB,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAC7B,IAAI,CAAC,YAAY,IAAI,IAAI,EACzB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,EAC3B,IAAI,CAAC,SAAS,CACf;AACD,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;;;;AAKxE,YAAA,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,uBAAuB,CAAC;AACzD,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;YACzC,aAAa,CAAC,KAAK,EAAE;AACrB,YAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa;YACtC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC;;aAChD;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;YACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,GAAG,SAAU;;;;AAK1D,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC;;AAGxF;;;;;AAKG;IACK,uBAAuB,CAAC,gBAA6B,EAAE,KAA8B,EAAA;;;AAG3F,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,KAAK,CAAC,eAAe,EAAE;;AAGzB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;AACpC,QAAA,MAAM,eAAe,GAAG,YAAY,CAAC,KAAK,CAAC;QAC3C,MAAM,sBAAsB,GAAG,CAAC,eAAe,IAAK,KAAoB,CAAC,MAAM,KAAK,CAAC;AACrF,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY;AACrC,QAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;QACrC,MAAM,gBAAgB,GACpB,CAAC,eAAe;AAChB,YAAA,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,mBAAmB,GAAG,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE;QACjE,MAAM,WAAW,GAAG;AAClB,cAAE,gCAAgC,CAAC,KAAmB;AACtD,cAAE,+BAA+B,CAAC,KAAmB,CAAC;;;;;;;AAQxD,QAAA,IAAI,MAAM,IAAK,MAAsB,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;YAC7E,KAAK,CAAC,cAAc,EAAE;;;QAIxB,IAAI,UAAU,IAAI,sBAAsB,IAAI,gBAAgB,IAAI,WAAW,EAAE;YAC3E;;;;;AAMF,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACxB,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,KAAgC;YAC/D,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC,uBAAuB,IAAI,EAAE;AACxE,YAAA,UAAU,CAAC,uBAAuB,GAAG,aAAa;;AAGpD,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;;;QAI5C,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE;AAChE,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;AAC/F,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AACzF,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAC7B,aAAA,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;AAC9B,aAAA,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAE9D,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;;;;AAMlE,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;AAC7C,QAAA,IAAI,CAAC,wBAAwB;YAC3B,eAAe,IAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC;kBAC5D,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACb,kBAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,EAAE,KAAK,CAAC;AACtF,QAAA,MAAM,eAAe,IAClB,IAAI,CAAC,qBAAqB;AAC3B,YAAA,IAAI,CAAC,yBAAyB;AAC5B,gBAAA,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,sBAAsB,GAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;AAC1C,QAAA,IAAI,CAAC,qCAAqC,GAAG,EAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAC;AACzF,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE;QAChC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;;;AAI3C,IAAA,qBAAqB,CAAC,KAA8B,EAAA;;;;;QAK1D,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,uBAAuB,CAAC;AAClE,QAAA,IAAI,CAAC,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC;QAEtE,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,mBAAmB,EAAE;AAC1B,QAAA,IAAI,CAAC,eAAe;AAClB,YAAA,IAAI,CAAC,aAAa;AAClB,gBAAA,IAAI,CAAC,YAAY;AACjB,oBAAA,IAAI,CAAC,iBAAiB;AACpB,wBAAA,SAAS;;AAGb,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAe;YACtC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC;YACjD,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;YAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;AACvD,YAAA,MAAM,sBAAsB,GAAG,SAAS,CAAC,gBAAgB,CACvD,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,CAClB;AAED,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,KAAK,EAAC,CAAC;AAC5E,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,IAAI,EAAE,IAAI;gBACV,YAAY;gBACZ,aAAa,EAAE,IAAI,CAAC,aAAa;AACjC,gBAAA,SAAS,EAAE,SAAS;gBACpB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;gBACzC,sBAAsB;gBACtB,QAAQ;AACR,gBAAA,SAAS,EAAE,eAAe;gBAC1B,KAAK;AACN,aAAA,CAAC;YACF,SAAS,CAAC,IAAI,CACZ,IAAI,EACJ,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,iBAAiB,EACtB,sBAAsB,EACtB,QAAQ,EACR,eAAe,EACf,KAAK,CACN;AACD,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB;AAC9C,SAAC,CAAC;;AAGJ;;;AAGG;AACK,IAAA,0BAA0B,CAAC,EAAC,CAAC,EAAE,CAAC,EAAQ,EAAE,EAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAQ,EAAA;;AAEzE,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;;;;;AAMtF,QAAA,IACE,CAAC,YAAY;AACb,YAAA,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,iBAAiB;YAC9C,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,EAC7C;AACA,YAAA,YAAY,GAAG,IAAI,CAAC,iBAAiB;;QAGvC,IAAI,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,cAAc,EAAE;AACxD,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;gBACpB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAe,CAAC,YAAY,CAAC,IAAI,CAAC;AACzD,gBAAA,MAAM,eAAe,GACnB,IAAI,CAAC,cAAe,CAAC,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,iBAAiB,EAAE,IAAI,IAAI;;AAGjF,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,cAAe,EAAC,CAAC;AAC/D,gBAAA,IAAI,CAAC,cAAe,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC/B,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,IAAI,CAAC,cAAe,EAAE,eAAe,CAAC;;AAEpF,gBAAA,IAAI,CAAC,cAAc,GAAG,YAAa;gBACnC,IAAI,CAAC,cAAc,CAAC,KAAK,CACvB,IAAI,EACJ,CAAC,EACD,CAAC;;;AAGD,gBAAA,YAAY,KAAK,IAAI,CAAC,iBAAiB,IAAI,YAAY,CAAC;sBACpD,IAAI,CAAC;sBACL,SAAS,CACd;AACD,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,SAAS,EAAE,YAAa;AACxB,oBAAA,YAAY,EAAE,YAAa,CAAC,YAAY,CAAC,IAAI,CAAC;AAC/C,iBAAA,CAAC;AACJ,aAAC,CAAC;;;AAIJ,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,IAAI,CAAC,cAAe,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3D,YAAA,IAAI,CAAC,cAAe,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC;AAEvE,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,gBAAA,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC;;iBAC5B;AACL,gBAAA,IAAI,CAAC,sBAAsB,CACzB,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EACnC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CACpC;;;;AAKP;;;AAGG;IACK,4BAA4B,GAAA;;AAElC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,OAAO,CAAC,OAAO,EAAE;;QAG1B,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE;;AAGjE,QAAA,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC;;QAG7C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC;;;;;QAMtE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAS,CAAC,qBAAqB,EAAE;AAEvD,QAAA,IAAI,QAAQ,KAAK,CAAC,EAAE;AAClB,YAAA,OAAO,OAAO,CAAC,OAAO,EAAE;;AAG1B,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACzC,YAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;AAC3B,gBAAA,MAAM,OAAO,GAAG,CAAC,KAAsB,KAAI;AACzC,oBAAA,IACE,CAAC,KAAK;yBACL,IAAI,CAAC,QAAQ;4BACZ,eAAe,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO;AAChD,4BAAA,KAAK,CAAC,YAAY,KAAK,WAAW,CAAC,EACrC;AACA,wBAAA,eAAe,EAAE;AACjB,wBAAA,OAAO,EAAE;wBACT,YAAY,CAAC,OAAO,CAAC;;AAEzB,iBAAC;;;;gBAKD,MAAM,OAAO,GAAG,UAAU,CAAC,OAAmB,EAAE,QAAQ,GAAG,GAAG,CAAC;AAC/D,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,QAAS,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC;AACnF,aAAC,CAAC;AACJ,SAAC,CAAC;;;IAII,yBAAyB,GAAA;AAC/B,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB;AACnD,QAAA,MAAM,mBAAmB,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,GAAG,IAAI;AACjF,QAAA,IAAI,WAAwB;QAE5B,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,iBAAkB,CAAC,aAAa,CAAC,kBAAkB,CACxE,mBAAmB,EACnB,iBAAkB,CAAC,OAAO,CAC3B;AACD,YAAA,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YACpC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC;;aAC1D;AACL,YAAA,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC;;;;AAKhD,QAAA,WAAW,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM;AACxC,QAAA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC;AAC5C,QAAA,OAAO,WAAW;;AAGpB;;;;AAIG;AACK,IAAA,4BAA4B,CAClC,WAAoB,EACpB,gBAA6B,EAC7B,KAA8B,EAAA;AAE9B,QAAA,MAAM,aAAa,GAAG,gBAAgB,KAAK,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,gBAAgB;AACtF,QAAA,MAAM,aAAa,GAAG,aAAa,GAAG,aAAa,CAAC,qBAAqB,EAAE,GAAG,WAAW;AACzF,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK;AAClE,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,EAAE;AACxD,QAAA,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI;AAChE,QAAA,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG;QAE9D,OAAO;YACL,CAAC,EAAE,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG,CAAC;YAC5C,CAAC,EAAE,aAAa,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;SAC3C;;;AAIK,IAAA,yBAAyB,CAAC,KAA8B,EAAA;AAC9D,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,EAAE;AACxD,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK;AAC9B;;;;;;;gBAOE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;cAClE,KAAK;QAET,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI;QAC3C,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,GAAG;;;AAI1C,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE;YACtD,IAAI,SAAS,EAAE;gBACb,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE;AACvD,gBAAA,QAAQ,CAAC,CAAC,GAAG,CAAC;AACd,gBAAA,QAAQ,CAAC,CAAC,GAAG,CAAC;gBACd,OAAO,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;;;AAIxD,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC;;;AAIP,IAAA,8BAA8B,CAAC,KAAY,EAAA;AACjD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI;QACnF,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC;AAChB,cAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,eAAgB,EAAE,IAAI,CAAC,wBAAwB;cACxF,KAAK;QAET,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,iBAAiB,KAAK,GAAG,EAAE;YACtD,CAAC;gBACC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5B,qBAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC;;aAC3D,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,iBAAiB,KAAK,GAAG,EAAE;YAC7D,CAAC;gBACC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC5B,qBAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC;;AAGlE,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;;;AAGtB,YAAA,MAAM,EAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAC,GAAG,CAAC,IAAI,CAAC;kBACnC,IAAI,CAAC;kBACL,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;AAEhB,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;AACvC,YAAA,MAAM,EAAC,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAC,GAAG,IAAI,CAAC,eAAe,EAAE;AAC3E,YAAA,MAAM,IAAI,GAAG,YAAY,CAAC,GAAG,GAAG,OAAO;YACvC,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG,OAAO,CAAC;AAC5D,YAAA,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,GAAG,OAAO;YACxC,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,IAAI,YAAY,GAAG,OAAO,CAAC;YAE1D,CAAC,GAAGC,OAAK,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,GAAGA,OAAK,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;;AAG1B,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC;;;AAIP,IAAA,4BAA4B,CAAC,qBAA4B,EAAA;AAC/D,QAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,qBAAqB;AACpC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB;AACzC,QAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,qCAAqC;;AAG1E,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;AACvD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;;;;;QAMvD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,+BAA+B,EAAE;AAC1D,YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChD,YAAA,uBAAuB,CAAC,CAAC,GAAG,CAAC;;QAG/B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,+BAA+B,EAAE;AAC1D,YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChD,YAAA,uBAAuB,CAAC,CAAC,GAAG,CAAC;;AAG/B,QAAA,OAAO,KAAK;;;IAIN,6BAA6B,GAAA;QACnC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACxC;;AAGF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAEnE,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,0BAA0B,EAAE;AACpD,YAAA,IAAI,CAAC,0BAA0B,GAAG,YAAY;AAC9C,YAAA,4BAA4B,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC;;;;IAKzD,2BAA2B,GAAA;AACjC,QAAA,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACxD,QAAA,IAAI,CAAC,oBAAoB,GAAG,SAAS;;AAGvC;;;;AAIG;IACK,0BAA0B,CAAC,CAAS,EAAE,CAAS,EAAA;AACrD,QAAA,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK;AAC5B,QAAA,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;AACpD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK;;;;AAKtC,QAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAAE;AAClC,YAAA,IAAI,CAAC,iBAAiB;AACpB,gBAAA,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,GAAG,EAAE;;;;;QAM1E,MAAM,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC;;AAGzE;;;;AAIG;IACK,sBAAsB,CAAC,CAAS,EAAE,CAAS,EAAA;;;AAGjD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC,iBAAiB;QAC7F,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,QAAS,CAAC,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;;AAG7E;;;AAGG;AACK,IAAA,gBAAgB,CAAC,eAAsB,EAAA;AAC7C,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB;QAEjD,IAAI,cAAc,EAAE;YAClB,OAAO,EAAC,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAC;;QAG3F,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;;;IAIb,wBAAwB,GAAA;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS;AAClD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;;AAG/B;;;AAGG;IACK,8BAA8B,GAAA;QACpC,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,iBAAiB;QAEnC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACvE;;;QAIF,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;;;AAIlE,QAAA,IACE,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;AACtD,aAAC,WAAW,CAAC,KAAK,KAAK,CAAC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,EACrD;YACA;;QAGF,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI;QACzD,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK;QAC5D,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG;QACtD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM;;;QAI/D,IAAI,YAAY,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE;AAC1C,YAAA,IAAI,YAAY,GAAG,CAAC,EAAE;gBACpB,CAAC,IAAI,YAAY;;AAGnB,YAAA,IAAI,aAAa,GAAG,CAAC,EAAE;gBACrB,CAAC,IAAI,aAAa;;;aAEf;YACL,CAAC,GAAG,CAAC;;;;QAKP,IAAI,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE;AAC5C,YAAA,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,CAAC,IAAI,WAAW;;AAGlB,YAAA,IAAI,cAAc,GAAG,CAAC,EAAE;gBACtB,CAAC,IAAI,cAAc;;;aAEhB;YACL,CAAC,GAAG,CAAC;;AAGP,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE;YACpE,IAAI,CAAC,mBAAmB,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC;;;;AAK5B,IAAA,kBAAkB,CAAC,KAA8B,EAAA;AACvD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc;AAEjC,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,KAAK;;AACP,aAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO,KAAK,CAAC,KAAK;;QAGpB,OAAO,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC;;;AAIxB,IAAA,eAAe,CAAC,KAAY,EAAA;QAClC,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC;QAElE,IAAI,gBAAgB,EAAE;AACpB,YAAA,MAAM,MAAM,GAAG,eAAe,CAAyB,KAAK,CAAE;;;YAI9D,IACE,IAAI,CAAC,aAAa;gBAClB,MAAM,KAAK,IAAI,CAAC,gBAAgB;gBAChC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,EACtC;AACA,gBAAA,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,CAAC;;YAGhF,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,gBAAgB,CAAC,IAAI;YACrD,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,gBAAgB,CAAC,GAAG;;;AAIpD,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,gBAAgB,CAAC,IAAI;gBAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,gBAAgB,CAAC,GAAG;AAC/C,gBAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;;;IAM/E,0BAA0B,GAAA;AAChC,QAAA,QACE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,cAAc;AACnE,YAAA,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,EAAE;;AAIrD;;;;;AAKG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACxC,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;;QAG5D,OAAO,IAAI,CAAC,iBAAiB;;;IAIvB,yBAAyB,CAC/B,aAA0B,EAC1B,UAA6B,EAAA;AAE7B,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,IAAI,QAAQ;AAE3D,QAAA,IAAI,gBAAgB,KAAK,QAAQ,EAAE;AACjC,YAAA,OAAO,aAAa;;AAGtB,QAAA,IAAI,gBAAgB,KAAK,QAAQ,EAAE;AACjC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS;;;;AAKlC,YAAA,QACE,UAAU;AACV,gBAAA,WAAW,CAAC,iBAAiB;AAC5B,gBAAA,WAAmB,CAAC,uBAAuB;AAC3C,gBAAA,WAAmB,CAAC,oBAAoB;AACxC,gBAAA,WAAmB,CAAC,mBAAmB;gBACxC,WAAW,CAAC,IAAI;;AAIpB,QAAA,OAAO,aAAa,CAAC,gBAAgB,CAAC;;;IAIhC,eAAe,GAAA;;;QAGrB,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;AACjF,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACvB,kBAAE,IAAI,CAAC,QAAS,CAAC,qBAAqB;AACtC,kBAAE,IAAI,CAAC,eAAgB;;QAG3B,OAAO,IAAI,CAAC,YAAY;;;AAIlB,IAAA,gBAAgB,GAAG,CAAC,KAAgB,KAAI;AAC9C,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;AAEjD,YAAA,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC9E,KAAK,CAAC,cAAc,EAAE;;;AAEnB,aAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;YAGzB,KAAK,CAAC,cAAc,EAAE;;AAE1B,KAAC;;AAGO,IAAA,gBAAgB,CAAC,KAAY,EAAA;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAG;YACjC,OAAO,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,CAAC;AAC3F,SAAC,CAAC;;;AAII,IAAA,0BAA0B,CAChC,YAAyB,EACzB,aAA0B,EAC1B,eAAmC,EAAA;;AAGnC,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,iBAAiB,EAAE;AAC3C,YAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;AACtB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;aACd,IAAI,aAAa,KAAK,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,SAAS,EAAE;;AAE9E,YAAA,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAClE,YAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC;AAC1C,YAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC;;AAGvC,YAAA,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;;;;YAK3B,IAAI,eAAe,EAAE;AACnB,gBAAA,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;;iBACzB;gBACL,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;;;;AAI/D;AAED;AACA,SAASA,OAAK,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAA;AACpD,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC5C;AAEA;AACA,SAAS,YAAY,CAAC,KAA8B,EAAA;;;;IAIlD,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAC9B;AAEA;AACA,SAAS,oBAAoB,CAAC,KAAY,EAAA;IACxC,KAAK,CAAC,cAAc,EAAE;AACxB;;ACxmDA;;;;;AAKG;SACa,eAAe,CAAU,KAAU,EAAE,SAAiB,EAAE,OAAe,EAAA;AACrF,IAAA,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,IAAA,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAE3C,IAAA,IAAI,IAAI,KAAK,EAAE,EAAE;QACf;;AAGF,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;AAC1B,IAAA,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AAEhC,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,EAAE;QACvC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;;AAG7B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM;AACpB;AAEA;;;;;;AAMG;AACG,SAAU,iBAAiB,CAC/B,YAAiB,EACjB,WAAgB,EAChB,YAAoB,EACpB,WAAmB,EAAA;AAEnB,IAAA,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC;AAEjD,IAAA,IAAI,YAAY,CAAC,MAAM,EAAE;AACvB,QAAA,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9D;AAEA;;;;;;;;AAQG;AACG,SAAU,aAAa,CAC3B,YAAiB,EACjB,WAAgB,EAChB,YAAoB,EACpB,WAAmB,EAAA;IAEnB,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC;AAEjD,IAAA,IAAI,YAAY,CAAC,MAAM,EAAE;AACvB,QAAA,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;;AAEzD;AAEA;AACA,SAAS,KAAK,CAAC,KAAa,EAAE,GAAW,EAAA;AACvC,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC1C;;AC/CA;;;;AAIG;MACU,sBAAsB,CAAA;AAuBb,IAAA,iBAAA;;AArBZ,IAAA,QAAQ;;AAGR,IAAA,cAAc;;IAGd,cAAc,GAAkC,EAAE;AAE1D;;;;AAIG;AACK,IAAA,iBAAiB;;IAGzB,WAAW,GAA8B,UAAU;;AAGnD,IAAA,SAAS;AAET,IAAA,WAAA,CAAoB,iBAAmC,EAAA;QAAnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;;AAErC;;;;AAIG;AACK,IAAA,aAAa,GAAG;AACtB,QAAA,IAAI,EAAE,IAAsB;AAC5B,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,QAAQ,EAAE,KAAK;KAChB;AAED;;;AAGG;AACH,IAAA,KAAK,CAAC,KAAyB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAGvB;;;;;;AAMG;AACH,IAAA,IAAI,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE,YAAoC,EAAA;AAC1F,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc;AACpC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC;QAE9F,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY;AACtD,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC;AACjF,QAAA,MAAM,oBAAoB,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAC/C,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,UAAU;AACzD,QAAA,MAAM,WAAW,GAAG,oBAAoB,CAAC,UAAU;AACnD,QAAA,MAAM,KAAK,GAAG,YAAY,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;;AAG9C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC;;AAG7E,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,CAAC;;;AAI7E,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE;;AAGjC,QAAA,eAAe,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;QAEjD,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,KAAI;;AAElC,YAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE;gBAC/B;;AAGF,YAAA,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI;YAC3C,MAAM,MAAM,GAAG,aAAa,GAAG,UAAU,GAAG,aAAa;YACzD,MAAM,eAAe,GAAG;AACtB,kBAAE,IAAI,CAAC,qBAAqB;AAC5B,kBAAE,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;;AAGjC,YAAA,OAAO,CAAC,MAAM,IAAI,MAAM;YAExB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;;YAM7E,IAAI,YAAY,EAAE;;;AAGhB,gBAAA,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,iBAAiB,CACjD,CAAe,YAAA,EAAA,eAAe,WAAW,EACzC,OAAO,CAAC,gBAAgB,CACzB;gBACD,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,MAAM,CAAC;;iBACvC;AACL,gBAAA,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,iBAAiB,CACjD,CAAkB,eAAA,EAAA,eAAe,QAAQ,EACzC,OAAO,CAAC,gBAAgB,CACzB;gBACD,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;;AAEhD,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,kBAAkB,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC;QACjF,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,oBAAoB,CAAC,IAAI;AACnD,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;QAEzE,OAAO,EAAC,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAC;;AAG9D;;;;;;;AAOG;AACH,IAAA,KAAK,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAc,EAAA;QACrE,MAAM,QAAQ,GACZ,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG;AACvB;;gBAEE,IAAI,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ;cAC9D,KAAK;AAEX,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;QAC/C,MAAM,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;AACnD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE;AAChD,QAAA,IAAI,oBAAoB,GAAwB,gBAAgB,CAAC,QAAQ,CAAC;;;;AAK1E,QAAA,IAAI,oBAAoB,KAAK,IAAI,EAAE;AACjC,YAAA,oBAAoB,GAAG,gBAAgB,CAAC,QAAQ,GAAG,CAAC,CAAC;;;;AAKvD,QAAA,IACE,CAAC,oBAAoB;AACrB,aAAC,QAAQ,IAAI,IAAI,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACjD;AACA,YAAA,oBAAoB,GAAG,gBAAgB,CAAC,CAAC,CAAC;;;;AAK5C,QAAA,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE;AACrB,YAAA,gBAAgB,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;;;;AAK1C,QAAA,IAAI,oBAAoB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE;AACpF,YAAA,MAAM,OAAO,GAAG,oBAAoB,CAAC,cAAc,EAAE;YACrD,OAAO,CAAC,aAAc,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC;YACzD,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;;aACrC;AACL,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;AACtC,YAAA,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAI7B,QAAA,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;;;;QAKhC,IAAI,CAAC,mBAAmB,EAAE;;;AAI5B,IAAA,SAAS,CAAC,KAAyB,EAAA;AACjC,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,KAAK,EAAE;QACtC,IAAI,CAAC,mBAAmB,EAAE;;;AAI5B,IAAA,iBAAiB,CAAC,SAAiC,EAAA;AACjD,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS;;;IAIjC,KAAK,GAAA;;AAEH,QAAA,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,IAAI,IAAG;AACrC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;YAEzC,IAAI,WAAW,EAAE;gBACf,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,gBAAgB;gBACzF,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,gBAAgB,IAAI,EAAE;;AAExD,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;AACxB,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI;AAC9B,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,CAAC;AAC5B,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK;;AAGrC;;;AAGG;IACH,sBAAsB,GAAA;QACpB,OAAO,IAAI,CAAC,iBAAiB;;;AAI/B,IAAA,YAAY,CAAC,IAAa,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC,SAAS,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC;;;AAI3F,IAAA,cAAc,CAAC,KAAa,EAAA;QAC1B,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,IAAI;;;IAI5D,cAAc,CAAC,aAAqB,EAAE,cAAsB,EAAA;;;;;QAK1D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAC,UAAU,EAAC,KAAI;AAC3C,YAAA,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,cAAc,CAAC;AAC1D,SAAC,CAAC;;;QAIF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAC,IAAI,EAAC,KAAI;YACrC,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;;;gBAG3C,IAAI,CAAC,4BAA4B,EAAE;;AAEvC,SAAC,CAAC;;AAGJ,IAAA,oBAAoB,CAAC,SAAsB,EAAA;AACzC,QAAA,IAAI,CAAC,QAAQ,GAAG,SAAS;;;IAInB,mBAAmB,GAAA;AACzB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY;AAEtD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aACxB,GAAG,CAAC,IAAI,IAAG;AACV,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE;YACjD,OAAO;gBACL,IAAI;AACJ,gBAAA,MAAM,EAAE,CAAC;AACT,gBAAA,gBAAgB,EAAE,gBAAgB,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE;AACxD,gBAAA,UAAU,EAAE,oBAAoB,CAAC,gBAAgB,CAAC;aACnD;AACH,SAAC;AACA,aAAA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AACb,YAAA,OAAO;kBACH,CAAC,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC;AACnC,kBAAE,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG;AACzC,SAAC,CAAC;;IAGE,uBAAuB,GAAA;;;;QAI7B,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,IAAI,CAAC,SAAS,KAAK;cAC3D,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,OAAO;AACrC,cAAE,IAAI,CAAC,cAAc;;AAGzB;;;;;AAKG;AACK,IAAA,gBAAgB,CAAC,eAAwB,EAAE,WAAoB,EAAE,KAAa,EAAA;AACpF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY;QACtD,IAAI,UAAU,GAAG;AACf,cAAE,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC;cACnC,WAAW,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG;;AAGzC,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,YAAA,UAAU,IAAI;AACZ,kBAAE,WAAW,CAAC,KAAK,GAAG,eAAe,CAAC;kBACpC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM;;AAGjD,QAAA,OAAO,UAAU;;AAGnB;;;;;AAKG;AACK,IAAA,mBAAmB,CACzB,YAAoB,EACpB,QAAuC,EACvC,KAAa,EAAA;AAEb,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY;QACtD,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,UAAU;QACzD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;AAC5D,QAAA,IAAI,aAAa,GAAG,eAAe,CAAC,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,KAAK;QAE9E,IAAI,gBAAgB,EAAE;YACpB,MAAM,KAAK,GAAG,YAAY,GAAG,MAAM,GAAG,KAAK;YAC3C,MAAM,GAAG,GAAG,YAAY,GAAG,OAAO,GAAG,QAAQ;;;;;AAM7C,YAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,gBAAA,aAAa,IAAI,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC;;iBACrE;AACL,gBAAA,aAAa,IAAI,eAAe,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC;;;AAI9E,QAAA,OAAO,aAAa;;AAGtB;;;;AAIG;IACK,wBAAwB,CAAC,QAAgB,EAAE,QAAgB,EAAA;AACjE,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AAClC,YAAA,OAAO,KAAK;;AAGd,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;AACzC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY;;;AAItD,QAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACpE,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU;AACvE,YAAA,OAAO,YAAY,GAAG,QAAQ,IAAI,YAAY,CAAC,KAAK,GAAG,QAAQ,IAAI,YAAY,CAAC,MAAM;;aACjF;YACL,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU;AACjD,YAAA,OAAO,YAAY,GAAG,QAAQ,IAAI,aAAa,CAAC,IAAI,GAAG,QAAQ,IAAI,aAAa,CAAC,GAAG;;;AAIxF;;;;;;AAMG;AACK,IAAA,gCAAgC,CACtC,IAAa,EACb,QAAgB,EAChB,QAAgB,EAChB,KAA8B,EAAA;AAE9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY;AACtD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,EAAE,UAAU,EAAC,KAAI;;AAEjE,YAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,gBAAA,OAAO,KAAK;;YAGd,IAAI,KAAK,EAAE;AACT,gBAAA,MAAM,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;;;;AAKlD,gBAAA,IACE,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI;oBAChC,IAAI,CAAC,aAAa,CAAC,QAAQ;AAC3B,oBAAA,SAAS,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,EACtC;AACA,oBAAA,OAAO,KAAK;;;AAIhB,YAAA,OAAO;AACL;;AAEE,oBAAA,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK;kBACjF,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;AACxF,SAAC,CAAC;QAEF,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;;AAExE;;ACvbD;;;;AAIG;MACU,iBAAiB,CAAA;AAoClB,IAAA,SAAA;AACA,IAAA,iBAAA;;AAnCF,IAAA,QAAQ;;AAGR,IAAA,cAAc;;AAGd,IAAA,SAAS;AAEjB;;;;AAIG;AACK,IAAA,YAAY;AAEpB;;;;AAIG;AACK,IAAA,aAAa,GAAG;AACtB,QAAA,IAAI,EAAE,IAAsB;AAC5B,QAAA,MAAM,EAAE,CAAC;AACT,QAAA,MAAM,EAAE,CAAC;AACT,QAAA,QAAQ,EAAE,KAAK;KAChB;AAED;;;AAGG;IACK,aAAa,GAA6C,EAAE;IAEpE,WACU,CAAA,SAAmB,EACnB,iBAAmC,EAAA;QADnC,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;;AAG3B;;;AAGG;AACH,IAAA,KAAK,CAAC,KAAyB,EAAA;AAC7B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AAEvB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,YAAA,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;AAC1B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;;AAGnD,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAGvB;;;;;;AAMG;AACH,IAAA,IAAI,CACF,IAAa,EACb,QAAgB,EAChB,QAAgB,EAChB,YAAoC,EAAA;AAEpC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAChF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;AAEvC,QAAA,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;AAC3D,YAAA,OAAO,IAAI;;QAGb,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;AAG9C,QAAA,IACE,YAAY,CAAC,IAAI,KAAK,UAAU;AAChC,YAAA,YAAY,CAAC,QAAQ;AACrB,YAAA,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC;AACtC,YAAA,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC,EACtC;AACA,YAAA,OAAO,IAAI;;QAGb,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAC7C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE;AAC5C,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,EAAE;AAElD,QAAA,IAAI,QAAQ,GAAG,aAAa,EAAE;AAC5B,YAAA,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC;;aACxB;AACL,YAAA,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC;;QAGhC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC;AAE3D,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC;;;AAGlF,QAAA,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;AACpC,QAAA,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;AACpC,QAAA,YAAY,CAAC,IAAI,GAAG,UAAU;AAC9B,QAAA,YAAY,CAAC,QAAQ;YACnB,cAAc,KAAK,iBAAiB,IAAI,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QAEpF,OAAO;YACL,aAAa;AACb,YAAA,YAAY,EAAE,QAAQ;SACvB;;AAGH;;;;;;;AAOG;AACH,IAAA,KAAK,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAc,EAAA;QACrE,IAAI,UAAU,GACZ,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG;cACrB,IAAI,CAAC,gCAAgC,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ;cAC9D,KAAK;;;;AAKX,QAAA,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;YACrB,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;;QAG3E,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAwB;QACvE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;AAEpD,QAAA,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;;AAG3C,QAAA,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAChE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC;YAC7C,UAAU,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;;aAC3D;AACL,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;;;;AAK3D,IAAA,SAAS,CAAC,KAAyB,EAAA;AACjC,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE;;;AAInC,IAAA,iBAAiB,CAAC,SAAiC,EAAA;AACjD,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS;;;IAIjC,KAAK,GAAA;AACH,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ;AAC1B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa;;;;;;;;AASvC,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;AACvD,YAAA,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AACjD,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW,EAAE;AAChE,gBAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;AACjB,qBAAA,IAAI,WAAW,CAAC,UAAU,KAAK,IAAI,EAAE;AAC1C,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC;;;;AAK1C,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE;AACtB,QAAA,YAAY,CAAC,IAAI,GAAG,IAAI;QACxB,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC;AAC7C,QAAA,YAAY,CAAC,QAAQ,GAAG,KAAK;;AAG/B;;;AAGG;IACH,sBAAsB,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY;;;AAI1B,IAAA,YAAY,CAAC,IAAa,EAAA;QACxB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;;;AAIxC,IAAA,cAAc,CAAC,KAAa,EAAA;QAC1B,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,IAAI;;;IAIzC,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,IAAG;YAC/B,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;;;gBAG3C,IAAI,CAAC,4BAA4B,EAAE;;AAEvC,SAAC,CAAC;;AAGJ,IAAA,oBAAoB,CAAC,SAAsB,EAAA;AACzC,QAAA,IAAI,SAAS,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,QAAQ,GAAG,SAAS;AACzB,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;;;AAI9B;;;;;;AAMG;AACK,IAAA,gCAAgC,CACtC,IAAa,EACb,QAAgB,EAChB,QAAgB,EAAA;QAEhB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,gBAAgB,CACzD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EACpB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CACrB;QACD,MAAM,KAAK,GAAG;cACV,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG;AACjC,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE;gBAClC,OAAO,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;AACjE,aAAC;cACD,CAAC,CAAC;QACN,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;;;IAI/D,YAAY,GAAA;;AAElB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS;;QAElE,OAAO,IAAI,CAAC,SAAS;;AAGvB;;;;;AAKG;AACK,IAAA,6BAA6B,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAgB,EAAA;QACrF,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,OAAO,CAAC,CAAC;;QAGX,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,YAAA,OAAO,CAAC;;QAGV,IAAI,WAAW,GAAG,QAAQ;AAC1B,QAAA,IAAI,QAAQ,GAAG,CAAC,CAAC;;;;;AAMjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACpC,YAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,gBAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC,qBAAqB,EAAE;AAC/D,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC;AAEvD,gBAAA,IAAI,QAAQ,GAAG,WAAW,EAAE;oBAC1B,WAAW,GAAG,QAAQ;oBACtB,QAAQ,GAAG,CAAC;;;;AAKlB,QAAA,OAAO,QAAQ;;AAElB;;ACrSD;;;AAGG;AACH,MAAM,wBAAwB,GAAG,IAAI;AAErC;;;AAGG;AACH,MAAM,0BAA0B,GAAG,IAAI;AAEvC;AACA,IAAK,2BAIJ;AAJD,CAAA,UAAK,2BAA2B,EAAA;AAC9B,IAAA,2BAAA,CAAA,2BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,2BAAA,CAAA,2BAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAE;AACF,IAAA,2BAAA,CAAA,2BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACN,CAAC,EAJI,2BAA2B,KAA3B,2BAA2B,GAI/B,EAAA,CAAA,CAAA;AAED;AACA,IAAK,6BAIJ;AAJD,CAAA,UAAK,6BAA6B,EAAA;AAChC,IAAA,6BAAA,CAAA,6BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,6BAAA,CAAA,6BAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,6BAAA,CAAA,6BAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACP,CAAC,EAJI,6BAA6B,KAA7B,6BAA6B,GAIjC,EAAA,CAAA,CAAA;AAED;;AAEG;MACU,WAAW,CAAA;AA+IZ,IAAA,iBAAA;AAEA,IAAA,OAAA;AACA,IAAA,cAAA;;AAhJV,IAAA,OAAO;;IAGP,QAAQ,GAAY,KAAK;;IAGzB,eAAe,GAAY,KAAK;;AAGhC,IAAA,QAAQ;AAER;;;AAGG;IACH,kBAAkB,GAAY,KAAK;;IAGnC,cAAc,GAAW,CAAC;AAE1B;;AAEG;IACH,SAAS,GAAY,KAAK;AAE1B;;;AAGG;AACH,IAAA,cAAc,GAAkD,MAAM,IAAI;;AAG1E,IAAA,aAAa,GAAiE,MAAM,IAAI;;AAG/E,IAAA,aAAa,GAAG,IAAI,OAAO,EAAQ;AAE5C;;AAEG;AACM,IAAA,OAAO,GAAG,IAAI,OAAO,EAAiE;AAE/F;;;AAGG;AACM,IAAA,MAAM,GAAG,IAAI,OAAO,EAA2C;;AAG/D,IAAA,OAAO,GAAG,IAAI,OAAO,EAU1B;;AAGK,IAAA,MAAM,GAAG,IAAI,OAAO,EAKzB;;AAGK,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAInC;;AAGK,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAGnC;;AAGJ,IAAA,IAAI;;AAGI,IAAA,UAAU;;IAGV,WAAW,GAAG,KAAK;;AAGnB,IAAA,gBAAgB;;AAGhB,IAAA,aAAa;;AAGb,IAAA,QAAQ;;IAGR,WAAW,GAAuB,EAAE;;IAGpC,SAAS,GAA2B,EAAE;;AAGtC,IAAA,eAAe,GAAG,IAAI,GAAG,EAAe;;AAGxC,IAAA,2BAA2B,GAAG,YAAY,CAAC,KAAK;;AAGhD,IAAA,wBAAwB,GAAG,2BAA2B,CAAC,IAAI;;AAG3D,IAAA,0BAA0B,GAAG,6BAA6B,CAAC,IAAI;;AAG/D,IAAA,WAAW;;AAGF,IAAA,iBAAiB,GAAG,IAAI,OAAO,EAAQ;;IAGhD,iBAAiB,GAAgC,IAAI;;AAGrD,IAAA,SAAS;;IAGT,mBAAmB,GAAkB,EAAE;;AAGvC,IAAA,kBAAkB;;IAGlB,UAAU,GAAc,KAAK;IAErC,WACE,CAAA,OAA8C,EACtC,iBAAmC,EAC3C,SAAc,EACN,OAAe,EACf,cAA6B,EAAA;QAH7B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QAEjB,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAc,CAAA,cAAA,GAAd,cAAc;AAEtB,QAAA,MAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;AAC9D,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;QAC1B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,cAAc,CAAC;AACrE,QAAA,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,SAAS,CAAC;;;IAI9D,OAAO,GAAA;QACL,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;AACjC,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;AAC9C,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AAC7B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;AAChC,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;AAC5B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK;AACxB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AAC7B,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,IAAI,CAAC;;;IAIlD,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,WAAW;;;IAIzB,KAAK,GAAA;QACH,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,wBAAwB,EAAE;;AAGjC;;;;;;;AAOG;AACH,IAAA,KAAK,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAc,EAAA;QACrE,IAAI,CAAC,gBAAgB,EAAE;;;QAIvB,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;YACzC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;;AAGxC,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC;;;QAIzD,IAAI,CAAC,qBAAqB,EAAE;;QAG5B,IAAI,CAAC,wBAAwB,EAAE;QAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAC,CAAC;;AAGnF;;;AAGG;AACH,IAAA,IAAI,CAAC,IAAa,EAAA;QAChB,IAAI,CAAC,MAAM,EAAE;AACb,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAC,CAAC;;AAG3C;;;;;;;;;;;;AAYG;AACH,IAAA,IAAI,CACF,IAAa,EACb,YAAoB,EACpB,aAAqB,EACrB,iBAA8B,EAC9B,sBAA+B,EAC/B,QAAe,EACf,SAAgB,EAChB,QAAiC,EAAS,EAAA;QAE1C,IAAI,CAAC,MAAM,EAAE;AACb,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAChB,IAAI;YACJ,YAAY;YACZ,aAAa;AACb,YAAA,SAAS,EAAE,IAAI;YACf,iBAAiB;YACjB,sBAAsB;YACtB,QAAQ;YACR,SAAS;YACT,KAAK;AACN,SAAA,CAAC;;AAGJ;;;AAGG;AACH,IAAA,SAAS,CAAC,KAAgB,EAAA;AACxB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAEpD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACrB,YAAA,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;;;AAIpE,YAAA,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC1D,IAAI,CAAC,MAAM,EAAE;;iBACR;gBACL,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;;;AAIlD,QAAA,OAAO,IAAI;;;AAIb,IAAA,aAAa,CAAC,SAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,QAAA,IAAI,IAAI,CAAC,aAAa,YAAY,sBAAsB,EAAE;AACxD,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS;;AAE1C,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;AACH,IAAA,WAAW,CAAC,WAA0B,EAAA;AACpC,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,EAAE;AACpC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,eAAe,CAAC,WAAgC,EAAA;AAC9C,QAAA,IAAI,WAAW,KAAK,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC;;aAC7E;YACL,MAAM,QAAQ,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACnE,YAAA,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU;AACpC,YAAA,QAAQ,CAAC,WAAW,GAAG,WAAW;AAClC,YAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;;QAE/B,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5F,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,qBAAqB,CAAC,QAAuB,EAAA;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU;;;AAI/B,QAAA,IAAI,CAAC,mBAAmB;YACtB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE;AAC9E,QAAA,OAAO,IAAI;;AAGb;;;;;;AAMG;AACH,IAAA,oBAAoB,CAAC,SAAsB,EAAA;AACzC,QAAA,IAAI,SAAS,KAAK,IAAI,CAAC,UAAU,EAAE;AACjC,YAAA,OAAO,IAAI;;QAGb,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;AAE3C,QAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC9C,YAAA,SAAS,KAAK,OAAO;AACrB,YAAA,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC5B;AACA,YAAA,MAAM,IAAI,KAAK,CACb,yGAAyG,CAC1G;;AAGH,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAC3E,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC;AAErE,QAAA,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;;AAGvD,QAAA,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;;AAGvD,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,SAAS,CAAC;;AAGpD,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;AAC7B,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,QAAA,OAAO,IAAI;;;IAIb,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,mBAAmB;;AAGjC;;;AAGG;AACH,IAAA,YAAY,CAAC,IAAa,EAAA;QACxB,OAAO,IAAI,CAAC;cACR,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI;cACpC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;;AAGpC;;;AAGG;AACH,IAAA,cAAc,CAAC,KAAa,EAAA;QAC1B,OAAO,IAAI,CAAC;cACR,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK;cACvC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,IAAI;;AAGrC;;;AAGG;IACH,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC;;AAGtC;;;;;;AAMG;AACH,IAAA,SAAS,CACP,IAAa,EACb,QAAgB,EAChB,QAAgB,EAChB,YAAoC,EAAA;;QAGpC,IACE,IAAI,CAAC,eAAe;YACpB,CAAC,IAAI,CAAC,QAAQ;AACd,YAAA,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,wBAAwB,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAClF;YACA;;AAGF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC;QAE9E,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,YAAY,EAAE,MAAM,CAAC,YAAY;AACjC,gBAAA,SAAS,EAAE,IAAI;gBACf,IAAI;AACL,aAAA,CAAC;;;AAIN;;;;;AAKG;IACH,0BAA0B,CAAC,QAAgB,EAAE,QAAgB,EAAA;AAC3D,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B;;AAGF,QAAA,IAAI,UAA4C;AAChD,QAAA,IAAI,uBAAuB,GAAG,2BAA2B,CAAC,IAAI;AAC9D,QAAA,IAAI,yBAAyB,GAAG,6BAA6B,CAAC,IAAI;;AAGlE,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAI;;;AAG5D,YAAA,IAAI,OAAO,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,EAAE;gBACpE;;AAGF,YAAA,IAAI,oBAAoB,CAAC,QAAQ,CAAC,UAAU,EAAE,wBAAwB,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBAC3F,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,GAAG,0BAA0B,CAC/E,OAAsB,EACtB,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,UAAU,EACf,QAAQ,EACR,QAAQ,CACT;AAED,gBAAA,IAAI,uBAAuB,IAAI,yBAAyB,EAAE;oBACxD,UAAU,GAAG,OAAsB;;;AAGzC,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,uBAAuB,IAAI,CAAC,yBAAyB,EAAE;AAC1D,YAAA,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;AAC7D,YAAA,MAAM,OAAO,GAAG;gBACd,KAAK;gBACL,MAAM;AACN,gBAAA,GAAG,EAAE,CAAC;AACN,gBAAA,KAAK,EAAE,KAAK;AACZ,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,IAAI,EAAE,CAAC;aACG;AACZ,YAAA,uBAAuB,GAAG,0BAA0B,CAAC,OAAO,EAAE,QAAQ,CAAC;AACvE,YAAA,yBAAyB,GAAG,4BAA4B,CAAC,OAAO,EAAE,QAAQ,CAAC;YAC3E,UAAU,GAAG,MAAM;;AAGrB,QAAA,IACE,UAAU;AACV,aAAC,uBAAuB,KAAK,IAAI,CAAC,wBAAwB;gBACxD,yBAAyB,KAAK,IAAI,CAAC,0BAA0B;AAC7D,gBAAA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC,EAClC;AACA,YAAA,IAAI,CAAC,wBAAwB,GAAG,uBAAuB;AACvD,YAAA,IAAI,CAAC,0BAA0B,GAAG,yBAAyB;AAC3D,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU;YAE7B,IAAI,CAAC,uBAAuB,IAAI,yBAAyB,KAAK,UAAU,EAAE;gBACxE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC;;iBACpD;gBACL,IAAI,CAAC,cAAc,EAAE;;;;;IAM3B,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;;;IAIvB,gBAAgB,GAAA;AACtB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAgC;AAC/D,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AAEvB,QAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;;;YAG9C,IAAI,CAAC,UAAU,KAAK,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAC/C;AACA,YAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AACnC,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,EAAE;AACjF,oBAAA,MAAM,IAAI,KAAK,CACb,yGAAyG,CAC1G;;;;;;;AAQP,QAAA,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,cAAc,IAAI,EAAE;QAChF,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,GAAG,MAAM;QACxD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;QAC1C,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;QAC9C,IAAI,CAAC,qBAAqB,EAAE;;;IAItB,qBAAqB,GAAA;QAC3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC;;;AAIrD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAE,CAAC,UAAW;;;IAI3E,MAAM,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAgC;QAC/D,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB;AAEzE,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;QAC1B,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;;;IAIvB,oBAAoB,GAAG,MAAK;QAClC,IAAI,CAAC,cAAc,EAAE;AAErB,QAAA,QAAQ,CAAC,CAAC,EAAE,uBAAuB;AAChC,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC;aACtC,SAAS,CAAC,MAAK;AACd,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW;AAC7B,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc;YAEtC,IAAI,IAAI,CAAC,wBAAwB,KAAK,2BAA2B,CAAC,EAAE,EAAE;gBACpE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;;iBACxB,IAAI,IAAI,CAAC,wBAAwB,KAAK,2BAA2B,CAAC,IAAI,EAAE;AAC7E,gBAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC;;YAG9B,IAAI,IAAI,CAAC,0BAA0B,KAAK,6BAA6B,CAAC,IAAI,EAAE;gBAC1E,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;;iBACxB,IAAI,IAAI,CAAC,0BAA0B,KAAK,6BAA6B,CAAC,KAAK,EAAE;AAClF,gBAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;;AAEhC,SAAC,CAAC;AACN,KAAC;AAED;;;;AAIG;IACH,gBAAgB,CAAC,CAAS,EAAE,CAAS,EAAA;AACnC,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;;AAGzE;;;;;;AAMG;AACH,IAAA,gCAAgC,CAAC,IAAa,EAAE,CAAS,EAAE,CAAS,EAAA;QAClE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;AAGxE;;;;;AAKG;AACH,IAAA,WAAW,CAAC,IAAa,EAAE,CAAS,EAAE,CAAS,EAAA;QAC7C,IACE,CAAC,IAAI,CAAC,QAAQ;YACd,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YACxC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,EAChC;AACA,YAAA,OAAO,KAAK;;AAGd,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAuB;;;QAI3F,IAAI,CAAC,gBAAgB,EAAE;AACrB,YAAA,OAAO,KAAK;;;;;;;;AASd,QAAA,OAAO,gBAAgB,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;AAG3F;;;AAGG;IACH,eAAe,CAAC,OAAoB,EAAE,KAAgB,EAAA;AACpD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;AAE3C,QAAA,IACE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AAC5B,YAAA,KAAK,CAAC,KAAK,CAAC,IAAI,IAAG;;;;;gBAKjB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC9E,CAAC,EACF;AACA,YAAA,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3B,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACzB,gBAAA,SAAS,EAAE,OAAO;AAClB,gBAAA,QAAQ,EAAE,IAAI;gBACd,KAAK;AACN,aAAA,CAAC;;;AAIN;;;AAGG;AACH,IAAA,cAAc,CAAC,OAAoB,EAAA;AACjC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;AACpC,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAGlE;;;AAGG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;AACrC,aAAA,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;aAC9B,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC;gBAElE,IAAI,gBAAgB,EAAE;AACpB,oBAAA,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,CAAC;;;AAE3E,iBAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC7B,IAAI,CAAC,qBAAqB,EAAE;;AAEhC,SAAC,CAAC;;AAGN;;;;;AAKG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;YAClD,IAAI,CAAC,iBAAiB,GAAG,UAAU,IAAI,IAAI,CAAC,SAAS;;QAGvD,OAAO,IAAI,CAAC,iBAAiB;;;IAIvB,wBAAwB,GAAA;AAC9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC;AACvB,aAAA,sBAAsB;aACtB,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;;AAEjF;AAED;;;;AAIG;AACH,SAAS,0BAA0B,CAAC,UAAmB,EAAE,QAAgB,EAAA;IACvE,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAC,GAAG,UAAU;AACxC,IAAA,MAAM,UAAU,GAAG,MAAM,GAAG,0BAA0B;AAEtD,IAAA,IAAI,QAAQ,IAAI,GAAG,GAAG,UAAU,IAAI,QAAQ,IAAI,GAAG,GAAG,UAAU,EAAE;QAChE,OAAO,2BAA2B,CAAC,EAAE;;AAChC,SAAA,IAAI,QAAQ,IAAI,MAAM,GAAG,UAAU,IAAI,QAAQ,IAAI,MAAM,GAAG,UAAU,EAAE;QAC7E,OAAO,2BAA2B,CAAC,IAAI;;IAGzC,OAAO,2BAA2B,CAAC,IAAI;AACzC;AAEA;;;;AAIG;AACH,SAAS,4BAA4B,CAAC,UAAmB,EAAE,QAAgB,EAAA;IACzE,MAAM,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAC,GAAG,UAAU;AACvC,IAAA,MAAM,UAAU,GAAG,KAAK,GAAG,0BAA0B;AAErD,IAAA,IAAI,QAAQ,IAAI,IAAI,GAAG,UAAU,IAAI,QAAQ,IAAI,IAAI,GAAG,UAAU,EAAE;QAClE,OAAO,6BAA6B,CAAC,IAAI;;AACpC,SAAA,IAAI,QAAQ,IAAI,KAAK,GAAG,UAAU,IAAI,QAAQ,IAAI,KAAK,GAAG,UAAU,EAAE;QAC3E,OAAO,6BAA6B,CAAC,KAAK;;IAG5C,OAAO,6BAA6B,CAAC,IAAI;AAC3C;AAEA;;;;;;;;AAQG;AACH,SAAS,0BAA0B,CACjC,OAAoB,EACpB,UAAmB,EACnB,SAAoB,EACpB,QAAgB,EAChB,QAAgB,EAAA;IAEhB,MAAM,gBAAgB,GAAG,0BAA0B,CAAC,UAAU,EAAE,QAAQ,CAAC;IACzE,MAAM,kBAAkB,GAAG,4BAA4B,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC7E,IAAA,IAAI,uBAAuB,GAAG,2BAA2B,CAAC,IAAI;AAC9D,IAAA,IAAI,yBAAyB,GAAG,6BAA6B,CAAC,IAAI;;;;;IAMlE,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS;AAEnC,QAAA,IAAI,gBAAgB,KAAK,2BAA2B,CAAC,EAAE,EAAE;AACvD,YAAA,IAAI,SAAS,GAAG,CAAC,EAAE;AACjB,gBAAA,uBAAuB,GAAG,2BAA2B,CAAC,EAAE;;;aAErD,IAAI,OAAO,CAAC,YAAY,GAAG,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE;AAClE,YAAA,uBAAuB,GAAG,2BAA2B,CAAC,IAAI;;;IAI9D,IAAI,kBAAkB,EAAE;AACtB,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU;AAErC,QAAA,IAAI,SAAS,KAAK,KAAK,EAAE;AACvB,YAAA,IAAI,kBAAkB,KAAK,6BAA6B,CAAC,KAAK,EAAE;;AAE9D,gBAAA,IAAI,UAAU,GAAG,CAAC,EAAE;AAClB,oBAAA,yBAAyB,GAAG,6BAA6B,CAAC,KAAK;;;iBAE5D,IAAI,OAAO,CAAC,WAAW,GAAG,UAAU,GAAG,OAAO,CAAC,WAAW,EAAE;AACjE,gBAAA,yBAAyB,GAAG,6BAA6B,CAAC,IAAI;;;aAE3D;AACL,YAAA,IAAI,kBAAkB,KAAK,6BAA6B,CAAC,IAAI,EAAE;AAC7D,gBAAA,IAAI,UAAU,GAAG,CAAC,EAAE;AAClB,oBAAA,yBAAyB,GAAG,6BAA6B,CAAC,IAAI;;;iBAE3D,IAAI,OAAO,CAAC,WAAW,GAAG,UAAU,GAAG,OAAO,CAAC,WAAW,EAAE;AACjE,gBAAA,yBAAyB,GAAG,6BAA6B,CAAC,KAAK;;;;AAKrE,IAAA,OAAO,CAAC,uBAAuB,EAAE,yBAAyB,CAAC;AAC7D;;ACx1BA;AACA,MAAM,qBAAqB,GAAG;AAC5B,IAAA,OAAO,EAAE,IAAI;CACd;AAED;AACA,MAAM,2BAA2B,GAAG;AAClC,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,OAAO,EAAE,IAAI;CACd;AAED;;;AAGG;MAQU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,mIAJd,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,mLAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAID,aAAa,EAAA,UAAA,EAAA,CAAA;kBAPzB,SAAS;AAEO,YAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAC3B,QAAA,EAAA,EAAE,EACK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA,EAAC,2BAA2B,EAAE,EAAE,EAAC,EAAA,MAAA,EAAA,CAAA,mLAAA,CAAA,EAAA;;AAIzC;;;;AAIG;MAEU,gBAAgB,CAAA;AACnB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,YAAY,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAC7C,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAC/D,IAAA,yBAAyB;AACzB,IAAA,OAAO,GAAmB,IAAI,OAAO,EAAS;;AAG9C,IAAA,cAAc,GAAG,IAAI,GAAG,EAAe;;AAGvC,IAAA,cAAc,GAAG,IAAI,GAAG,EAAW;;AAGnC,IAAA,oBAAoB,GAA8B,MAAM,CAAC,EAAE,CAAC;;AAG5D,IAAA,gBAAgB;AAExB;;;AAGG;IACK,kBAAkB,GAAG,CAAC,IAAa,KAAK,IAAI,CAAC,UAAU,EAAE;AAEjE;;;;AAIG;IACK,qBAAqB,GAAkC,IAAI;AAEnE;;;AAGG;AACM,IAAA,WAAW,GAAqC,IAAI,OAAO,EAA2B;AAE/F;;;AAGG;AACM,IAAA,SAAS,GAAqC,IAAI,OAAO,EAA2B;AAG7F,IAAA,WAAA,GAAA;;AAGA,IAAA,qBAAqB,CAAC,IAAiB,EAAA;QACrC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;;;;AAKjC,IAAA,gBAAgB,CAAC,IAAa,EAAA;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;;;;QAK7B,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;;AAGlC,gBAAA,IAAI,CAAC,yBAAyB,IAAI;gBAClC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACpD,IAAI,CAAC,SAAS,EACd,WAAW,EACX,IAAI,CAAC,4BAA4B,EACjC,2BAA2B,CAC5B;AACH,aAAC,CAAC;;;;AAKN,IAAA,mBAAmB,CAAC,IAAiB,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;;;AAIlC,IAAA,cAAc,CAAC,IAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QAEvB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,yBAAyB,IAAI;;;AAItC;;;;AAIG;IACH,aAAa,CAAC,IAAa,EAAE,KAA8B,EAAA;;AAEzD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YAClD;;AAGF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC;AACrC,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;;;;YAI5C,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACnD,YAAA,MAAM,eAAe,GAAG,CAAC,CAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAA4B,CAAC;AAEvF,YAAA,MAAM,MAAM,GAAgF;;;AAG1F,gBAAA,CAAC,QAAQ,EAAE,CAAC,CAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC;;;;;AAMrE,gBAAA,CAAC,aAAa,EAAE,IAAI,CAAC,4BAA4B,EAAE,2BAA2B,CAAC;aAChF;YAED,IAAI,YAAY,EAAE;AAChB,gBAAA,MAAM,CAAC,IAAI,CACT,CAAC,UAAU,EAAE,eAAe,EAAE,qBAAqB,CAAC,EACpD,CAAC,aAAa,EAAE,eAAe,EAAE,qBAAqB,CAAC,CACxD;;iBACI;gBACL,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,qBAAqB,CAAC,CAAC;;;;YAKlE,IAAI,CAAC,YAAY,EAAE;gBACjB,MAAM,CAAC,IAAI,CAAC;oBACV,WAAW;oBACX,CAAC,CAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAe,CAAC;oBACpD,2BAA2B;AAC5B,iBAAA,CAAC;;AAGJ,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,KAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9D;AACH,aAAC,CAAC;;;;AAKN,IAAA,YAAY,CAAC,IAAa,EAAA;AACxB,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,IAAG;YAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;AACrC,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,gBAAA,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAC1B,gBAAA,OAAO,CAAC,GAAG,SAAS,CAAC;;AAEvB,YAAA,OAAO,SAAS;AAClB,SAAC,CAAC;QAEF,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,qBAAqB,EAAE;;;;AAKhC,IAAA,UAAU,CAAC,IAAa,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;AAGvD;;;;;;AAMG;AACH,IAAA,QAAQ,CAAC,UAAwC,EAAA;AAC/C,QAAA,MAAM,OAAO,GAAwB,CAAC,IAAI,CAAC,OAAO,CAAC;QAEnD,IAAI,UAAU,IAAI,UAAU,KAAK,IAAI,CAAC,SAAS,EAAE;;;;YAI/C,OAAO,CAAC,IAAI,CACV,IAAI,UAAU,CAAC,CAAC,QAAyB,KAAI;AAC3C,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACzC,oBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACnC,UAAwB,EACxB,QAAQ,EACR,CAAC,KAAY,KAAI;AACf,wBAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE;AACtC,4BAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;qBAEvB,EACD,qBAAqB,CACtB;AAED,oBAAA,OAAO,MAAK;AACV,wBAAA,OAAO,EAAE;AACX,qBAAC;AACH,iBAAC,CAAC;aACH,CAAC,CACH;;AAGH,QAAA,OAAO,KAAK,CAAC,GAAG,OAAO,CAAC;;AAG1B;;;;AAIG;IACH,qBAAqB,CAAC,IAAU,EAAE,OAAgB,EAAA;AAChD,QAAA,IAAI,CAAC,qBAAqB,KAAK,IAAI,OAAO,EAAE;QAC5C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;;AAG/C;;;AAGG;AACH,IAAA,mBAAmB,CAAC,IAAU,EAAA;AAC5B,QAAA,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,IAAI,CAAC;;AAG1C;;;AAGG;AACH,IAAA,uBAAuB,CAAC,IAAU,EAAA;QAChC,OAAO,IAAI,CAAC,qBAAqB,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;;IAGtD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;QACjC,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;;AAG3B;;;AAGG;AACK,IAAA,4BAA4B,GAAG,CAAC,KAAY,KAAI;QACtD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1C,KAAK,CAAC,cAAc,EAAE;;AAE1B,KAAC;;AAGO,IAAA,4BAA4B,GAAG,CAAC,KAAiB,KAAI;QAC3D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;;;;AAI1C,YAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE;gBAC7D,KAAK,CAAC,cAAc,EAAE;;AAGxB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEhC,KAAC;;IAGO,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACpD,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS;;uGAlRxB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;AC3ChC;AACA,MAAM,cAAc,GAAG;AACrB,IAAA,kBAAkB,EAAE,CAAC;AACrB,IAAA,+BAA+B,EAAE,CAAC;CACnC;AAED;;AAEG;MAEU,QAAQ,CAAA;AACX,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAGvE,IAAA,WAAA,GAAA;AAEA;;;;AAIG;AACH,IAAA,UAAU,CACR,OAA8C,EAC9C,MAAA,GAAwB,cAAc,EAAA;QAEtC,OAAO,IAAI,OAAO,CAChB,OAAO,EACP,MAAM,EACN,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,SAAS,CACf;;AAGH;;;AAGG;AACH,IAAA,cAAc,CAAU,OAA8C,EAAA;QACpE,OAAO,IAAI,WAAW,CACpB,OAAO,EACP,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,cAAc,CACpB;;uGAzCQ,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAR,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,cADI,MAAM,EAAA,CAAA;;2FAClB,QAAQ,EAAA,UAAA,EAAA,CAAA;kBADpB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACbhC;;;;;AAKG;MACU,eAAe,GAAG,IAAI,cAAc,CAAU,iBAAiB;;ACT5E;;;;AAIG;AACa,SAAA,iBAAiB,CAAC,IAAU,EAAE,IAAY,EAAA;AACxD,IAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;AACvB,QAAA,MAAM,KAAK,CACT,CAAG,EAAA,IAAI,CAAwC,sCAAA,CAAA,GAAG,CAA0B,uBAAA,EAAA,IAAI,CAAC,QAAQ,CAAI,EAAA,CAAA,CAC9F;;AAEL;;ACKA;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAgB,eAAe;AAEhF;MAQa,aAAa,CAAA;AACxB,IAAA,OAAO,GAAG,MAAM,CAA0B,UAAU,CAAC;AAE7C,IAAA,WAAW,GAAG,MAAM,CAAU,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;AAChF,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;;AAG3C,IAAA,aAAa,GAAG,IAAI,OAAO,EAAiB;;AAGrD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;IAEvB,SAAS,GAAG,KAAK;AAIzB,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC;;AAGhE,QAAA,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC;;IAGpC,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa;YACrD,OAAO,MAAM,EAAE;gBACb,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBAClE,IAAI,GAAG,EAAE;AACP,oBAAA,IAAI,CAAC,WAAW,GAAG,GAAG;AACtB,oBAAA,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;oBACpB;;AAEF,gBAAA,MAAM,GAAG,MAAM,CAAC,aAAa;;;;IAKnC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,IAAI,CAAC;AACrC,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;;uGA/CpB,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAU2B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,uBAAA,EAAA,UAAA,EAAA,gBAAgB,CAZxD,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAExD,aAAa,EAAA,UAAA,EAAA,CAAA;kBAPzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC3B,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACpE,iBAAA;wDAYK,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC;;;AC7BtE;;;AAGG;MACU,eAAe,GAAG,IAAI,cAAc,CAAiB,iBAAiB;;AC4BnF;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAc,aAAa;AAE1E;MAWa,OAAO,CAAA;AAClB,IAAA,OAAO,GAAG,MAAM,CAA0B,UAAU,CAAC;AACrD,IAAA,aAAa,GAAG,MAAM,CAAc,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC7E,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAC5C,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAAgB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClF,IAAA,WAAW,GAAG,MAAM,CAAU,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;AAChF,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAEnC,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;AACzC,IAAA,QAAQ,GAAG,IAAI,eAAe,CAAkB,EAAE,CAAC;AACnD,IAAA,gBAAgB;AAChB,IAAA,oBAAoB;;AAG5B,IAAA,QAAQ;;AAGc,IAAA,IAAI;;AAGA,IAAA,QAAQ;AAElC;;;;AAIG;AAC0B,IAAA,mBAAmB;AAEhD;;;;;AAKG;AACuB,IAAA,eAAe;AAEzC;;;AAGG;AACyB,IAAA,cAAc;AAE1C;;;AAGG;AAC+B,IAAA,gBAAgB;;AAGlD,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;;IAEhF,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;;AAEjC,IAAA,SAAS;AAEjB;;;;;AAKG;AACgC,IAAA,iBAAiB;;AAGtB,IAAA,YAAY;AAE1C;;;;;;;;;;;;AAYG;AAC+B,IAAA,gBAAgB;AAElD;;;AAGG;IAEH,KAAK,GAAW,CAAC;;AAGkB,IAAA,OAAO,GACxC,IAAI,YAAY,EAAgB;;AAGE,IAAA,QAAQ,GAC1C,IAAI,YAAY,EAAkB;;AAGH,IAAA,KAAK,GAA6B,IAAI,YAAY,EAAc;;AAG9D,IAAA,OAAO,GAAoC,IAAI,YAAY,EAE3F;;AAG+B,IAAA,MAAM,GAAmC,IAAI,YAAY,EAExF;;AAGgC,IAAA,OAAO,GAAmC,IAAI,YAAY,EAE1F;AAEH;;;AAGG;AAEM,IAAA,KAAK,GAA+B,IAAI,UAAU,CACzD,CAAC,QAAkC,KAAI;AACrC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,aAAA,IAAI,CACH,GAAG,CAAC,UAAU,KAAK;AACjB,YAAA,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,UAAU,CAAC,eAAe;YAC3C,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,QAAQ,EAAE,UAAU,CAAC,QAAQ;AAC9B,SAAA,CAAC,CAAC;aAEJ,SAAS,CAAC,QAAQ,CAAC;AAEtB,QAAA,OAAO,MAAK;YACV,YAAY,CAAC,WAAW,EAAE;AAC5B,SAAC;AACH,KAAC,CACF;AAEO,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAIpC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa;AACxC,QAAA,MAAM,MAAM,GAAG,MAAM,CAAiB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACxE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE;AAChD,YAAA,kBAAkB,EAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,GAAG,MAAM,CAAC,kBAAkB,GAAG,CAAC;AAC7E,YAAA,+BAA+B,EAC7B,MAAM,IAAI,MAAM,CAAC,+BAA+B,IAAI;kBAChD,MAAM,CAAC;AACT,kBAAE,CAAC;YACP,MAAM,EAAE,MAAM,EAAE,MAAM;AACvB,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;QAE9E,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;;;;;;;;QAU9B,IAAI,aAAa,EAAE;AACjB,YAAA,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;;AAG3B,YAAA,aAAa,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBACvF,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AAClC,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAGnC;;;AAGG;IACH,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;;;IAI9C,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;;;IAIvC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;;;IAIvB,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;;AAGjC;;AAEG;IACH,mBAAmB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;;AAG5C;;;AAGG;AACH,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC;;IAG1C,eAAe,GAAA;;;;;QAKb,eAAe,CACb,MAAK;YACH,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AAEhC,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;SAE3D,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;AAGH,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,qBAAqB,CAAC;AACzD,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC;;;AAIlD,QAAA,IAAI,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;YACzD,IAAI,CAAC,kBAAkB,EAAE;;;QAI3B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;;;QAIhC,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1E,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;;IAI5D,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;;QAGrC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;;AAGtE,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACzB,SAAC,CAAC;;AAGJ,IAAA,UAAU,CAAC,MAAqB,EAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxC,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;AAG7B,IAAA,aAAa,CAAC,MAAqB,EAAA;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;QACxC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;AAErC,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,YAAA,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAI/B,IAAA,mBAAmB,CAAC,OAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO;;AAGjC,IAAA,qBAAqB,CAAC,OAAuB,EAAA;AAC3C,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;AAIhC,IAAA,uBAAuB,CAAC,WAA+B,EAAA;AACrD,QAAA,IAAI,CAAC,oBAAoB,GAAG,WAAW;;AAGzC,IAAA,yBAAyB,CAAC,WAA+B,EAAA;AACvD,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,oBAAoB,EAAE;AAC7C,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;;IAK5B,kBAAkB,GAAA;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAA4B;QACzD,IAAI,WAAW,GAAG,OAAO;AACzB,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,WAAW;gBACT,OAAO,CAAC,OAAO,KAAK;sBACf,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB;AAC3C;wBACG,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAiB;;QAGjF,IAAI,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAClE,YAAA,iBAAiB,CAAC,WAAW,EAAE,SAAS,CAAC;;QAG3C,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,IAAI,OAAO,CAAC;;;IAI/C,mBAAmB,GAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe;QAErC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAc,QAAQ,CAAC;;AAGlE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC;;;AAIxB,IAAA,WAAW,CAAC,GAAwB,EAAA;AAC1C,QAAA,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AAC/B,YAAA,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE;AACrB,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI;AACrB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc;AAC1C,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC;AACvB,sBAAE;AACE,wBAAA,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,WAAW;AAC/C,wBAAA,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;wBACvC,aAAa,EAAE,IAAI,CAAC,iBAAiB;AACtC;sBACD,IAAI;AACR,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC;AACnB,sBAAE;AACE,wBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW;AAC3C,wBAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;AACnC,wBAAA,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS;wBAC1C,aAAa,EAAE,IAAI,CAAC,iBAAiB;AACtC;sBACD,IAAI;AAER,gBAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC5B,gBAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC5B,gBAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AACtB,gBAAA,GAAG,CAAC,cAAc;AAChB,oBAAA,OAAO,cAAc,KAAK,QAAQ,IAAI;AACpC,0BAAE;AACF,0BAAE,oBAAoB,CAAC,cAAc,CAAC;AAC1C,gBAAA,GAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;AAC9C,gBAAA,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;gBACpC;AACG,qBAAA,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,EAAE;qBAC9C,uBAAuB,CAAC,WAAW;qBACnC,mBAAmB,CAAC,OAAO;AAC3B,qBAAA,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC;gBAE1D,IAAI,GAAG,EAAE;AACP,oBAAA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;;;AAGlC,SAAC,CAAC;;AAGF,QAAA,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;;AAE7C,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACzC;;;;YAKF,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa;YACrD,OAAO,MAAM,EAAE;gBACb,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBACzE,IAAI,UAAU,EAAE;AACd,oBAAA,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC;oBACnC;;AAEF,gBAAA,MAAM,GAAG,MAAM,CAAC,aAAa;;AAEjC,SAAC,CAAC;;;AAII,IAAA,aAAa,CAAC,GAAwB,EAAA;AAC5C,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,IAAG;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAC,CAAC;;;AAI1D,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,IAAG;AACpC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAC,CAAC;AAC/D,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAG;AAC7B,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACd,gBAAA,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,KAAK,EAAE,QAAQ,CAAC,KAAK;AACtB,aAAA,CAAC;;;AAIF,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,IAAG;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI;AACpC,gBAAA,IAAI,EAAE,IAAI;gBACV,YAAY,EAAE,UAAU,CAAC,YAAY;AACtC,aAAA,CAAC;AACJ,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,IAAG;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,gBAAA,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI;AACnC,gBAAA,IAAI,EAAE,IAAI;AACX,aAAA,CAAC;AACJ,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,IAAG;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,aAAa,EAAE,SAAS,CAAC,aAAa;gBACtC,YAAY,EAAE,SAAS,CAAC,YAAY;AACpC,gBAAA,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,CAAC,IAAI;AACnD,gBAAA,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI;gBACnC,sBAAsB,EAAE,SAAS,CAAC,sBAAsB;AACxD,gBAAA,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,KAAK,EAAE,SAAS,CAAC,KAAK;AACvB,aAAA,CAAC;AACJ,SAAC,CAAC;;;AAII,IAAA,eAAe,CAAC,MAAsB,EAAA;AAC5C,QAAA,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,gBAAgB,GACjB,GAAG,MAAM;AAEV,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAG,gBAAgB;AACnE,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,CAAC;QAEzC,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;;QAG1B,IAAI,iBAAiB,EAAE;AACrB,YAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB;;QAG5C,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,GAAG,YAAY;;QAGlC,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,eAAe,GAAG,eAAe;;QAGxC,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB;;QAGhD,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;;;;IAKpC,qBAAqB,GAAA;;AAE3B,QAAA,IAAI,CAAC;aACF,IAAI;;QAEH,GAAG,CAAC,OAAO,IAAG;AACZ,YAAA,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC;;;;YAK5D,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAChD,gBAAA,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGnC,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC;AAC3C,SAAC,CAAC;;AAEF,QAAA,SAAS,CAAC,CAAC,OAAwB,KAAI;YACrC,OAAO,KAAK,CACV,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CACpC;SAC/B,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAE3B,SAAS,CAAC,cAAc,IAAG;;AAE1B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;AAC7B,YAAA,MAAM,MAAM,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa;YACnD,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;AACxF,SAAC,CAAC;;uGAriBK,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAqD2B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,mBAAA,EAAA,CAAA,oBAAA,EAAA,qBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,CAAA,EAAA,cAAA,EAAA,CAAA,mBAAA,EAAA,gBAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,EAAA,gBAAgB,CAwCnB,EAAA,iBAAA,EAAA,CAAA,0BAAA,EAAA,mBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,qBAAA,EAAA,cAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,CAAA,EAAA,KAAA,EAAA,CAAA,cAAA,EAAA,OAAA,EAAA,eAAe,mWA/F9C,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAElD,OAAO,EAAA,UAAA,EAAA,CAAA;kBAVnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACnB,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,2BAA2B,EAAE,uBAAuB;AACrD,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAS,OAAA,EAAC,CAAC;AAC9D,iBAAA;wDAqBuB,IAAI,EAAA,CAAA;sBAAzB,KAAK;uBAAC,aAAa;gBAGM,QAAQ,EAAA,CAAA;sBAAjC,KAAK;uBAAC,iBAAiB;gBAOK,mBAAmB,EAAA,CAAA;sBAA/C,KAAK;uBAAC,oBAAoB;gBAQD,eAAe,EAAA,CAAA;sBAAxC,KAAK;uBAAC,iBAAiB;gBAMI,cAAc,EAAA,CAAA;sBAAzC,KAAK;uBAAC,mBAAmB;gBAMQ,gBAAgB,EAAA,CAAA;sBAAjD,KAAK;uBAAC,yBAAyB;gBAI5B,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAgB3B,iBAAiB,EAAA,CAAA;sBAAnD,KAAK;uBAAC,0BAA0B;gBAGH,YAAY,EAAA,CAAA;sBAAzC,KAAK;uBAAC,qBAAqB;gBAeM,gBAAgB,EAAA,CAAA;sBAAjD,KAAK;uBAAC,yBAAyB;gBAOhC,KAAK,EAAA,CAAA;sBADJ,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAC;gBAIvB,OAAO,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB;gBAIY,QAAQ,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB;gBAIQ,KAAK,EAAA,CAAA;sBAArC,MAAM;uBAAC,cAAc;gBAGa,OAAO,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB;gBAKU,MAAM,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe;gBAKY,OAAO,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB;gBASf,KAAK,EAAA,CAAA;sBADb,MAAM;uBAAC,cAAc;;;AC1LxB;;;;AAIG;MACU,mBAAmB,GAAG,IAAI,cAAc,CACnD,kBAAkB;AAGpB;;;;;AAKG;MAMU,gBAAgB,CAAA;;AAElB,IAAA,MAAM,GAAG,IAAI,GAAG,EAAK;;IAI9B,QAAQ,GAAY,KAAK;IAEzB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;uGATV,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAK2B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,0BAAA,EAAA,UAAA,EAAA,gBAAgB,CAP3D,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,gBAAgB,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE/D,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,kBAAkB;oBAC5B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAkB,gBAAA,EAAC,CAAC;AAC3E,iBAAA;8BAOC,QAAQ,EAAA,CAAA;sBADP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,0BAA0B,EAAE,SAAS,EAAE,gBAAgB,EAAC;;;ACDzE;MAiBa,WAAW,CAAA;AACtB,IAAA,OAAO,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC7C,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAC5C,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,MAAM,GAAG,MAAM,CAAgC,mBAAmB,EAAE;AAC1E,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;;AAGM,IAAA,iBAAiB;;AAGR,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;AAGzC,IAAA,0BAA0B;;AAG1B,IAAA,OAAO,UAAU,GAAkB,EAAE;;AAG7C,IAAA,YAAY;AAEZ;;;;AAIG;IAEH,WAAW,GAAoD,EAAE;;AAGvC,IAAA,IAAI;;AAGG,IAAA,WAAW;AAE5C;;;AAGG;IACM,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;;AAGpC,IAAA,QAAQ;;AAGtC,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;;IAElE,IAAI,QAAQ,CAAC,KAAc,EAAA;;;;;QAKzB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK;;AAE7C,IAAA,SAAS;;AAIjB,IAAA,eAAe;AAEf;;;AAGG;AAEH,IAAA,cAAc,GAAkD,MAAM,IAAI;;AAI1E,IAAA,aAAa,GAAiE,MAAM,IAAI;;AAIxF,IAAA,kBAAkB;;AAIlB,IAAA,cAAc;AAEd;;;;;;;;;;;;;AAaG;AACmC,IAAA,wBAAwB;AAE9D;;;;;;;;;;AAUG;AAEH,IAAA,SAAS;;AAIA,IAAA,OAAO,GAAsC,IAAI,YAAY,EAAuB;AAE7F;;AAEG;AAEM,IAAA,OAAO,GAAkC,IAAI,YAAY,EAAmB;AAErF;;;AAGG;AAEM,IAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB;;AAIzE,IAAA,MAAM,GAAsC,IAAI,YAAY,EAAuB;AAE5F;;;;;;AAMG;AACK,IAAA,cAAc,GAAG,IAAI,GAAG,EAAW;AAI3C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,MAAM,MAAM,GAAG,MAAM,CAAiB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAExE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC;;QAG9D,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;AACzD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI;QAE7B,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;QAG9B,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,CAAC,IAAsB,EAAE,IAA8B,KAAI;AAC5F,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;AAClD,SAAC;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,CAChC,KAAa,EACb,IAAsB,EACtB,IAA8B,KAC5B;AACF,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;AACxD,SAAC;AAED,QAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,YAAY,CAAC;AACnD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC;AACrC,QAAA,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAEjC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;;;;AAKhC,IAAA,OAAO,CAAC,IAAa,EAAA;AACnB,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC;;;AAInD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE;AAClC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;;;;AAK5E,IAAA,UAAU,CAAC,IAAa,EAAA;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;;;;AAKhC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AAE3D,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AACvC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC;;;;;IAMpD,cAAc,GAAA;AACZ,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAU,EAAE,CAAU,KAAI;AACrE,YAAA,MAAM,gBAAgB,GAAG,CAAC,CAAC;AACxB,iBAAA,iBAAiB;iBACjB,uBAAuB,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;;;;AAK1D,YAAA,OAAO,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;AACrE,SAAC,CAAC;;IAGJ,WAAW,GAAA;QACT,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;AAElD,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;AAGzC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;;AAGjC,QAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS;AAClC,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;AAIpB,IAAA,2BAA2B,CAAC,GAA6B,EAAA;AAC/D,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC;AACP,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3D,iBAAA,SAAS,CAAC,KAAK,IAAI,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;AAGjD,QAAA,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AAC/B,YAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,IAAG;AACxD,gBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,oBAAA,MAAM,qBAAqB,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AAEnF,oBAAA,IAAI,CAAC,qBAAqB,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC7E,wBAAA,OAAO,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAA,CAAA,CAAG,CAAC;;AAGlF,oBAAA,OAAO,qBAAsB;;AAG/B,gBAAA,OAAO,IAAI;AACb,aAAC,CAAC;AAEF,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;oBAChC,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AACjC,wBAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEvB,iBAAC,CAAC;;;;AAKJ,YAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AACpC,gBAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC;AAC5B,qBAAA,2BAA2B,CAAC,IAAI,CAAC,OAAO;AACxC,qBAAA,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC;AAC9D,gBAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;;;AAI1D,gBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;;AAGxC,YAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE;AACjC,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,wBAAwB,CAAC;AAEzF,gBAAA,IAAI,CAAC,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;oBACjE,MAAM,IAAI,KAAK,CACb,CAAA,uEAAA,EAA0E,IAAI,CAAC,wBAAwB,CAAG,CAAA,CAAA,CAC3G;;AAGH,gBAAA,GAAG,CAAC,oBAAoB,CAAC,SAAwB,CAAC;;AAGpD,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC5B,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC5B,YAAA,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;AAC1C,YAAA,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB;YAChD,GAAG,CAAC,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;AACjE,YAAA,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;YAC9B;iBACG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;AACzF,iBAAA,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,SAAC,CAAC;;;AAII,IAAA,aAAa,CAAC,GAA6B,EAAA;AACjD,QAAA,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AAC/B,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,IAAG;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;gBACrB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,aAAA,CAAC;AACJ,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAG;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AACtB,aAAA,CAAC;AACF,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAG;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,aAAa,EAAE,KAAK,CAAC,aAAa;gBAClC,YAAY,EAAE,KAAK,CAAC,YAAY;AAChC,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AACtB,aAAA,CAAC;AACJ,SAAC,CAAC;AAEF,QAAA,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,IAAG;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,aAAa,EAAE,SAAS,CAAC,aAAa;gBACtC,YAAY,EAAE,SAAS,CAAC,YAAY;AACpC,gBAAA,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,CAAC,IAAI;AACnD,gBAAA,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI;AACnC,gBAAA,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI;gBACzB,sBAAsB,EAAE,SAAS,CAAC,sBAAsB;gBACxD,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,KAAK,EAAE,SAAS,CAAC,KAAK;AACvB,aAAA,CAAC;;;AAIF,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;QAEF,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,MAC1D,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CACvC;;;AAIK,IAAA,eAAe,CAAC,MAAsB,EAAA;AAC5C,QAAA,MAAM,EAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAC,GAC1F,MAAM;AAER,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAG,gBAAgB;AACnE,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,GAAG,KAAK,GAAG,eAAe;AACxE,QAAA,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,IAAI,IAAI,GAAG,KAAK,GAAG,sBAAsB;AACzF,QAAA,IAAI,CAAC,WAAW,GAAG,eAAe,IAAI,UAAU;QAEhD,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;;;;AAKpB,IAAA,iBAAiB,CAAC,KAAgB,EAAA;AACxC,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;AAC9B,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC;;uGAjYzB,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,CAAA,wBAAA,EAAA,aAAA,CAAA,EAAA,IAAA,EAAA,CAAA,iBAAA,EAAA,MAAA,CAAA,EAAA,WAAA,EAAA,CAAA,wBAAA,EAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,EAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,EAAA,UAAA,EAiD2B,gBAAgB,CAcT,EAAA,eAAA,EAAA,CAAA,4BAAA,EAAA,iBAAA,EAAA,gBAAgB,8MAeb,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,2BAAA,EAAA,gBAAA,CAAA,EAAA,wBAAA,EAAA,CAAA,6BAAA,EAAA,0BAAA,CAAA,EAAA,SAAA,EAAA,CAAA,sBAAA,EAAA,WAAA,EAkCzB,gBAAgB,CA7HvD,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,oBAAA,EAAA,OAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,8BAAA,EAAA,UAAA,EAAA,8BAAA,EAAA,2BAAA,EAAA,+BAAA,EAAA,4BAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA;;AAET,YAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAC;AACnD,YAAA,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAC;AACnD,SAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FASU,WAAW,EAAA,UAAA,EAAA,CAAA;kBAhBvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8BAA8B;AACxC,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,SAAS,EAAE;;AAET,wBAAA,EAAC,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAC;AACnD,wBAAA,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,aAAa,EAAC;AACnD,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,gCAAgC,EAAE,2BAA2B;AAC7D,wBAAA,iCAAiC,EAAE,4BAA4B;AAChE,qBAAA;AACF,iBAAA;wDAgCC,WAAW,EAAA,CAAA;sBADV,KAAK;uBAAC,wBAAwB;gBAIL,IAAI,EAAA,CAAA;sBAA7B,KAAK;uBAAC,iBAAiB;gBAGS,WAAW,EAAA,CAAA;sBAA3C,KAAK;uBAAC,wBAAwB;gBAMtB,EAAE,EAAA,CAAA;sBAAV;gBAG6B,QAAQ,EAAA,CAAA;sBAArC,KAAK;uBAAC,qBAAqB;gBAIxB,QAAQ,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAelE,eAAe,EAAA,CAAA;sBADd,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,4BAA4B,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAQzE,cAAc,EAAA,CAAA;sBADb,KAAK;uBAAC,2BAA2B;gBAKlC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,0BAA0B;gBAKjC,kBAAkB,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,+BAA+B,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAK5E,cAAc,EAAA,CAAA;sBADb,KAAK;uBAAC,2BAA2B;gBAiBI,wBAAwB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,6BAA6B;gBAcpC,SAAS,EAAA,CAAA;sBADR,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,sBAAsB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAK1D,OAAO,EAAA,CAAA;sBADf,MAAM;uBAAC,oBAAoB;gBAOnB,OAAO,EAAA,CAAA;sBADf,MAAM;uBAAC,oBAAoB;gBAQnB,MAAM,EAAA,CAAA;sBADd,MAAM;uBAAC,mBAAmB;gBAKlB,MAAM,EAAA,CAAA;sBADd,MAAM;uBAAC,mBAAmB;;;ACrK7B;;;;AAIG;MACU,gBAAgB,GAAG,IAAI,cAAc,CAAiB,gBAAgB;AAEnF;;;AAGG;MAKU,cAAc,CAAA;AACzB,IAAA,WAAW,GAAG,MAAM,CAAiB,WAAW,CAAC;IAEzC,KAAK,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAGhD,IAAA,IAAI;;IAGyB,SAAS,GAAY,KAAK;AAIhE,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC,IAAI,CAAC;;IAGvC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,IAAI,CAAC;;uGAlB9B,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EASN,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAXxB,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE1D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;oBACvC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AACtE,iBAAA;wDAOU,IAAI,EAAA,CAAA;sBAAZ;gBAGqC,SAAS,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AChCtC;;;;AAIG;MACU,oBAAoB,GAAG,IAAI,cAAc,CAAqB,oBAAoB;AAE/F;;;AAGG;MAKU,kBAAkB,CAAA;AAC7B,IAAA,WAAW,GAAG,MAAM,CAAiB,WAAW,CAAC;IAEzC,KAAK,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAGhD,IAAA,IAAI;AAIb,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC,IAAI,CAAC;;IAG3C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC,IAAI,CAAC;;uGAflC,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAFlB,CAAC,EAAC,OAAO,EAAE,oBAAoB,EAAE,WAAW,EAAE,kBAAkB,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAElE,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iCAAiC;oBAC3C,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,oBAAoB,EAAE,WAAW,EAAoB,kBAAA,EAAC,CAAC;AAC9E,iBAAA;wDAOU,IAAI,EAAA,CAAA;sBAAZ;;;ACdH,MAAM,oBAAoB,GAAG;IAC3B,WAAW;IACX,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,cAAc;IACd,kBAAkB;CACnB;MAOY,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAbzB,WAAW;YACX,gBAAgB;YAChB,OAAO;YACP,aAAa;YACb,cAAc;YACd,kBAAkB,CAAA,EAAA,OAAA,EAAA,CAKR,mBAAmB,EAV7B,WAAW;YACX,gBAAgB;YAChB,OAAO;YACP,aAAa;YACb,cAAc;YACd,kBAAkB,CAAA,EAAA,CAAA;AAQP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAFd,SAAA,EAAA,CAAC,QAAQ,CAAC,YADX,mBAAmB,CAAA,EAAA,CAAA;;2FAGlB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,oBAAoB;AAC7B,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,GAAG,oBAAoB,CAAC;oBACvD,SAAS,EAAE,CAAC,QAAQ,CAAC;AACtB,iBAAA;;;;;"}