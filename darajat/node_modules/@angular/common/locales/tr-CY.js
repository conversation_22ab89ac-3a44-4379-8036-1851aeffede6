/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["tr-CY", [["öö", "ös"], ["ÖÖ", "ÖS"], u], [["ÖÖ", "ÖS"], u, u], [["P", "P", "S", "Ç", "P", "C", "C"], ["<PERSON>", "<PERSON>zt", "Sal", "Çar", "Per", "Cum", "Cmt"], ["Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"], ["<PERSON>", "Pt", "Sa", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ct"]], u, [["O", "<PERSON>", "M", "N", "M", "H", "T", "A", "E", "E", "K", "A"], ["Oca", "<PERSON><PERSON>", "<PERSON>", "Nis", "May", "Haz", "Tem", "Ağu", "Eyl", "<PERSON>ki", "Kas", "Ara"], ["<PERSON>cak", "Şubat", "<PERSON>", "Nisan", "<PERSON>ıs", "<PERSON><PERSON>ran", "<PERSON><PERSON>uz", "<PERSON>ğustos", "<PERSON>ylül", "<PERSON>kim", "Kasım", "Aralık"]], u, [["MÖ", "MS"], u, ["Milattan Önce", "Milattan Sonra"]], 1, [6, 0], ["d.MM.y", "d MMM y", "d MMMM y", "d MMMM y EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "%#,##0", "¤#,##0.00", "#E0"], "EUR", "€", "Euro", { "AUD": ["AU$", "$"], "BYN": [u, "р."], "PHP": [u, "₱"], "RON": [u, "L"], "RUR": [u, "р."], "THB": ["฿"], "TRY": ["₺"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=tr-CY.js.map