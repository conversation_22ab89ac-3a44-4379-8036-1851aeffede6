/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['teo'] = ["teo",[["Taparachu","Ebongi"],u,u],u,[["J","B","A","U","U","K","S"],["Jum","Bar","Aar","Uni","Ung","Kan","Sab"],["Nakaejuma","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>’on","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>i","Ung","Kan","Sab"]],u,[["R","M","K","D","M","M","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>r","Muk","<PERSON>wa","<PERSON>n","<PERSON>","<PERSON>d","<PERSON>l","<PERSON>ed","<PERSON>k","<PERSON>ib","<PERSON>","<PERSON>o"],["<PERSON>ara","<PERSON>muk","<PERSON>wamg’","<PERSON>dung’el","<PERSON>uk","<PERSON>modok’king’ol","Ojola","Opedel","Osokosokoma","Otibar","Olabor","Opoo"]],u,[["KK","BK"],u,["Kabla ya Christo","Baada ya Christo"]],1,[0,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"UGX","USh","Ango’otol lok’ Uganda",{"JPY":["JP¥","¥"],"UGX":["USh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    