import { CdkDragDrop } from '@angular/cdk/drag-drop';
import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, EventEmitter, ElementRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { FilterService, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { ButtonProps } from 'primeng/button';
import { Listbox, ListboxChangeEvent } from 'primeng/listbox';
import { Nullable } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 * Callbacks to invoke on filter.
 * @group Interface
 */
interface OrderListFilterOptions {
    filter?: (value?: any) => void;
    reset?: () => void;
}
/**
 * Custom change event.
 * @see {@link OrderList.selectionChange}
 * @group Events
 */
interface OrderListSelectionChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Current selected values.
     */
    value: any[];
}
/**
 * Custom change event.
 * @see {@link OrderList.selectionChange}
 * @group Events
 */
interface OrderListFilterEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Filtered options.
     */
    value: any[];
}
/**
 * Defines valid templates in OrderList.
 * @group Templates
 */
interface OrderListTemplates {
    /**
     * Custom header template.
     * @param {Object} context - item data.
     */
    item(context: {
        /**
         * Item instance.
         */
        $implicit: any;
        /**
         * Index of the item.
         */
        index: number;
    }): TemplateRef<{
        $implicit: any;
        index: number;
    }>;
    /**
     * Custom header template.
     */
    header(): TemplateRef<any>;
    /**
     * Custom filter template.
     * @param {OrderListFilterOptions} options - filter options.
     */
    filter(context: {
        /**
         * Filter options.
         */
        options: OrderListFilterOptions;
    }): TemplateRef<{
        options: OrderListFilterOptions;
    }>;
    /**
     * Custom empty filter template.
     */
    emptyfilter(): TemplateRef<any>;
    /**
     * Custom empty template.
     */
    empty(): TemplateRef<any>;
    /**
     * Custom clear icon template.
     */
    clearicon(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filtericon(): TemplateRef<any>;
    /**
     * Custom move up icon template.
     */
    moveupicon(): TemplateRef<any>;
    /**
     * Custom move top icon template.
     */
    movetopicon(): TemplateRef<any>;
    /**
     * Custom move down icon template.
     */
    movedownicon(): TemplateRef<any>;
    /**
     * Custom move bottom icon template.
     */
    movebottomicon(): TemplateRef<any>;
}

/**
 *
 * OrderList is used to maneged the order of a collection.
 *
 * [Live Demo](https://primeng.org/orderlist)
 *
 * @module orderliststyle
 *
 */
declare enum OrderListClasses {
    /**
     * Class name of the root element
     */
    root = "p-orderlist",
    /**
     * Class name of the controls element
     */
    controls = "p-orderlist-controls"
}
declare class OrderListStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: () => string[];
        controls: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<OrderListStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<OrderListStyle>;
}
interface OrderListStyle extends BaseStyle {
}

/**
 * OrderList is used to manage the order of a collection.
 * @group Components
 */
declare class OrderList extends BaseComponent implements AfterContentInit {
    /**
     * Text for the caption.
     * @group Props
     */
    header: string | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Specifies one or more IDs in the DOM that labels the input field.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Inline style of the list element.
     * @group Props
     */
    listStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * A boolean value that indicates whether the component should be responsive.
     * @group Props
     */
    responsive: boolean | undefined;
    /**
     * When specified displays an input field to filter the items on keyup and decides which fields to search against.
     * @group Props
     */
    filterBy: string | undefined;
    /**
     * Placeholder of the filter input.
     * @group Props
     */
    filterPlaceholder: string | undefined;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * When true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean;
    /**
     * Whether to enable dragdrop based reordering.
     * @group Props
     */
    dragdrop: boolean;
    /**
     * Defines the location of the buttons with respect to the list.
     * @group Props
     */
    controlsPosition: 'left' | 'right';
    /**
     * Defines a string that labels the filter input.
     * @group Props
     */
    ariaFilterLabel: string | undefined;
    /**
     * Defines how the items are filtered.
     * @group Props
     */
    filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte';
    /**
     * Indicates the width of the screen at which the component should change its behavior.
     * @group Props
     */
    breakpoint: string;
    /**
     * Whether to displays rows with alternating colors.
     * @group Props
     */
    stripedRows: boolean | undefined;
    /**
     * When present, it specifies that the component should be disabled.
     * @group Props
     */
    disabled: boolean;
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.
     * @group Props
     */
    trackBy: Function;
    /**
     * Height of the viewport, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * Whether to focus on the first visible or selected element.
     * @group Props
     */
    autoOptionFocus: boolean;
    /**
     * A list of values that are currently selected.
     * @group Props
     */
    set selection(val: any[]);
    get selection(): any[];
    /**
     * Array of values to be displayed in the component.
     * It represents the data source for the list of items.
     * @group Props
     */
    set value(val: any[] | undefined);
    get value(): any[] | undefined;
    /**
     * Used to pass all properties of the ButtonProps to the Button component.
     * @group Props
     */
    buttonProps: ButtonProps;
    /**
     * Used to pass all properties of the ButtonProps to the move up button inside the component.
     * @group Props
     */
    moveUpButtonProps: ButtonProps;
    /**
     * Used to pass all properties of the ButtonProps to the move top button inside the component.
     * @group Props
     */
    moveTopButtonProps: ButtonProps;
    /**
     * Used to pass all properties of the ButtonProps to the move down button inside the component.
     * @group Props
     */
    moveDownButtonProps: ButtonProps;
    /**
     * Used to pass all properties of the ButtonProps to the move bottom button inside the component.
     * @group Props
     */
    moveBottomButtonProps: ButtonProps;
    /**
     * Callback to invoke on selection change.
     * @param {*} any - selection instance.
     * @group Emits
     */
    selectionChange: EventEmitter<any>;
    /**
     * Callback to invoke when list is reordered.
     * @param {*} any - list instance.
     * @group Emits
     */
    onReorder: EventEmitter<any>;
    /**
     * Callback to invoke when selection changes.
     * @param {OrderListSelectionChangeEvent} event - Custom change event.
     * @group Emits
     */
    onSelectionChange: EventEmitter<OrderListSelectionChangeEvent>;
    /**
     * Callback to invoke when filtering occurs.
     * @param {OrderListFilterEvent} event - Custom filter event.
     * @group Emits
     */
    onFilterEvent: EventEmitter<OrderListFilterEvent>;
    /**
     * Callback to invoke when the list is focused
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the list is blurred
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    listViewChild: Listbox;
    filterViewChild: Nullable<ElementRef>;
    /**
     * Custom item template.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    /**
     * Custom empty template.
     * @group Templates
     */
    emptyMessageTemplate: TemplateRef<any> | undefined;
    /**
     * Custom empty filter template.
     * @group Templates
     */
    emptyFilterMessageTemplate: TemplateRef<any> | undefined;
    /**
     * Custom filter template.
     * @group Templates
     */
    filterTemplate: TemplateRef<any> | undefined;
    /**
     * Custom header template.
     * @group Templates
     */
    headerTemplate: TemplateRef<any> | undefined;
    /**
     * Custom move up icon template.
     * @group Templates
     */
    moveUpIconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom move top icon template.
     * @group Templates
     */
    moveTopIconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom move down icon template.
     * @group Templates
     */
    moveDownIconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom move bottom icon template.
     * @group Templates
     */
    moveBottomIconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom filter icon template.
     * @group Templates
     */
    filterIconTemplate: TemplateRef<any> | undefined;
    get moveUpAriaLabel(): string;
    get moveTopAriaLabel(): string;
    get moveDownAriaLabel(): string;
    get moveBottomAriaLabel(): string;
    _componentStyle: OrderListStyle;
    filterOptions: Nullable<OrderListFilterOptions>;
    d_selection: any[];
    movedUp: Nullable<boolean>;
    movedDown: Nullable<boolean>;
    itemTouched: Nullable<boolean>;
    styleElement: any;
    id: string;
    filterValue: Nullable<string>;
    visibleOptions: Nullable<any[]>;
    _value: any[] | undefined;
    filterService: FilterService;
    getButtonProps(direction: string): ButtonProps;
    ngOnInit(): void;
    templates: Nullable<QueryList<PrimeTemplate>>;
    _itemTemplate: TemplateRef<any> | undefined;
    _emptyMessageTemplate: TemplateRef<any> | undefined;
    _emptyFilterMessageTemplate: TemplateRef<any> | undefined;
    _filterTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _moveUpIconTemplate: TemplateRef<any> | undefined;
    _moveTopIconTemplate: TemplateRef<any> | undefined;
    _moveDownIconTemplate: TemplateRef<any> | undefined;
    _moveBottomIconTemplate: TemplateRef<any> | undefined;
    _filterIconTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    onChangeSelection(e: ListboxChangeEvent): void;
    onFilterKeyup(event: KeyboardEvent): void;
    filter(): void;
    /**
     * Callback to invoke on filter reset.
     * @group Method
     */
    resetFilter(): void;
    isItemVisible(item: any): boolean | undefined;
    isSelected(item: any): boolean;
    isEmpty(): boolean;
    moveUp(): void;
    moveTop(): void;
    moveDown(): void;
    moveBottom(): void;
    onDrop(event: CdkDragDrop<string[]>): void;
    onListFocus(event: any): void;
    onListBlur(event: any): void;
    getVisibleOptions(): any[];
    moveDisabled(): boolean;
    createStyle(): void;
    destroyStyle(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<OrderList, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<OrderList, "p-orderList, p-orderlist, p-order-list", never, { "header": { "alias": "header"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "listStyle": { "alias": "listStyle"; "required": false; }; "responsive": { "alias": "responsive"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "filterPlaceholder": { "alias": "filterPlaceholder"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "dragdrop": { "alias": "dragdrop"; "required": false; }; "controlsPosition": { "alias": "controlsPosition"; "required": false; }; "ariaFilterLabel": { "alias": "ariaFilterLabel"; "required": false; }; "filterMatchMode": { "alias": "filterMatchMode"; "required": false; }; "breakpoint": { "alias": "breakpoint"; "required": false; }; "stripedRows": { "alias": "stripedRows"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "trackBy": { "alias": "trackBy"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "autoOptionFocus": { "alias": "autoOptionFocus"; "required": false; }; "selection": { "alias": "selection"; "required": false; }; "value": { "alias": "value"; "required": false; }; "buttonProps": { "alias": "buttonProps"; "required": false; }; "moveUpButtonProps": { "alias": "moveUpButtonProps"; "required": false; }; "moveTopButtonProps": { "alias": "moveTopButtonProps"; "required": false; }; "moveDownButtonProps": { "alias": "moveDownButtonProps"; "required": false; }; "moveBottomButtonProps": { "alias": "moveBottomButtonProps"; "required": false; }; }, { "selectionChange": "selectionChange"; "onReorder": "onReorder"; "onSelectionChange": "onSelectionChange"; "onFilterEvent": "onFilterEvent"; "onFocus": "onFocus"; "onBlur": "onBlur"; }, ["itemTemplate", "emptyMessageTemplate", "emptyFilterMessageTemplate", "filterTemplate", "headerTemplate", "moveUpIconTemplate", "moveTopIconTemplate", "moveDownIconTemplate", "moveBottomIconTemplate", "filterIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_responsive: unknown;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_dragdrop: unknown;
    static ngAcceptInputType_stripedRows: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_autoOptionFocus: unknown;
}
declare class OrderListModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<OrderListModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<OrderListModule, never, [typeof OrderList, typeof i1.SharedModule], [typeof OrderList, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<OrderListModule>;
}

export { OrderList, OrderListClasses, OrderListModule, OrderListStyle };
export type { OrderListFilterEvent, OrderListFilterOptions, OrderListSelectionChangeEvent, OrderListTemplates };
