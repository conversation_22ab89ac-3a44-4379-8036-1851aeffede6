import * as i0 from '@angular/core';
import { Provider, ElementRef, EventEmitter } from '@angular/core';
import { Validator, AbstractControl } from '@angular/forms';

/**
 * A builtin pattern included within the KeyFilter component.
 * @see {@link KeyFilter.pKeyFilter}
 */
type KeyFilterPattern = 'pint' | 'int' | 'pnum' | 'money' | 'num' | 'hex' | 'email' | 'alpha' | 'alphanum';

declare const KEYFILTER_VALIDATOR: Provider;
/**
 * KeyFilter Directive is a built-in feature of InputText to restrict user input based on a regular expression.
 * @group Components
 */
declare class KeyFilter implements Validator {
    private document;
    private platformId;
    el: ElementRef;
    /**
     * When enabled, instead of blocking keys, input is validated internally to test against the regular expression.
     * @group Props
     */
    pValidateOnly: boolean | undefined;
    /**
     * Sets the pattern for key filtering.
     * @group Props
     */
    set pattern(_pattern: RegExp | KeyFilterPattern | null | undefined);
    get pattern(): RegExp | KeyFilterPattern | null | undefined;
    /**
     * Emits a value whenever the ngModel of the component changes.
     * @param {(string | number)} modelValue - Custom model change event.
     * @group Emits
     */
    ngModelChange: EventEmitter<string | number>;
    regex: RegExp;
    _pattern: RegExp | KeyFilterPattern | null | undefined;
    isAndroid: boolean;
    lastValue: any;
    constructor(document: Document, platformId: any, el: ElementRef);
    isNavKeyPress(e: KeyboardEvent): boolean;
    isSpecialKey(e: KeyboardEvent): boolean;
    getKey(e: KeyboardEvent): any;
    getCharCode(e: KeyboardEvent): number;
    findDelta(value: string, prevValue: string): string;
    isValidChar(c: string): boolean;
    isValidString(str: string): boolean;
    onInput(e: KeyboardEvent): void;
    onKeyPress(e: KeyboardEvent): void;
    onPaste(e: ClipboardEvent): void;
    validate(c: AbstractControl): {
        [key: string]: any;
    } | any;
    static ɵfac: i0.ɵɵFactoryDeclaration<KeyFilter, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<KeyFilter, "[pKeyFilter]", never, { "pValidateOnly": { "alias": "pValidateOnly"; "required": false; }; "pattern": { "alias": "pKeyFilter"; "required": false; }; }, { "ngModelChange": "ngModelChange"; }, never, never, true, never>;
    static ngAcceptInputType_pValidateOnly: unknown;
}
declare class KeyFilterModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<KeyFilterModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<KeyFilterModule, never, [typeof KeyFilter], [typeof KeyFilter]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<KeyFilterModule>;
}

export { KEYFILTER_VALIDATOR, KeyFilter, KeyFilterModule };
export type { KeyFilterPattern };
