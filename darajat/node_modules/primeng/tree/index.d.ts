import * as i0 from '@angular/core';
import { TemplateRef, OnInit, AfterContentInit, OnChanges, OnDestroy, EventEmitter, ElementRef, SimpleChanges } from '@angular/core';
import * as i1 from 'primeng/api';
import { TreeNode, ScrollerOptions, BlockableUI, TreeDragDropService } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Scroller } from 'primeng/scroller';
import { Nullable } from 'primeng/ts-helpers';
import { Subscription } from 'rxjs';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Tree is used to display hierarchical data.
 *
 * [Live Demo](https://www.primeng.org/tree/)
 *
 * @module treestyle
 *
 */
declare enum TreeClasses {
    /**
     * Class name of the root element
     */
    root = "p-tree",
    /**
     * Class name of the mask element
     */
    mask = "p-tree-mask",
    /**
     * Class name of the loading icon element
     */
    loadingIcon = "p-tree-loading-icon",
    /**
     * Class name of the filter input element
     */
    pcFilterInput = "p-tree-filter-input",
    /**
     * Class name of the wrapper element
     */
    wrapper = "p-tree-root",
    /**
     * Class name of the root children element
     */
    rootChildren = "p-tree-root-children",
    /**
     * Class name of the node element
     */
    node = "p-tree-node",
    /**
     * Class name of the node content element
     */
    nodeContent = "p-tree-node-content",
    /**
     * Class name of the node toggle button element
     */
    nodeToggleButton = "p-tree-node-toggle-button",
    /**
     * Class name of the node toggle icon element
     */
    nodeToggleIcon = "p-tree-node-toggle-icon",
    /**
     * Class name of the node checkbox element
     */
    nodeCheckbox = "p-tree-node-checkbox",
    /**
     * Class name of the node icon element
     */
    nodeIcon = "p-tree-node-icon",
    /**
     * Class name of the node label element
     */
    nodeLabel = "p-tree-node-label",
    /**
     * Class name of the node children element
     */
    nodeChildren = "p-tree-node-children",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-tree-empty-message",
    /**
     * Class name of the drop point element
     */
    dropPoint = "p-tree-node-droppoint"
}
declare class TreeStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-tree-selectable': boolean;
            'p-tree-loading': any;
            'p-tree-flex-scrollable': boolean;
            'p-tree-node-dragover': any;
        })[];
        mask: string;
        loadingIcon: string;
        pcFilterInput: string;
        wrapper: string;
        rootChildren: string;
        node: ({ instance }: {
            instance: any;
        }) => {
            'p-tree-node': boolean;
            'p-tree-node-leaf': any;
        };
        nodeContent: ({ instance }: {
            instance: any;
        }) => {
            'p-tree-node-content': boolean;
            'p-tree-node-selectable': any;
            'p-tree-node-dragover': any;
            'p-tree-node-selected': any;
        };
        nodeToggleButton: string;
        nodeToggleIcon: string;
        nodeCheckbox: string;
        nodeIcon: string;
        nodeLabel: string;
        nodeChildren: string;
        emptyMessage: string;
        dropPoint: ({ param }: {
            param: any;
        }) => (string | {
            'p-tree-node-droppoint-active': any;
        })[];
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TreeStyle>;
}
interface TreeStyle extends BaseStyle {
}

/**
 * Custom node select event.
 * @see {@link Tree.onNodeSelect}
 * @group Events
 */
interface TreeNodeSelectEvent {
    /**
     * Browser event
     */
    originalEvent: Event;
    /**
     * Node instance.
     */
    node: TreeNode<any>;
}
/**
 * Custom node unselect event.
 * @see {@link Tree.onNodeUnSelect}
 * @extends {TreeNodeSelectEvent}
 * @group Events
 */
interface TreeNodeUnSelectEvent extends TreeNodeSelectEvent {
}
/**
 * Custom node expand event.
 * @see {@link Tree.onNodeExpand}
 * @extends {TreeNodeSelectEvent}
 * @group Events
 */
interface TreeNodeExpandEvent extends TreeNodeSelectEvent {
}
/**
 * Custom node collapse event.
 * @see {@link Tree.onNodeCollapse}
 * @extends {TreeNodeSelectEvent}
 * @group Events
 */
interface TreeNodeCollapseEvent extends TreeNodeSelectEvent {
}
/**
 * Custom context menu select event.
 * @see {@link Tree.onNodeContextMenuSelect}
 * @extends {TreeNodeSelectEvent}
 * @group Events
 */
interface TreeNodeContextMenuSelectEvent extends TreeNodeSelectEvent {
}
/**
 * Custom node double click event.
 * @see {@link Tree.onNodeDoubleClick}
 * @extends {TreeNodeSelectEvent}
 * @group Events
 */
interface TreeNodeDoubleClickEvent extends TreeNodeSelectEvent {
}
/**
 * Custom node drop event.
 * @see {@link Tree.onNodeDrop}
 * @group Events
 */
interface TreeNodeDropEvent {
    /**
     * Browser drag event.
     */
    originalEvent?: DragEvent;
    /**
     * Dragged node instance.
     */
    dragNode?: TreeNode<any> | null;
    /**
     * Dropped node instance.
     */
    dropNode?: TreeNode<any> | null;
    /**
     * Index of the dragged node.
     */
    index?: number;
    /**
     * Callback to invoke on drop.
     */
    accept?: Function;
}
/**
 * Custom lazy load event.
 * @see {@link Tree.onLazyLoad}
 * @group Events
 */
interface TreeLazyLoadEvent {
    /**
     * First element index in viewport.
     */
    first: number;
    /**
     * Last element index in viewport.
     */
    last: number;
}
/**
 * Custom scroll index change event.
 * @see {@link Tree.onScrollIndexChange}
 * @group Events
 */
interface TreeScrollIndexChangeEvent extends TreeLazyLoadEvent {
}
/**
 * Custom scroll event.
 * @see {@link Tree.onScroll}
 * @group Events
 */
interface TreeScrollEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
}
/**
 * Custom filter event.
 * @see {@link Tree.onFilter}
 * @group Events
 */
interface TreeFilterEvent {
    /**
     * Filter value.
     */
    filter: string;
    /**
     * Filtered nodes.
     */
    filteredValue: TreeNode<any>[] | null | undefined;
}
/**
 * Defines valid templates in Tree.
 * @group Templates
 */
interface TreeTemplates {
    /**
     * Custom header template.
     */
    header(): TemplateRef<any>;
    /**
     * Custom empty message template.
     */
    empty(): TemplateRef<any>;
    /**
     * Custom footer template.
     */
    footer(): TemplateRef<any>;
    /**
     * Custom loader template.
     * @param {Object} context - loader options.
     */
    loader(context: {
        /**
         * Scroller options.
         * @see {@link ScrollerOptions}
         */
        options: ScrollerOptions;
    }): TemplateRef<{
        options: ScrollerOptions;
    }>;
    /**
     * Custom toggler icon template.
     * @param {Object} context - expand data.
     */
    togglericon(context: {
        /**
         * Expanded state of the node.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom checkbox icon template.
     * @param {Object} context - node data.
     */
    checkboxicon(context: {
        /**
         * Checked state of the node.
         */
        $implicit: boolean;
        /**
         * Partial selection state of the node.
         */
        partialSelected: boolean;
    }): TemplateRef<{
        $implicit: boolean;
        partialSelected: boolean;
    }>;
    /**
     * Custom loading icon template.
     */
    loadingicon(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filtericon(): TemplateRef<any>;
}

declare class UITreeNode extends BaseComponent implements OnInit {
    static ICON_CLASS: string;
    rowNode: any;
    node: TreeNode<any> | undefined;
    parentNode: TreeNode<any> | undefined;
    root: boolean | undefined;
    index: number | undefined;
    firstChild: boolean | undefined;
    lastChild: boolean | undefined;
    level: number | undefined;
    indentation: number | undefined;
    itemSize: number | undefined;
    loadingMode: string;
    tree: Tree;
    timeout: any;
    draghoverPrev: boolean | undefined;
    draghoverNext: boolean | undefined;
    draghoverNode: boolean | undefined;
    _componentStyle: TreeStyle;
    get selected(): boolean;
    get checked(): boolean;
    get nodeClass(): {
        'p-tree-node': boolean;
        'p-tree-node-leaf': any;
    };
    get selectable(): boolean;
    ngOnInit(): void;
    getIcon(): string;
    isLeaf(): boolean;
    toggle(event: Event): void;
    expand(event: Event): void;
    collapse(event: Event): void;
    onNodeClick(event: MouseEvent): void;
    onNodeKeydown(event: KeyboardEvent): void;
    onNodeTouchEnd(): void;
    onNodeRightClick(event: MouseEvent): void;
    onNodeDblClick(event: MouseEvent): void;
    isSelected(): boolean;
    isSameNode(event: any): any;
    onDropPoint(event: DragEvent, position: number): void;
    processPointDrop(event: any): void;
    createDropPointEventMetadata(position: number): {
        dragNode: TreeNode<any>;
        dragNodeIndex: number;
        dragNodeSubNodes: TreeNode<any>[];
        dropNode: TreeNode<any>;
        index: number;
        position: number;
    };
    onDropPointDragOver(event: any): void;
    onDropPointDragEnter(event: Event, position: number): void;
    onDropPointDragLeave(event: Event): void;
    onDragStart(event: any): void;
    onDragStop(event: any): void;
    onDropNodeDragOver(event: any): void;
    onDropNode(event: any): void;
    createDropNodeEventMetadata(): {
        dragNode: TreeNode<any>;
        dragNodeIndex: number;
        dragNodeSubNodes: TreeNode<any>[];
        dropNode: TreeNode<any>;
    };
    processNodeDrop(event: any): void;
    onDropNodeDragEnter(event: any): void;
    onDropNodeDragLeave(event: any): void;
    onKeyDown(event: KeyboardEvent): void;
    onArrowUp(event: KeyboardEvent): void;
    onArrowDown(event: KeyboardEvent): void;
    onArrowRight(event: KeyboardEvent): void;
    onArrowLeft(event: KeyboardEvent): boolean;
    onEnter(event: KeyboardEvent): void;
    setAllNodesTabIndexes(): void;
    setTabIndexForSelectionMode(event: any, nodeTouched: any): void;
    findNextSiblingOfAncestor(nodeElement: any): any;
    findLastVisibleDescendant(nodeElement: any): any;
    getParentNodeElement(nodeElement: HTMLElement | Element): HTMLElement;
    focusNode(element: any): void;
    focusRowChange(firstFocusableRow: any, currentFocusedRow: any, lastVisibleDescendant?: any): void;
    focusVirtualNode(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<UITreeNode, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<UITreeNode, "p-treeNode", never, { "rowNode": { "alias": "rowNode"; "required": false; }; "node": { "alias": "node"; "required": false; }; "parentNode": { "alias": "parentNode"; "required": false; }; "root": { "alias": "root"; "required": false; }; "index": { "alias": "index"; "required": false; }; "firstChild": { "alias": "firstChild"; "required": false; }; "lastChild": { "alias": "lastChild"; "required": false; }; "level": { "alias": "level"; "required": false; }; "indentation": { "alias": "indentation"; "required": false; }; "itemSize": { "alias": "itemSize"; "required": false; }; "loadingMode": { "alias": "loadingMode"; "required": false; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_root: unknown;
    static ngAcceptInputType_index: unknown;
    static ngAcceptInputType_firstChild: unknown;
    static ngAcceptInputType_lastChild: unknown;
    static ngAcceptInputType_level: unknown;
    static ngAcceptInputType_indentation: unknown;
    static ngAcceptInputType_itemSize: unknown;
}
/**
 * Tree is used to display hierarchical data.
 * @group Components
 */
declare class Tree extends BaseComponent implements OnInit, AfterContentInit, OnChanges, OnDestroy, BlockableUI {
    dragDropService: TreeDragDropService;
    /**
     * An array of treenodes.
     * @group Props
     */
    value: TreeNode<any> | TreeNode<any>[] | any[] | any;
    /**
     * Defines the selection mode.
     * @group Props
     */
    selectionMode: 'single' | 'multiple' | 'checkbox' | null | undefined;
    /**
     * Loading mode display.
     * @group Props
     */
    loadingMode: 'mask' | 'icon';
    /**
     * A single treenode instance or an array to refer to the selections.
     * @group Props
     */
    selection: any;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Context menu instance.
     * @group Props
     */
    contextMenu: any;
    /**
     * Scope of the draggable nodes to match a droppableScope.
     * @group Props
     */
    draggableScope: any;
    /**
     * Scope of the droppable nodes to match a draggableScope.
     * @group Props
     */
    droppableScope: any;
    /**
     * Whether the nodes are draggable.
     * @group Props
     */
    draggableNodes: boolean | undefined;
    /**
     * Whether the nodes are droppable.
     * @group Props
     */
    droppableNodes: boolean | undefined;
    /**
     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean;
    /**
     * Whether checkbox selections propagate to ancestor nodes.
     * @group Props
     */
    propagateSelectionUp: boolean;
    /**
     * Whether checkbox selections propagate to descendant nodes.
     * @group Props
     */
    propagateSelectionDown: boolean;
    /**
     * Displays a loader to indicate data load is in progress.
     * @group Props
     */
    loading: boolean | undefined;
    /**
     * The icon to show while indicating data load is in progress.
     * @group Props
     */
    loadingIcon: string | undefined;
    /**
     * Text to display when there is no data.
     * @group Props
     */
    emptyMessage: string;
    /**
     * Used to define a string that labels the tree.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Defines a string that labels the toggler icon for accessibility.
     * @group Props
     */
    togglerAriaLabel: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * When enabled, drop can be accepted or rejected based on condition defined at onNodeDrop.
     * @group Props
     */
    validateDrop: boolean | undefined;
    /**
     * When specified, displays an input field to filter the items.
     * @group Props
     */
    filter: boolean | undefined;
    /**
     * Determines whether the filter input should be automatically focused when the component is rendered.
     * @group Props
     */
    filterInputAutoFocus: boolean;
    /**
     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.
     * @group Props
     */
    filterBy: string;
    /**
     * Mode for filtering valid values are "lenient" and "strict". Default is lenient.
     * @group Props
     */
    filterMode: string;
    /**
     * Mode for filtering valid values are "lenient" and "strict". Default is lenient.
     * @group Props
     */
    filterOptions: any;
    /**
     * Placeholder text to show when filter input is empty.
     * @group Props
     */
    filterPlaceholder: string | undefined;
    /**
     * Values after the tree nodes are filtered.
     * @group Props
     */
    filteredNodes: TreeNode<any>[] | undefined | null;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Height of the scrollable viewport.
     * @group Props
     */
    scrollHeight: string | undefined;
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    lazy: boolean;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Height of an item in the list for VirtualScrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * Indentation factor for spacing of the nested node when virtual scrolling is enabled.
     * @group Props
     */
    indentation: number;
    /**
     * Custom templates of the component.
     * @group Props
     */
    _templateMap: any;
    /**
     * Function to optimize the node list rendering, default algorithm checks for object identity.
     * @group Props
     */
    trackBy: Function;
    /**
     * Highlights the node on select.
     * @group Props
     */
    highlightOnSelect: boolean;
    /**
     * Callback to invoke on selection change.
     * @param {(TreeNode<any> | TreeNode<any>[] | null)} event - Custom selection change event.
     * @group Emits
     */
    selectionChange: EventEmitter<TreeNode<any> | TreeNode<any>[] | null>;
    /**
     * Callback to invoke when a node is selected.
     * @param {TreeNodeSelectEvent} event - Node select event.
     * @group Emits
     */
    onNodeSelect: EventEmitter<TreeNodeSelectEvent>;
    /**
     * Callback to invoke when a node is unselected.
     * @param {TreeNodeUnSelectEvent} event - Node unselect event.
     * @group Emits
     */
    onNodeUnselect: EventEmitter<TreeNodeUnSelectEvent>;
    /**
     * Callback to invoke when a node is expanded.
     * @param {TreeNodeExpandEvent} event - Node expand event.
     * @group Emits
     */
    onNodeExpand: EventEmitter<TreeNodeExpandEvent>;
    /**
     * Callback to invoke when a node is collapsed.
     * @param {TreeNodeCollapseEvent} event - Node collapse event.
     * @group Emits
     */
    onNodeCollapse: EventEmitter<TreeNodeCollapseEvent>;
    /**
     * Callback to invoke when a node is selected with right click.
     * @param {onNodeContextMenuSelect} event - Node context menu select event.
     * @group Emits
     */
    onNodeContextMenuSelect: EventEmitter<TreeNodeContextMenuSelectEvent>;
    /**
     * Callback to invoke when a node is double clicked.
     * @param {TreeNodeDoubleClickEvent} event - Node double click event.
     * @group Emits
     */
    onNodeDoubleClick: EventEmitter<TreeNodeDoubleClickEvent>;
    /**
     * Callback to invoke when a node is dropped.
     * @param {TreeNodeDropEvent} event - Node drop event.
     * @group Emits
     */
    onNodeDrop: EventEmitter<TreeNodeDropEvent>;
    /**
     * Callback to invoke in lazy mode to load new data.
     * @param {TreeLazyLoadEvent} event - Custom lazy load event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<TreeLazyLoadEvent>;
    /**
     * Callback to invoke in virtual scroll mode when scroll position changes.
     * @param {TreeScrollEvent} event - Custom scroll event.
     * @group Emits
     */
    onScroll: EventEmitter<TreeScrollEvent>;
    /**
     * Callback to invoke in virtual scroll mode when scroll position and item's range in view changes.
     * @param {TreeScrollIndexChangeEvent} event - Scroll index change event.
     * @group Emits
     */
    onScrollIndexChange: EventEmitter<TreeScrollIndexChangeEvent>;
    /**
     * Callback to invoke when data is filtered.
     * @param {TreeFilterEvent} event - Custom filter event.
     * @group Emits
     */
    onFilter: EventEmitter<TreeFilterEvent>;
    /**
     * Filter template.
     * @group Templates
     */
    filterTemplate: TemplateRef<any>;
    /**
     * Node template.
     * @group Templates
     */
    nodeTemplate: TemplateRef<any> | undefined;
    /**
     * Header template.
     * @group Templates
     */
    headerTemplate: TemplateRef<any> | undefined;
    /**
     * Footer template.
     * @group Templates
     */
    footerTemplate: TemplateRef<any> | undefined;
    /**
     * Loader template.
     * @group Templates
     */
    loaderTemplate: TemplateRef<any> | undefined;
    /**
     * Empty message template.
     * @group Templates
     */
    emptyMessageTemplate: TemplateRef<any> | undefined;
    /**
     * Toggler icon template.
     * @group Templates
     */
    togglerIconTemplate: TemplateRef<any> | undefined;
    /**
     * Checkbox icon template.
     * @group Templates
     */
    checkboxIconTemplate: TemplateRef<any> | undefined;
    /**
     * Loading icon template.
     * @group Templates
     */
    loadingIconTemplate: TemplateRef<any> | undefined;
    /**
     * Filter icon template.
     * @group Templates
     */
    filterIconTemplate: TemplateRef<any> | undefined;
    filterViewChild: Nullable<ElementRef>;
    scroller: Nullable<Scroller>;
    wrapperViewChild: Nullable<ElementRef>;
    contentViewChild: Nullable<ElementRef>;
    private templates;
    _headerTemplate: TemplateRef<any> | undefined;
    _emptyMessageTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _loaderTemplate: TemplateRef<any> | undefined;
    _togglerIconTemplate: TemplateRef<any> | undefined;
    _checkboxIconTemplate: TemplateRef<any> | undefined;
    _loadingIconTemplate: TemplateRef<any> | undefined;
    _filterIconTemplate: TemplateRef<any> | undefined;
    _filterTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    serializedValue: Nullable<TreeNode<any>[]>;
    nodeTouched: boolean | undefined | null;
    dragNodeTree: Tree | undefined | null;
    dragNode: TreeNode<any> | undefined | null;
    dragNodeSubNodes: TreeNode<any>[] | undefined | null;
    dragNodeIndex: number | undefined | null;
    dragNodeScope: any;
    dragHover: boolean | undefined | null;
    dragStartSubscription: Subscription | undefined | null;
    dragStopSubscription: Subscription | undefined | null;
    _componentStyle: TreeStyle;
    handleDropEvent(event: DragEvent): void;
    handleDragOverEvent(event: DragEvent): void;
    handleDragEnterEvent(): void;
    handleDragLeaveEvent(event: DragEvent): void;
    constructor(dragDropService: TreeDragDropService);
    ngOnInit(): void;
    ngOnChanges(simpleChange: SimpleChanges): void;
    get emptyMessageLabel(): string;
    updateSerializedValue(): void;
    serializeNodes(parent: TreeNode<any> | null, nodes: TreeNode<any>[] | any, level: number, visible: boolean): void;
    onNodeClick(event: Event, node: TreeNode): void;
    onNodeTouchEnd(): void;
    onNodeRightClick(event: MouseEvent, node: TreeNode<any>): void;
    onNodeDblClick(event: MouseEvent, node: TreeNode<any>): void;
    findIndexInSelection(node: TreeNode): number;
    syncNodeOption(node: TreeNode, parentNodes: TreeNode<any>[], option: any, value?: any): void;
    hasFilteredNodes(): number;
    hasFilterActive(): boolean;
    getNodeWithKey(key: string, nodes: TreeNode<any>[]): TreeNode<any> | undefined;
    propagateUp(node: TreeNode, select: boolean): void;
    propagateDown(node: TreeNode, select: boolean): void;
    isSelected(node: TreeNode): boolean;
    isSingleSelectionMode(): boolean;
    isMultipleSelectionMode(): boolean;
    isCheckboxSelectionMode(): boolean;
    isNodeLeaf(node: TreeNode): boolean;
    getRootNode(): any;
    getTemplateForNode(node: TreeNode): TemplateRef<any> | null;
    onDragOver(event: DragEvent): void;
    onDrop(event: DragEvent): void;
    processTreeDrop(dragNode: TreeNode, dragNodeIndex: number): void;
    onDragEnter(): void;
    onDragLeave(event: DragEvent): void;
    allowDrop(dragNode: TreeNode, dropNode: TreeNode<any> | null, dragNodeScope: any): boolean;
    isValidDragScope(dragScope: any): boolean;
    _filter(value: string): void;
    /**
     * Resets filter.
     * @group Method
     */
    resetFilter(): void;
    /**
     * Scrolls to virtual index.
     * @param {number} number - Index to be scrolled.
     * @group Method
     */
    scrollToVirtualIndex(index: number): void;
    /**
     * Scrolls to virtual index.
     * @param {ScrollToOptions} options - Scroll options.
     * @group Method
     */
    scrollTo(options: any): void;
    findFilteredNodes(node: TreeNode, paramsWithoutNode: any): boolean;
    isFilterMatched(node: TreeNode, params: any): boolean;
    getIndex(options: any, index: number): any;
    getBlockableElement(): HTMLElement;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Tree, [{ optional: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Tree, "p-tree", never, { "value": { "alias": "value"; "required": false; }; "selectionMode": { "alias": "selectionMode"; "required": false; }; "loadingMode": { "alias": "loadingMode"; "required": false; }; "selection": { "alias": "selection"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "contextMenu": { "alias": "contextMenu"; "required": false; }; "draggableScope": { "alias": "draggableScope"; "required": false; }; "droppableScope": { "alias": "droppableScope"; "required": false; }; "draggableNodes": { "alias": "draggableNodes"; "required": false; }; "droppableNodes": { "alias": "droppableNodes"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "propagateSelectionUp": { "alias": "propagateSelectionUp"; "required": false; }; "propagateSelectionDown": { "alias": "propagateSelectionDown"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "loadingIcon": { "alias": "loadingIcon"; "required": false; }; "emptyMessage": { "alias": "emptyMessage"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "togglerAriaLabel": { "alias": "togglerAriaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "validateDrop": { "alias": "validateDrop"; "required": false; }; "filter": { "alias": "filter"; "required": false; }; "filterInputAutoFocus": { "alias": "filterInputAutoFocus"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "filterMode": { "alias": "filterMode"; "required": false; }; "filterOptions": { "alias": "filterOptions"; "required": false; }; "filterPlaceholder": { "alias": "filterPlaceholder"; "required": false; }; "filteredNodes": { "alias": "filteredNodes"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "indentation": { "alias": "indentation"; "required": false; }; "_templateMap": { "alias": "_templateMap"; "required": false; }; "trackBy": { "alias": "trackBy"; "required": false; }; "highlightOnSelect": { "alias": "highlightOnSelect"; "required": false; }; }, { "selectionChange": "selectionChange"; "onNodeSelect": "onNodeSelect"; "onNodeUnselect": "onNodeUnselect"; "onNodeExpand": "onNodeExpand"; "onNodeCollapse": "onNodeCollapse"; "onNodeContextMenuSelect": "onNodeContextMenuSelect"; "onNodeDoubleClick": "onNodeDoubleClick"; "onNodeDrop": "onNodeDrop"; "onLazyLoad": "onLazyLoad"; "onScroll": "onScroll"; "onScrollIndexChange": "onScrollIndexChange"; "onFilter": "onFilter"; }, ["filterTemplate", "nodeTemplate", "headerTemplate", "footerTemplate", "loaderTemplate", "emptyMessageTemplate", "togglerIconTemplate", "checkboxIconTemplate", "loadingIconTemplate", "filterIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_draggableNodes: unknown;
    static ngAcceptInputType_droppableNodes: unknown;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_propagateSelectionUp: unknown;
    static ngAcceptInputType_propagateSelectionDown: unknown;
    static ngAcceptInputType_loading: unknown;
    static ngAcceptInputType_validateDrop: unknown;
    static ngAcceptInputType_filter: unknown;
    static ngAcceptInputType_filterInputAutoFocus: unknown;
    static ngAcceptInputType_lazy: unknown;
    static ngAcceptInputType_virtualScroll: unknown;
    static ngAcceptInputType_virtualScrollItemSize: unknown;
    static ngAcceptInputType_indentation: unknown;
    static ngAcceptInputType_highlightOnSelect: unknown;
}
declare class TreeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TreeModule, never, [typeof Tree, typeof i1.SharedModule], [typeof Tree, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TreeModule>;
}

export { Tree, TreeClasses, TreeModule, TreeStyle, UITreeNode };
export type { TreeFilterEvent, TreeLazyLoadEvent, TreeNodeCollapseEvent, TreeNodeContextMenuSelectEvent, TreeNodeDoubleClickEvent, TreeNodeDropEvent, TreeNodeExpandEvent, TreeNodeSelectEvent, TreeNodeUnSelectEvent, TreeScrollEvent, TreeScrollIndexChangeEvent, TreeTemplates };
