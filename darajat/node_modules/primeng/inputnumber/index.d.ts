import * as i0 from '@angular/core';
import { TemplateRef, OnInit, AfterContentInit, OnChanges, Injector, EventEmitter, QueryList, ElementRef, SimpleChanges } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseInput } from 'primeng/baseinput';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 * Custom input event.
 * @see {@link InputNumber.onInput}
 * @group Events
 */
interface InputNumberInputEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Input value.
     */
    value: number | string | null;
    /**
     * Selected option value.
     */
    formattedValue: string;
}
/**
 * Defines valid templates in InputNumber.
 * @group Templates
 */
interface InputNumberTemplates {
    /**
     * Custom clear icon template.
     */
    clearicon(): TemplateRef<any>;
    /**
     * Custom increment button icon template.
     */
    incrementbuttonicon(): TemplateRef<any>;
    /**
     * Custom decrement button icon template.
     */
    decrementbuttonicon(): TemplateRef<any>;
}

/**
 *
 * InputNumber is an input component to provide numerical input.
 *
 * [Live Demo](https://www.primeng.org/inputnumber/)
 *
 * @module inputnumberstyle
 *
 */
declare enum InputNumberClasses {
    /**
     * Class name of the root element
     */
    root = "p-inputnumber",
    /**
     * Class name of the input element
     */
    pcInputText = "p-inputnumber-input",
    /**
     * Class name of the button group element
     */
    buttonGroup = "p-inputnumber-button-group",
    /**
     * Class name of the increment button element
     */
    incrementButton = "p-inputnumber-increment-button",
    /**
     * Class name of the decrement button element
     */
    decrementButton = "p-inputnumber-decrement-button",
    /**
     * Class name of the clear icon
     */
    clearIcon = "p-autocomplete-clear-icon"
}
declare class InputNumberStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-inputwrapper-filled': any;
            'p-inputwrapper-focus': any;
            'p-inputnumber-stacked': boolean;
            'p-inputnumber-horizontal': boolean;
            'p-inputnumber-vertical': boolean;
            'p-inputnumber-fluid': any;
            'p-invalid': any;
        })[];
        pcInputText: string;
        buttonGroup: string;
        incrementButton: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        decrementButton: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        clearIcon: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<InputNumberStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<InputNumberStyle>;
}
interface InputNumberStyle extends BaseStyle {
}

declare const INPUTNUMBER_VALUE_ACCESSOR: any;
/**
 * InputNumber is an input component to provide numerical input.
 * @group Components
 */
declare class InputNumber extends BaseInput implements OnInit, AfterContentInit, OnChanges {
    readonly injector: Injector;
    /**
     * Displays spinner buttons.
     * @group Props
     */
    showButtons: boolean;
    /**
     * Whether to format the value.
     * @group Props
     */
    format: boolean;
    /**
     * Layout of the buttons, valid values are "stacked" (default), "horizontal" and "vertical".
     * @group Props
     */
    buttonLayout: string;
    /**
     * Identifier of the focus input to match a label defined for the component.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Advisory information to display on input.
     * @group Props
     */
    placeholder: string | undefined;
    /**
     * Specifies tab order of the element.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * Title text of the input text.
     * @group Props
     */
    title: string | undefined;
    /**
     * Specifies one or more IDs in the DOM that labels the input field.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Specifies one or more IDs in the DOM that describes the input field.
     * @group Props
     */
    ariaDescribedBy: string | undefined;
    /**
     * Used to define a string that labels the input element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Used to indicate that user input is required on an element before a form can be submitted.
     * @group Props
     */
    ariaRequired: boolean | undefined;
    /**
     * Used to define a string that autocomplete attribute the current element.
     * @group Props
     */
    autocomplete: string | undefined;
    /**
     * Style class of the increment button.
     * @group Props
     */
    incrementButtonClass: string | undefined;
    /**
     * Style class of the decrement button.
     * @group Props
     */
    decrementButtonClass: string | undefined;
    /**
     * Style class of the increment button.
     * @group Props
     */
    incrementButtonIcon: string | undefined;
    /**
     * Style class of the decrement button.
     * @group Props
     */
    decrementButtonIcon: string | undefined;
    /**
     * When present, it specifies that an input field is read-only.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * Determines whether the input field is empty.
     * @group Props
     */
    allowEmpty: boolean;
    /**
     * Locale to be used in formatting.
     * @group Props
     */
    locale: string | undefined;
    /**
     * The locale matching algorithm to use. Possible values are "lookup" and "best fit"; the default is "best fit". See Locale Negotiation for details.
     * @group Props
     */
    localeMatcher: any;
    /**
     * Defines the behavior of the component, valid values are "decimal" and "currency".
     * @group Props
     */
    mode: string | any;
    /**
     * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as "USD" for the US dollar, "EUR" for the euro, or "CNY" for the Chinese RMB. There is no default value; if the style is "currency", the currency property must be provided.
     * @group Props
     */
    currency: string | undefined;
    /**
     * How to display the currency in currency formatting. Possible values are "symbol" to use a localized currency symbol such as €, ü"code" to use the ISO currency code, "name" to use a localized currency name such as "dollar"; the default is "symbol".
     * @group Props
     */
    currencyDisplay: string | undefined | any;
    /**
     * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.
     * @group Props
     */
    useGrouping: boolean;
    /**
     * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).
     * @group Props
     */
    minFractionDigits: number | undefined;
    /**
     * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).
     * @group Props
     */
    maxFractionDigits: number | undefined;
    /**
     * Text to display before the value.
     * @group Props
     */
    prefix: string | undefined;
    /**
     * Text to display after the value.
     * @group Props
     */
    suffix: string | undefined;
    /**
     * Inline style of the input field.
     * @group Props
     */
    inputStyle: any;
    /**
     * Style class of the input field.
     * @group Props
     */
    inputStyleClass: string | undefined;
    /**
     * When enabled, a clear icon is displayed to clear the value.
     * @group Props
     */
    showClear: boolean;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Callback to invoke on input.
     * @param {InputNumberInputEvent} event - Custom input event.
     * @group Emits
     */
    onInput: EventEmitter<InputNumberInputEvent>;
    /**
     * Callback to invoke when the component receives focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the component loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Callback to invoke on input key press.
     * @param {KeyboardEvent} event - Keyboard event.
     * @group Emits
     */
    onKeyDown: EventEmitter<KeyboardEvent>;
    /**
     * Callback to invoke when clear token is clicked.
     * @group Emits
     */
    onClear: EventEmitter<void>;
    /**
     * Template of the clear icon.
     * @group Templates
     */
    clearIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Template of the increment button icon.
     * @group Templates
     */
    incrementButtonIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Template of the decrement button icon.
     * @group Templates
     */
    decrementButtonIconTemplate: Nullable<TemplateRef<any>>;
    templates: QueryList<PrimeTemplate>;
    input: ElementRef<HTMLInputElement>;
    _clearIconTemplate: TemplateRef<any> | undefined;
    _incrementButtonIconTemplate: TemplateRef<any> | undefined;
    _decrementButtonIconTemplate: TemplateRef<any> | undefined;
    value: Nullable<number>;
    focused: Nullable<boolean>;
    initialized: Nullable<boolean>;
    groupChar: string;
    prefixChar: string;
    suffixChar: string;
    isSpecialChar: Nullable<boolean>;
    timer: any;
    lastValue: Nullable<string>;
    _numeral: any;
    numberFormat: any;
    _decimal: any;
    _decimalChar: string;
    _group: any;
    _minusSign: any;
    _currency: Nullable<RegExp | string>;
    _prefix: Nullable<RegExp>;
    _suffix: Nullable<RegExp>;
    _index: number | any;
    _componentStyle: InputNumberStyle;
    private ngControl;
    constructor(injector: Injector);
    ngOnChanges(simpleChange: SimpleChanges): void;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    getOptions(): {
        localeMatcher: any;
        style: any;
        currency: string;
        currencyDisplay: any;
        useGrouping: boolean;
        minimumFractionDigits: number;
        maximumFractionDigits: number;
    };
    constructParser(): void;
    updateConstructParser(): void;
    escapeRegExp(text: string): string;
    getDecimalExpression(): RegExp;
    getDecimalChar(): string;
    getGroupingExpression(): RegExp;
    getMinusSignExpression(): RegExp;
    getCurrencyExpression(): RegExp;
    getPrefixExpression(): RegExp;
    getSuffixExpression(): RegExp;
    formatValue(value: any): any;
    parseValue(text: any): any;
    repeat(event: Event, interval: number | null, dir: number): void;
    spin(event: Event, dir: number): void;
    clear(): void;
    onUpButtonMouseDown(event: MouseEvent): void;
    onUpButtonMouseUp(): void;
    onUpButtonMouseLeave(): void;
    onUpButtonKeyDown(event: KeyboardEvent): void;
    onUpButtonKeyUp(): void;
    onDownButtonMouseDown(event: MouseEvent): void;
    onDownButtonMouseUp(): void;
    onDownButtonMouseLeave(): void;
    onDownButtonKeyUp(): void;
    onDownButtonKeyDown(event: KeyboardEvent): void;
    onUserInput(event: Event): void;
    onInputKeyDown(event: KeyboardEvent): void;
    onInputKeyPress(event: KeyboardEvent): void;
    onPaste(event: ClipboardEvent): void;
    allowMinusSign(): boolean;
    isMinusSign(char: string): boolean;
    isDecimalSign(char: string): boolean;
    isDecimalMode(): boolean;
    getDecimalCharIndexes(val: string): {
        decimalCharIndex: number;
        decimalCharIndexWithoutPrefix: number;
    };
    getCharIndexes(val: string): {
        decimalCharIndex: number;
        minusCharIndex: number;
        suffixCharIndex: number;
        currencyCharIndex: number;
    };
    insert(event: Event, text: string, sign?: {
        isDecimalSign: boolean;
        isMinusSign: boolean;
    }): void;
    insertText(value: string, text: string, start: number, end: number): any;
    deleteRange(value: string, start: number, end: number): any;
    initCursor(): any;
    onInputClick(): void;
    isNumeralChar(char: string): boolean;
    resetRegex(): void;
    updateValue(event: Event, valueStr: Nullable<string>, insertedValueStr: Nullable<string>, operation: Nullable<string>): void;
    handleOnInput(event: Event, currentValue: string, newValue: any): void;
    isValueChanged(currentValue: string, newValue: string): boolean;
    validateValue(value: number | string): string | number;
    updateInput(value: any, insertedValueStr: Nullable<string>, operation: Nullable<string>, valueStr: Nullable<string>): void;
    concatValues(val1: string, val2: string): string;
    getDecimalLength(value: string): number;
    onInputFocus(event: Event): void;
    onInputBlur(event: Event): void;
    formattedValue(): any;
    updateModel(event: Event, value: any): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    clearTimer(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<InputNumber, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<InputNumber, "p-inputNumber, p-inputnumber, p-input-number", never, { "showButtons": { "alias": "showButtons"; "required": false; }; "format": { "alias": "format"; "required": false; }; "buttonLayout": { "alias": "buttonLayout"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "title": { "alias": "title"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "ariaDescribedBy": { "alias": "ariaDescribedBy"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaRequired": { "alias": "ariaRequired"; "required": false; }; "autocomplete": { "alias": "autocomplete"; "required": false; }; "incrementButtonClass": { "alias": "incrementButtonClass"; "required": false; }; "decrementButtonClass": { "alias": "decrementButtonClass"; "required": false; }; "incrementButtonIcon": { "alias": "incrementButtonIcon"; "required": false; }; "decrementButtonIcon": { "alias": "decrementButtonIcon"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "allowEmpty": { "alias": "allowEmpty"; "required": false; }; "locale": { "alias": "locale"; "required": false; }; "localeMatcher": { "alias": "localeMatcher"; "required": false; }; "mode": { "alias": "mode"; "required": false; }; "currency": { "alias": "currency"; "required": false; }; "currencyDisplay": { "alias": "currencyDisplay"; "required": false; }; "useGrouping": { "alias": "useGrouping"; "required": false; }; "minFractionDigits": { "alias": "minFractionDigits"; "required": false; }; "maxFractionDigits": { "alias": "maxFractionDigits"; "required": false; }; "prefix": { "alias": "prefix"; "required": false; }; "suffix": { "alias": "suffix"; "required": false; }; "inputStyle": { "alias": "inputStyle"; "required": false; }; "inputStyleClass": { "alias": "inputStyleClass"; "required": false; }; "showClear": { "alias": "showClear"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; }, { "onInput": "onInput"; "onFocus": "onFocus"; "onBlur": "onBlur"; "onKeyDown": "onKeyDown"; "onClear": "onClear"; }, ["clearIconTemplate", "incrementButtonIconTemplate", "decrementButtonIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_showButtons: unknown;
    static ngAcceptInputType_format: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_ariaRequired: unknown;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_allowEmpty: unknown;
    static ngAcceptInputType_useGrouping: unknown;
    static ngAcceptInputType_minFractionDigits: unknown;
    static ngAcceptInputType_maxFractionDigits: unknown;
    static ngAcceptInputType_showClear: unknown;
    static ngAcceptInputType_autofocus: unknown;
}
declare class InputNumberModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<InputNumberModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<InputNumberModule, never, [typeof InputNumber, typeof i1.SharedModule], [typeof InputNumber, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<InputNumberModule>;
}

export { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberClasses, InputNumberModule, InputNumberStyle };
export type { InputNumberInputEvent, InputNumberTemplates };
