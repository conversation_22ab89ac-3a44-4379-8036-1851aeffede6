import * as rxjs from 'rxjs';
import { Subscription } from 'rxjs';
import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, OnInit, OnDestroy, OnChanges, ElementRef, EventEmitter, QueryList, NgZone, SimpleChanges, ChangeDetectorRef, AfterViewInit, Renderer2 } from '@angular/core';
import * as i8 from 'primeng/api';
import { FilterMetadata, TreeNode, TreeTableNode, LazyLoadMeta, SortMeta, BlockableUI, ScrollerOptions, PrimeTemplate, FilterService } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as i4 from 'primeng/scroller';
import { Scroller } from 'primeng/scroller';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';
import * as i1 from '@angular/common';
import * as i2 from 'primeng/paginator';
import * as i3 from 'primeng/ripple';
import * as i5 from 'primeng/icons';
import * as i6 from 'primeng/badge';
import * as i7 from 'primeng/checkbox';
import * as i9 from '@angular/forms';

/**
 *
 * TreeTable is used to display hierarchical data in tabular format.
 *
 * [Live Demo](https://www.primeng.org/treetable/)
 *
 * @module treetablestyle
 *
 */
declare enum TreeTableClasses {
    /**
     * Class name of the root element
     */
    root = "p-treetable",
    /**
     * Class name of the loading element
     */
    loading = "p-treetable-loading",
    /**
     * Class name of the mask element
     */
    mask = "p-treetable-mask",
    /**
     * Class name of the loading icon element
     */
    loadingIcon = "p-treetable-loading-icon",
    /**
     * Class name of the header element
     */
    header = "p-treetable-header",
    /**
     * Class name of the paginator element
     */
    pcPaginator = "p-treetable-paginator-[position]",
    /**
     * Class name of the table container element
     */
    tableContainer = "p-treetable-table-container",
    /**
     * Class name of the table element
     */
    table = "p-treetable-table",
    /**
     * Class name of the thead element
     */
    thead = "p-treetable-thead",
    /**
     * Class name of the column resizer element
     */
    columnResizer = "p-treetable-column-resizer",
    /**
     * Class name of the column title element
     */
    columnTitle = "p-treetable-column-title",
    /**
     * Class name of the sort icon element
     */
    sortIcon = "p-treetable-sort-icon",
    /**
     * Class name of the sort badge element
     */
    pcSortBadge = "p-treetable-sort-badge",
    /**
     * Class name of the tbody element
     */
    tbody = "p-treetable-tbody",
    /**
     * Class name of the node toggle button element
     */
    nodeToggleButton = "p-treetable-node-toggle-button",
    /**
     * Class name of the node toggle icon element
     */
    nodeToggleIcon = "p-treetable-node-toggle-icon",
    /**
     * Class name of the node checkbox element
     */
    pcNodeCheckbox = "p-treetable-node-checkbox",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-treetable-empty-message",
    /**
     * Class name of the tfoot element
     */
    tfoot = "p-treetable-tfoot",
    /**
     * Class name of the footer element
     */
    footer = "p-treetable-footer",
    /**
     * Class name of the column resize indicator element
     */
    columnResizeIndicator = "p-treetable-column-resize-indicator",
    /**
     * Class name of the wrapper element
     */
    wrapper = "p-treetable-wrapper",
    /**
     * Class name of the scrollable wrapper element
     */
    scrollableWrapper = "p-treetable-scrollable-wrapper",
    /**
     * Class name of the scrollable view element
     */
    scrollableView = "p-treetable-scrollable-view",
    /**
     * Class name of the frozen view element
     */
    frozenView = "p-treetable-frozen-view",
    /**
     * Class name of the column resizer helper element
     */
    columnResizerHelper = "p-treetable-column-resizer-helper",
    /**
     * Class name of the reorder indicator up element
     */
    reorderIndicatorUp = "p-treetable-reorder-indicator-up",
    /**
     * Class name of the reorder indicator down element
     */
    reorderIndicatorDown = "p-treetable-reorder-indicator-down",
    /**
     * Class name of the scrollable header element
     */
    scrollableHeader = "p-treetable-scrollable-header",
    /**
     * Class name of the scrollable header box element
     */
    scrollableHeaderBox = "p-treetable-scrollable-header-box",
    /**
     * Class name of the scrollable header table element
     */
    scrollableHeaderTable = "p-treetable-scrollable-header-table",
    /**
     * Class name of the scrollable body element
     */
    scrollableBody = "p-treetable-scrollable-body",
    /**
     * Class name of the scrollable footer element
     */
    scrollableFooter = "p-treetable-scrollable-footer",
    /**
     * Class name of the scrollable footer box element
     */
    scrollableFooterBox = "p-treetable-scrollable-footer-box",
    /**
     * Class name of the scrollable footer table element
     */
    scrollableFooterTable = "p-treetable-scrollable-footer-table",
    /**
     * Class name of the sortable column icon element
     */
    sortableColumnIcon = "p-sortable-column-icon"
}
declare class TreeTableStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-treetable-gridlines': any;
            'p-treetable-hoverable-rows': any;
            'p-treetable-auto-layout': any;
            'p-treetable-resizable': any;
            'p-treetable-resizable-fit': boolean;
            'p-treetable-flex-scrollable': boolean;
        })[];
        loading: string;
        mask: string;
        loadingIcon: string;
        header: string;
        pcPaginator: ({ instance }: {
            instance: any;
        }) => any[];
        tableContainer: string;
        table: ({ instance }: {
            instance: any;
        }) => {
            'p-treetable-table': boolean;
            'p-treetable-scrollable-table': any;
            'p-treetable-resizable-table': any;
            'p-treetable-resizable-table-fit': boolean;
        };
        thead: string;
        sortableColumn: ({ instance }: {
            instance: any;
        }) => {
            'p-sortable-column': any;
            'p-treetable-column-sorted': any;
        };
        sortableColumnIcon: string;
        sortableColumnBadge: string;
        columnResizer: string;
        columnHeaderContent: string;
        columnTitle: string;
        sortIcon: string;
        pcSortBadge: string;
        tbody: string;
        row: ({ instance }: {
            instance: any;
        }) => {
            'p-treetable-row-selected': any;
        };
        contextMenuRow: ({ instance }: {
            instance: any;
        }) => {
            'p-treetable-contextmenu-row-selected': any;
        };
        toggler: string;
        nodeToggleButton: string;
        nodeToggleIcon: string;
        pcNodeCheckbox: string;
        tfoot: string;
        footerCell: ({ instance }: {
            instance: any;
        }) => {
            'p-treetable-frozen-column': any;
        };
        footer: string;
        columnResizeIndicator: string;
        wrapper: string;
        scrollableWrapper: string;
        scrollableView: string;
        frozenView: string;
        columnResizerHelper: string;
        reorderIndicatorUp: string;
        reorderIndicatorDown: string;
        scrollableHeader: string;
        scrollableHeaderBox: string;
        scrollableHeaderTable: string;
        scrollableBody: string;
        scrollableFooter: string;
        scrollableFooterBox: string;
        scrollableFooterTable: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeTableStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TreeTableStyle>;
}
interface TreeTableStyle extends BaseStyle {
}

/**
 * Paginator state.
 * @group Interface
 */
interface TreeTablePaginatorState {
    /**
     * Current page.
     */
    page?: number;
    /**
     * Index of the first element.
     */
    first?: number;
    /**
     * Row count.
     */
    rows?: number;
    /**
     * Page count.
     */
    pageCount?: number;
}
/**
 * Custom lazy load event.
 * @see {@link TreeTable.onLazyLoad}
 * @extends {LazyLoadMeta}
 * @group Events
 */
interface TreeTableLazyLoadEvent extends LazyLoadMeta {
    /**
     * First element in viewport.
     */
    first: any;
    /**
     * Last element in viewport.
     */
    last: any;
}
/**
 * Custom column reorder event.
 * @see {@link TreeTable.onColReorder}
 * @group Events
 */
interface TreeTableColumnReorderEvent {
    /**
     * Index of the dragged item.
     */
    dragIndex?: number;
    /**
     * Index of the drop position.
     */
    dropIndex?: number;
    /**
     * Columns after reorder.
     */
    columns?: any[];
}
/**
 * Custom filter event.
 * @see {@link TreeTable.onFilter}
 * @group Events
 */
interface TreeTableFilterEvent {
    /**
     * Filter meta.
     */
    filters?: {
        [s: string]: FilterMetadata | undefined;
    };
    /**
     * Value after filter.
     */
    filteredValue?: TreeNode[];
}
/**
 * Custom node expand event.
 * @see {@link TreeTable.onNodeExpand}
 * @group Events
 */
interface TreeTableNodeExpandEvent<T = any> extends TreeTableNodeCollapseEvent<T> {
}
/**
 * Custom node collapse event.
 * @see {@link TreeTable.onNodeCollapse}
 * @group Events
 */
interface TreeTableNodeCollapseEvent<T = any> {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Node instance.
     */
    node: TreeTableNode<T>;
}
/**
 * Custom sort event.
 * @see {@link TreeTable.onSort}
 * @see {@link TreeTable.sortFunction}
 * @group Events
 */
interface TreeTableSortEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
    /**
     * Value to be sorted.
     */
    data?: TreeNode[];
    /**
     * Sort mode.
     */
    mode?: 'single' | 'multiple';
    /**
     * Sort field.
     */
    field?: string;
    /**
     * Sort order.
     */
    order?: number;
    /**
     * Multiple sort meta.
     */
    multiSortMeta?: SortMeta[] | null | undefined;
    /**
     * Multiple sort meta.
     */
    multisortmeta?: any;
    /**
     * Sort meta.
     */
    sortMeta?: SortMeta;
}
/**
 * Custom column resize event.
 * @see {@link TreeTable.onColResize}
 * @group Events
 */
interface TreeTableColResizeEvent {
    /**
     * Instance of resized column.
     */
    element: HTMLElement;
    /**
     * Position.
     */
    delta: number;
}
/**
 * Custom node select event.
 * @see {@link TreeTable.onNodeSelect}
 * @extends {TreeTableNode}
 * @group Events
 */
interface TreeTableNodeSelectEvent extends TreeTableNode {
}
/**
 * Custom node unselect event.
 * @see {@link TreeTable.onNodeUnSelect}
 * @group Events
 */
interface TreeTableNodeUnSelectEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
    /**
     * Node instance.
     */
    node?: TreeTableNode<any>;
    /**
     * Selection type.
     */
    type?: string;
}
/**
 * Custom context menu select event.
 * @see {@link TreeTable.onContextMenuSelect}
 * @group Events
 */
interface TreeTableContextMenuSelectEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Node instance.
     */
    node: TreeTableNode<any>;
}
/**
 * Custom checkbox toggle event.
 * @see {@link TreeTable.onHeaderCheckboxToggle}
 * @group Events
 */
interface TreeTableHeaderCheckboxToggleEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Checked state.
     */
    checked: boolean;
}
/**
 * Custom edit event.
 * @see {@link TreeTable.onEditInit}
 * @see {@link TreeTable.onEditCancel}
 * @see {@link TreeTable.onEditComplete}
 * @group Events
 */
interface TreeTableEditEvent {
    /**
     * Field to be edited.
     */
    field: string;
    /**
     * New value.
     */
    data: string;
}
/**
 * Filtering options.
 * @group Interface
 */
interface TreeTableFilterOptions {
    /**
     * Field to be filtered.
     */
    filterField: string;
    /**
     * Value to use when filtering.
     */
    filterValue: any;
    /**
     * Filter constraints.
     */
    filterConstraint: (dataFieldValue: any, filterValue: any, filterLocale: string) => boolean;
    /**
     * Boolean value that defines if strict mode enabled.
     */
    isStrictMode: boolean;
}
/**
 * Defines valid templates in TreeTable.
 * @group Templates
 */
interface TreeTableTemplates {
    /**
     * Custom caption template.
     */
    caption(): TemplateRef<any>;
    /**
     * Custom header template.
     * @param {Object} context - header data.
     */
    header(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
    }): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom body template.
     * @param {Object} context - body data.
     */
    body(context: {
        $implicit: TreeNode;
        /**
         * Serialized node.
         */
        node: TreeNode;
        /**
         * Row data.
         */
        rowData: any;
        /**
         * Columns instance.
         */
        columns: any[];
    }): TemplateRef<{
        $implicit: TreeNode;
        node: TreeNode;
        rowData: any;
        columns: any[];
    }>;
    /**
     * Custom footer template.
     * @param {Object} context - footer data.
     */
    footer(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
    }): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom summary template.
     */
    summary(): TemplateRef<any>;
    /**
     * Custom colgroup template.
     * @param {Object} context - column group data.
     */
    colgroup(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
    }): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom emptymessage template.
     */
    emptymessage(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
        /**
         * Boolean value indicates whether column is frozen.
         */
        frozen: boolean;
    }): TemplateRef<{
        $implicit: any[];
        frozen: boolean;
    }>;
    /**
     * Custom paginator left section template.
     */
    paginatorleft(): TemplateRef<any>;
    /**
     * Custom paginator right section template.
     */
    paginatorright(): TemplateRef<any>;
    /**
     * Custom paginator dropdown template.
     */
    paginatordropdownitem(): TemplateRef<any>;
    /**
     * Custom frozenheader template.
     * @param {Object} context - frozen header data.
     */
    frozenheader(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
    }): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom frozenbody template.
     */
    frozenbody(): TemplateRef<any>;
    /**
     * Custom frozenfooter template.
     * @param {Object} context - frozen footer data.
     */
    frozenfooter(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
    }): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom frozen column group template.
     * @param {Object} context - group data.
     */
    frozencolgroup(context: {
        /**
         * Columns instance.
         */
        $implicit: any[];
    }): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom loading icon template.
     */
    loadingicon(): TemplateRef<any>;
    /**
     * Custom reorder indicator up icon template.
     */
    reorderindicatorupicon(): TemplateRef<any>;
    /**
     * Custom reorder indicator down icon template.
     */
    reorderindicatordownicon(): TemplateRef<any>;
    /**
     * Custom sort icon template.
     * @param {Object} context - sort data.
     */
    sorticon(context: {
        /**
         * Sort order.
         */
        $implicit: number;
    }): TemplateRef<{
        $implicit: number;
    }>;
    /**
     * Custom checkbox icon template.
     * @param {Object} context - checkbox data.
     */
    checkboxicon(context: {
        /**
         * Checkbox state.
         */
        $implicit: boolean;
        /**
         * Partial selection state of row node.
         */
        partialSelected: boolean;
    }): TemplateRef<{
        $implicit: boolean;
        partialSelected: boolean;
    }>;
    /**
     * Custom header checkbox icon template.
     * @param {Object} context - checkbox data.
     */
    headercheckboxicon(context: {
        /**
         * Checkbox state.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom toggler icon template.
     * @param {Object} context - toggle icon data.
     */
    togglericon(context: {
        /**
         * Expand state of row node.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom paginator first page link icon template.
     */
    paginatorfirstpagelinkicon(): TemplateRef<any>;
    /**
     * Custom paginatorlastpagelinkicon template.
     */
    paginatorlastpagelinkicon(): TemplateRef<any>;
    /**
     * Custom paginatorpreviouspagelinkicon template.
     */
    paginatorpreviouspagelinkicon(): TemplateRef<any>;
    /**
     * Custom paginatornextpagelinkicon template.
     */
    paginatornextpagelinkicon(): TemplateRef<any>;
}

declare class TreeTableService {
    private sortSource;
    private selectionSource;
    private contextMenuSource;
    private uiUpdateSource;
    private totalRecordsSource;
    sortSource$: rxjs.Observable<SortMeta | SortMeta[]>;
    selectionSource$: rxjs.Observable<unknown>;
    contextMenuSource$: rxjs.Observable<any>;
    uiUpdateSource$: rxjs.Observable<any>;
    totalRecordsSource$: rxjs.Observable<any>;
    onSort(sortMeta: SortMeta | SortMeta[] | null): void;
    onSelectionChange(): void;
    onContextMenu(node: any): void;
    onUIUpdate(value: any): void;
    onTotalRecordsChange(value: number): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeTableService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TreeTableService>;
}
/**
 * TreeTable is used to display hierarchical data in tabular format.
 * @group Components
 */
declare class TreeTable extends BaseComponent implements AfterContentInit, OnInit, OnDestroy, BlockableUI, OnChanges {
    _componentStyle: TreeTableStyle;
    /**
     * An array of objects to represent dynamic columns.
     * @group Props
     */
    columns: any[] | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the table.
     * @group Props
     */
    tableStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the table.
     * @group Props
     */
    tableStyleClass: string | undefined;
    /**
     * Whether the cell widths scale according to their content or not.
     * @group Props
     */
    autoLayout: boolean | undefined;
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    lazy: boolean;
    /**
     * Whether to call lazy loading on initialization.
     * @group Props
     */
    lazyLoadOnInit: boolean;
    /**
     * When specified as true, enables the pagination.
     * @group Props
     */
    paginator: boolean | undefined;
    /**
     * Number of rows to display per page.
     * @group Props
     */
    rows: number | undefined;
    /**
     * Index of the first row to be displayed.
     * @group Props
     */
    first: number;
    /**
     * Number of page links to display in paginator.
     * @group Props
     */
    pageLinks: number;
    /**
     * Array of integer/object values to display inside rows per page dropdown of paginator
     * @group Props
     */
    rowsPerPageOptions: any[] | undefined;
    /**
     * Whether to show it even there is only one page.
     * @group Props
     */
    alwaysShowPaginator: boolean;
    /**
     * Position of the paginator.
     * @group Props
     */
    paginatorPosition: 'top' | 'bottom' | 'both';
    /**
     * Custom style class for paginator
     * @group Props
     */
    paginatorStyleClass: string | undefined;
    /**
     * Target element to attach the paginator dropdown overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @group Props
     */
    paginatorDropdownAppendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;
    /**
     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}
     * @group Props
     */
    currentPageReportTemplate: string;
    /**
     * Whether to display current page report.
     * @group Props
     */
    showCurrentPageReport: boolean | undefined;
    /**
     * Whether to display a dropdown to navigate to any page.
     * @group Props
     */
    showJumpToPageDropdown: boolean | undefined;
    /**
     * When enabled, icons are displayed on paginator to go first and last page.
     * @group Props
     */
    showFirstLastIcon: boolean;
    /**
     * Whether to show page links.
     * @group Props
     */
    showPageLinks: boolean;
    /**
     * Sort order to use when an unsorted column gets sorted by user interaction.
     * @group Props
     */
    defaultSortOrder: number;
    /**
     * Defines whether sorting works on single column or on multiple columns.
     * @group Props
     */
    sortMode: 'single' | 'multiple';
    /**
     * When true, resets paginator to first page after sorting.
     * @group Props
     */
    resetPageOnSort: boolean;
    /**
     * Whether to use the default sorting or a custom one using sortFunction.
     * @group Props
     */
    customSort: boolean | undefined;
    /**
     * Specifies the selection mode, valid values are "single" and "multiple".
     * @group Props
     */
    selectionMode: string | undefined;
    /**
     * Selected row with a context menu.
     * @group Props
     */
    contextMenuSelection: any;
    /**
     * Mode of the contet menu selection.
     * @group Props
     */
    contextMenuSelectionMode: string;
    /**
     * A property to uniquely identify a record in data.
     * @group Props
     */
    dataKey: string | undefined;
    /**
     * Defines whether metaKey is should be considered for the selection. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean | undefined;
    /**
     * Algorithm to define if a row is selected, valid values are "equals" that compares by reference and "deepEquals" that compares all fields.
     * @group Props
     */
    compareSelectionBy: string;
    /**
     * Adds hover effect to rows without the need for selectionMode.
     * @group Props
     */
    rowHover: boolean | undefined;
    /**
     * Displays a loader to indicate data load is in progress.
     * @group Props
     */
    loading: boolean | undefined;
    /**
     * The icon to show while indicating data load is in progress.
     * @group Props
     */
    loadingIcon: string | undefined;
    /**
     * Whether to show the loading mask when loading property is true.
     * @group Props
     */
    showLoader: boolean;
    /**
     * When specified, enables horizontal and/or vertical scrolling.
     * @group Props
     */
    scrollable: boolean | undefined;
    /**
     * Height of the scroll viewport in fixed pixels or the "flex" keyword for a dynamic size.
     * @group Props
     */
    scrollHeight: string | undefined;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Height of a row to use in calculations of virtual scrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * The delay (in milliseconds) before triggering the virtual scroll. This determines the time gap between the user's scroll action and the actual rendering of the next set of items in the virtual scroll.
     * @group Props
     */
    virtualScrollDelay: number;
    /**
     * Width of the frozen columns container.
     * @group Props
     */
    frozenWidth: string | undefined;
    /**
     * An array of objects to represent dynamic columns that are frozen.
     * @group Props
     */
    frozenColumns: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * When enabled, columns can be resized using drag and drop.
     * @group Props
     */
    resizableColumns: boolean | undefined;
    /**
     * Defines whether the overall table width should change on column resize, valid values are "fit" and "expand".
     * @group Props
     */
    columnResizeMode: string;
    /**
     * When enabled, columns can be reordered using drag and drop.
     * @group Props
     */
    reorderableColumns: boolean | undefined;
    /**
     * Local ng-template varilable of a ContextMenu.
     * @group Props
     */
    contextMenu: any;
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.
     * @group Props
     */
    rowTrackBy: Function;
    /**
     * An array of FilterMetadata objects to provide external filters.
     * @group Props
     */
    filters: {
        [s: string]: FilterMetadata | undefined;
    };
    /**
     * An array of fields as string to use in global filtering.
     * @group Props
     */
    globalFilterFields: string[] | undefined;
    /**
     * Delay in milliseconds before filtering the data.
     * @group Props
     */
    filterDelay: number;
    /**
     * Mode for filtering valid values are "lenient" and "strict". Default is lenient.
     * @group Props
     */
    filterMode: string;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Locale to be used in paginator formatting.
     * @group Props
     */
    paginatorLocale: string | undefined;
    /**
     * Number of total records, defaults to length of value when not defined.
     * @group Props
     */
    get totalRecords(): number;
    set totalRecords(val: number);
    /**
     * Name of the field to sort data by default.
     * @group Props
     */
    get sortField(): string | undefined | null;
    set sortField(val: string | undefined | null);
    /**
     * Order to sort when default sorting is enabled.
     * @defaultValue 1
     * @group Props
     */
    get sortOrder(): number;
    set sortOrder(val: number);
    /**
     * An array of SortMeta objects to sort the data by default in multiple sort mode.
     * @defaultValue null
     * @group Props
     */
    get multiSortMeta(): SortMeta[] | undefined | null;
    set multiSortMeta(val: SortMeta[] | undefined | null);
    /**
     * Selected row in single mode or an array of values in multiple mode.
     * @defaultValue null
     * @group Props
     */
    get selection(): any;
    set selection(val: any);
    /**
     * An array of objects to display.
     * @defaultValue null
     * @group Props
     */
    get value(): TreeNode<any>[] | undefined;
    set value(val: TreeNode<any>[] | undefined);
    /**
     * Indicates the height of rows to be scrolled.
     * @defaultValue 28
     * @group Props
     * @deprecated use virtualScrollItemSize property instead.
     */
    get virtualRowHeight(): number;
    set virtualRowHeight(val: number);
    /**
     * A map of keys to control the selection state.
     * @group Props
     */
    get selectionKeys(): any;
    set selectionKeys(value: any);
    /**
     * Whether to show grid lines between cells.
     * @defaultValue false
     * @group Props
     */
    showGridlines: boolean;
    /**
     * Callback to invoke on selected node change.
     * @param {TreeTableNode} object - Node instance.
     * @group Emits
     */
    selectionChange: EventEmitter<TreeTableNode<any> | TreeTableNode<any>[] | null>;
    /**
     * Callback to invoke on context menu selection change.
     * @param {TreeTableNode} object - Node instance.
     * @group Emits
     */
    contextMenuSelectionChange: EventEmitter<TreeTableNode>;
    /**
     * Callback to invoke when data is filtered.
     * @param {TreeTableFilterEvent} event - Custom filter event.
     * @group Emits
     */
    onFilter: EventEmitter<TreeTableFilterEvent>;
    /**
     * Callback to invoke when a node is expanded.
     * @param {TreeTableNodeExpandEvent} event - Node expand event.
     * @group Emits
     */
    onNodeExpand: EventEmitter<TreeTableNodeExpandEvent>;
    /**
     * Callback to invoke when a node is collapsed.
     * @param {TreeTableNodeCollapseEvent} event - Node collapse event.
     * @group Emits
     */
    onNodeCollapse: EventEmitter<TreeTableNodeCollapseEvent>;
    /**
     * Callback to invoke when pagination occurs.
     * @param {TreeTablePaginatorState} object - Paginator state.
     * @group Emits
     */
    onPage: EventEmitter<TreeTablePaginatorState>;
    /**
     * Callback to invoke when a column gets sorted.
     * @param {Object} Object - Sort data.
     * @group Emits
     */
    onSort: EventEmitter<any>;
    /**
     * Callback to invoke when paging, sorting or filtering happens in lazy mode.
     * @param {TreeTableLazyLoadEvent} event - Custom lazy load event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<TreeTableLazyLoadEvent>;
    /**
     * An event emitter to invoke on custom sorting, refer to sorting section for details.
     * @param {TreeTableSortEvent} event - Custom sort event.
     * @group Emits
     */
    sortFunction: EventEmitter<TreeTableSortEvent>;
    /**
     * Callback to invoke when a column is resized.
     * @param {TreeTableColResizeEvent} event - Custom column resize event.
     * @group Emits
     */
    onColResize: EventEmitter<TreeTableColResizeEvent>;
    /**
     * Callback to invoke when a column is reordered.
     * @param {TreeTableColumnReorderEvent} event - Custom column reorder.
     * @group Emits
     */
    onColReorder: EventEmitter<TreeTableColumnReorderEvent>;
    /**
     * Callback to invoke when a node is selected.
     * @param {TreeTableNode} object - Node instance.
     * @group Emits
     */
    onNodeSelect: EventEmitter<TreeTableNode>;
    /**
     * Callback to invoke when a node is unselected.
     * @param {TreeTableNodeUnSelectEvent} event - Custom node unselect event.
     * @group Emits
     */
    onNodeUnselect: EventEmitter<TreeTableNodeUnSelectEvent>;
    /**
     * Callback to invoke when a node is selected with right click.
     * @param {TreeTableContextMenuSelectEvent} event - Custom context menu select event.
     * @group Emits
     */
    onContextMenuSelect: EventEmitter<TreeTableContextMenuSelectEvent>;
    /**
     * Callback to invoke when state of header checkbox changes.
     * @param {TreeTableHeaderCheckboxToggleEvent} event - Custom checkbox toggle event.
     * @group Emits
     */
    onHeaderCheckboxToggle: EventEmitter<TreeTableHeaderCheckboxToggleEvent>;
    /**
     * Callback to invoke when a cell switches to edit mode.
     * @param {TreeTableEditEvent} event - Custom edit event.
     * @group Emits
     */
    onEditInit: EventEmitter<TreeTableEditEvent>;
    /**
     * Callback to invoke when cell edit is completed.
     * @param {TreeTableEditEvent} event - Custom edit event.
     * @group Emits
     */
    onEditComplete: EventEmitter<TreeTableEditEvent>;
    /**
     * Callback to invoke when cell edit is cancelled with escape key.
     * @param {TreeTableEditEvent} event - Custom edit event.
     * @group Emits
     */
    onEditCancel: EventEmitter<TreeTableEditEvent>;
    /**
     * Callback to invoke when selectionKeys are changed.
     * @param {Object} object - updated value of the selectionKeys.
     * @group Emits
     */
    selectionKeysChange: EventEmitter<any>;
    resizeHelperViewChild: Nullable<ElementRef>;
    reorderIndicatorUpViewChild: Nullable<ElementRef>;
    reorderIndicatorDownViewChild: Nullable<ElementRef>;
    tableViewChild: Nullable<ElementRef>;
    scrollableViewChild: Nullable<ElementRef>;
    scrollableFrozenViewChild: Nullable<ElementRef>;
    _value: TreeNode<any>[] | undefined;
    _virtualRowHeight: number;
    _selectionKeys: any;
    serializedValue: any[] | undefined | null;
    _totalRecords: number;
    _multiSortMeta: SortMeta[] | undefined | null;
    _sortField: string | undefined | null;
    _sortOrder: number;
    filteredNodes: Nullable<any[]>;
    filterTimeout: any;
    _colGroupTemplate: Nullable<TemplateRef<any>>;
    colGroupTemplate: Nullable<TemplateRef<any>>;
    _captionTemplate: Nullable<TemplateRef<any>>;
    captionTemplate: Nullable<TemplateRef<any>>;
    _headerTemplate: Nullable<TemplateRef<any>>;
    headerTemplate: Nullable<TemplateRef<any>>;
    _bodyTemplate: Nullable<TemplateRef<any>>;
    bodyTemplate: Nullable<TemplateRef<any>>;
    _footerTemplate: Nullable<TemplateRef<any>>;
    footerTemplate: Nullable<TemplateRef<any>>;
    _summaryTemplate: Nullable<TemplateRef<any>>;
    summaryTemplate: Nullable<TemplateRef<any>>;
    _emptyMessageTemplate: Nullable<TemplateRef<any>>;
    emptyMessageTemplate: Nullable<TemplateRef<any>>;
    _paginatorLeftTemplate: Nullable<TemplateRef<any>>;
    paginatorLeftTemplate: Nullable<TemplateRef<any>>;
    _paginatorRightTemplate: Nullable<TemplateRef<any>>;
    paginatorRightTemplate: Nullable<TemplateRef<any>>;
    _paginatorDropdownItemTemplate: Nullable<TemplateRef<any>>;
    paginatorDropdownItemTemplate: Nullable<TemplateRef<any>>;
    _frozenHeaderTemplate: Nullable<TemplateRef<any>>;
    frozenHeaderTemplate: Nullable<TemplateRef<any>>;
    _frozenBodyTemplate: Nullable<TemplateRef<any>>;
    frozenBodyTemplate: Nullable<TemplateRef<any>>;
    _frozenFooterTemplate: Nullable<TemplateRef<any>>;
    frozenFooterTemplate: Nullable<TemplateRef<any>>;
    _frozenColGroupTemplate: Nullable<TemplateRef<any>>;
    frozenColGroupTemplate: Nullable<TemplateRef<any>>;
    _loadingIconTemplate: Nullable<TemplateRef<any>>;
    loadingIconTemplate: Nullable<TemplateRef<any>>;
    _reorderIndicatorUpIconTemplate: Nullable<TemplateRef<any>>;
    reorderIndicatorUpIconTemplate: Nullable<TemplateRef<any>>;
    _reorderIndicatorDownIconTemplate: Nullable<TemplateRef<any>>;
    reorderIndicatorDownIconTemplate: Nullable<TemplateRef<any>>;
    _sortIconTemplate: Nullable<TemplateRef<any>>;
    sortIconTemplate: Nullable<TemplateRef<any>>;
    _checkboxIconTemplate: Nullable<TemplateRef<any>>;
    checkboxIconTemplate: Nullable<TemplateRef<any>>;
    _headerCheckboxIconTemplate: Nullable<TemplateRef<any>>;
    headerCheckboxIconTemplate: Nullable<TemplateRef<any>>;
    _togglerIconTemplate: Nullable<TemplateRef<any>>;
    togglerIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorFirstPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    paginatorFirstPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorLastPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    paginatorLastPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorPreviousPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    paginatorPreviousPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorNextPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    paginatorNextPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _loaderTemplate: Nullable<TemplateRef<any>>;
    loaderTemplate: Nullable<TemplateRef<any>>;
    lastResizerHelperX: Nullable<number>;
    reorderIconWidth: Nullable<number>;
    reorderIconHeight: Nullable<number>;
    draggedColumn: Nullable<any[]>;
    dropPosition: Nullable<number>;
    preventSelectionSetterPropagation: Nullable<boolean>;
    _selection: any;
    selectedKeys: any;
    rowTouched: Nullable<boolean>;
    editingCell: Nullable<Element>;
    editingCellData: any | undefined | null;
    editingCellField: any | undefined | null;
    editingCellClick: Nullable<boolean>;
    documentEditListener: VoidListener;
    initialized: Nullable<boolean>;
    toggleRowIndex: Nullable<number>;
    ngOnInit(): void;
    templates: Nullable<QueryList<PrimeTemplate>>;
    ngAfterContentInit(): void;
    filterService: FilterService;
    tableService: TreeTableService;
    zone: NgZone;
    ngOnChanges(simpleChange: SimpleChanges): void;
    updateSerializedValue(): void;
    serializeNodes(parent: Nullable<TreeTableNode>, nodes: Nullable<TreeNode[]>, level: Nullable<number>, visible: Nullable<boolean>): void;
    serializePageNodes(): void;
    updateselectedKeys(): void;
    onPageChange(event: TreeTablePaginatorState): void;
    sort(event: TreeTableSortEvent): void;
    sortSingle(): void;
    sortNodes(nodes: TreeNode[]): void;
    sortMultiple(): void;
    sortMultipleNodes(nodes: TreeNode[]): void;
    multisortField(node1: TreeTableNode, node2: TreeTableNode, multiSortMeta: SortMeta[], index: number): number;
    getSortMeta(field: string): SortMeta;
    isSorted(field: string): boolean;
    createLazyLoadMetadata(): any;
    onLazyItemLoad(event: TreeTableLazyLoadEvent): void;
    /**
     * Resets scroll to top.
     * @group Method
     */
    resetScrollTop(): void;
    /**
     * Scrolls to given index when using virtual scroll.
     * @param {number} index - index of the element.
     * @group Method
     */
    scrollToVirtualIndex(index: number): void;
    /**
     * Scrolls to given index.
     * @param {ScrollToOptions} options - Scroll options.
     * @group Method
     */
    scrollTo(options: ScrollToOptions): void;
    isEmpty(): boolean;
    getBlockableElement(): HTMLElement;
    onColumnResizeBegin(event: MouseEvent): void;
    onColumnResize(event: MouseEvent): void;
    onColumnResizeEnd(event: MouseEvent, column: any): void;
    findParentScrollableView(column: any): any;
    resizeColGroup(table: Nullable<HTMLElement>, resizeColumnIndex: Nullable<number>, newColumnWidth: Nullable<number>, nextColumnWidth: Nullable<number>): void;
    onColumnDragStart(event: DragEvent, columnElement: any): void;
    onColumnDragEnter(event: DragEvent, dropHeader: any): void;
    onColumnDragLeave(event: DragEvent): void;
    onColumnDrop(event: DragEvent, dropColumn: any): void;
    handleRowClick(event: any): void;
    handleRowTouchEnd(event: Event): void;
    handleRowRightClick(event: any): void;
    toggleNodeWithCheckbox(event: any): void;
    toggleNodesWithCheckbox(event: Event, check: boolean): void;
    toggleAll(checked: boolean): void;
    propagateSelectionUp(node: TreeTableNode, select: boolean): void;
    propagateSelectionDown(node: TreeTableNode, select: boolean): void;
    isSelected(node: TreeTableNode): boolean;
    isNodeSelected(node: any): boolean;
    isNodePartialSelected(node: any): boolean;
    nodeKey(node: any): any;
    toggleCheckbox(event: any): void;
    propagateDown(node: any, check: any): void;
    propagateUp(node: any, check: any): void;
    findIndexInSelection(node: any): number;
    isSingleSelectionMode(): boolean;
    isMultipleSelectionMode(): boolean;
    equals(node1: TreeTableNode, node2: TreeTableNode): boolean;
    filter(value: string | string[], field: string, matchMode: string): void;
    filterGlobal(value: string, matchMode: string): void;
    isFilterBlank(filter: any): boolean;
    _filter(): void;
    findFilteredNodes(node: TreeTableNode, paramsWithoutNode: any): boolean;
    isFilterMatched(node: TreeTableNode, filterOptions: TreeTableFilterOptions): boolean;
    isNodeLeaf(node: TreeTableNode): boolean;
    hasFilter(): boolean;
    /**
     * Clears the sort and paginator state.
     * @group Method
     */
    reset(): void;
    updateEditingCell(cell: any, data: any, field: string): void;
    isEditingCellValid(): boolean;
    bindDocumentEditListener(): void;
    unbindDocumentEditListener(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeTable, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TreeTable, "p-treeTable, p-treetable, p-tree-table", never, { "columns": { "alias": "columns"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "tableStyle": { "alias": "tableStyle"; "required": false; }; "tableStyleClass": { "alias": "tableStyleClass"; "required": false; }; "autoLayout": { "alias": "autoLayout"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "lazyLoadOnInit": { "alias": "lazyLoadOnInit"; "required": false; }; "paginator": { "alias": "paginator"; "required": false; }; "rows": { "alias": "rows"; "required": false; }; "first": { "alias": "first"; "required": false; }; "pageLinks": { "alias": "pageLinks"; "required": false; }; "rowsPerPageOptions": { "alias": "rowsPerPageOptions"; "required": false; }; "alwaysShowPaginator": { "alias": "alwaysShowPaginator"; "required": false; }; "paginatorPosition": { "alias": "paginatorPosition"; "required": false; }; "paginatorStyleClass": { "alias": "paginatorStyleClass"; "required": false; }; "paginatorDropdownAppendTo": { "alias": "paginatorDropdownAppendTo"; "required": false; }; "currentPageReportTemplate": { "alias": "currentPageReportTemplate"; "required": false; }; "showCurrentPageReport": { "alias": "showCurrentPageReport"; "required": false; }; "showJumpToPageDropdown": { "alias": "showJumpToPageDropdown"; "required": false; }; "showFirstLastIcon": { "alias": "showFirstLastIcon"; "required": false; }; "showPageLinks": { "alias": "showPageLinks"; "required": false; }; "defaultSortOrder": { "alias": "defaultSortOrder"; "required": false; }; "sortMode": { "alias": "sortMode"; "required": false; }; "resetPageOnSort": { "alias": "resetPageOnSort"; "required": false; }; "customSort": { "alias": "customSort"; "required": false; }; "selectionMode": { "alias": "selectionMode"; "required": false; }; "contextMenuSelection": { "alias": "contextMenuSelection"; "required": false; }; "contextMenuSelectionMode": { "alias": "contextMenuSelectionMode"; "required": false; }; "dataKey": { "alias": "dataKey"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "compareSelectionBy": { "alias": "compareSelectionBy"; "required": false; }; "rowHover": { "alias": "rowHover"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "loadingIcon": { "alias": "loadingIcon"; "required": false; }; "showLoader": { "alias": "showLoader"; "required": false; }; "scrollable": { "alias": "scrollable"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "virtualScrollDelay": { "alias": "virtualScrollDelay"; "required": false; }; "frozenWidth": { "alias": "frozenWidth"; "required": false; }; "frozenColumns": { "alias": "frozenColumns"; "required": false; }; "resizableColumns": { "alias": "resizableColumns"; "required": false; }; "columnResizeMode": { "alias": "columnResizeMode"; "required": false; }; "reorderableColumns": { "alias": "reorderableColumns"; "required": false; }; "contextMenu": { "alias": "contextMenu"; "required": false; }; "rowTrackBy": { "alias": "rowTrackBy"; "required": false; }; "filters": { "alias": "filters"; "required": false; }; "globalFilterFields": { "alias": "globalFilterFields"; "required": false; }; "filterDelay": { "alias": "filterDelay"; "required": false; }; "filterMode": { "alias": "filterMode"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "paginatorLocale": { "alias": "paginatorLocale"; "required": false; }; "totalRecords": { "alias": "totalRecords"; "required": false; }; "sortField": { "alias": "sortField"; "required": false; }; "sortOrder": { "alias": "sortOrder"; "required": false; }; "multiSortMeta": { "alias": "multiSortMeta"; "required": false; }; "selection": { "alias": "selection"; "required": false; }; "value": { "alias": "value"; "required": false; }; "virtualRowHeight": { "alias": "virtualRowHeight"; "required": false; }; "selectionKeys": { "alias": "selectionKeys"; "required": false; }; "showGridlines": { "alias": "showGridlines"; "required": false; }; }, { "selectionChange": "selectionChange"; "contextMenuSelectionChange": "contextMenuSelectionChange"; "onFilter": "onFilter"; "onNodeExpand": "onNodeExpand"; "onNodeCollapse": "onNodeCollapse"; "onPage": "onPage"; "onSort": "onSort"; "onLazyLoad": "onLazyLoad"; "sortFunction": "sortFunction"; "onColResize": "onColResize"; "onColReorder": "onColReorder"; "onNodeSelect": "onNodeSelect"; "onNodeUnselect": "onNodeUnselect"; "onContextMenuSelect": "onContextMenuSelect"; "onHeaderCheckboxToggle": "onHeaderCheckboxToggle"; "onEditInit": "onEditInit"; "onEditComplete": "onEditComplete"; "onEditCancel": "onEditCancel"; "selectionKeysChange": "selectionKeysChange"; }, ["_colGroupTemplate", "_captionTemplate", "_headerTemplate", "_bodyTemplate", "_footerTemplate", "_summaryTemplate", "_emptyMessageTemplate", "_paginatorLeftTemplate", "_paginatorRightTemplate", "_paginatorDropdownItemTemplate", "_frozenHeaderTemplate", "_frozenBodyTemplate", "_frozenFooterTemplate", "_frozenColGroupTemplate", "_loadingIconTemplate", "_reorderIndicatorUpIconTemplate", "_reorderIndicatorDownIconTemplate", "_sortIconTemplate", "_checkboxIconTemplate", "_headerCheckboxIconTemplate", "_togglerIconTemplate", "_paginatorFirstPageLinkIconTemplate", "_paginatorLastPageLinkIconTemplate", "_paginatorPreviousPageLinkIconTemplate", "_paginatorNextPageLinkIconTemplate", "_loaderTemplate", "templates"], never, false, never>;
    static ngAcceptInputType_autoLayout: unknown;
    static ngAcceptInputType_lazy: unknown;
    static ngAcceptInputType_lazyLoadOnInit: unknown;
    static ngAcceptInputType_paginator: unknown;
    static ngAcceptInputType_rows: unknown;
    static ngAcceptInputType_first: unknown;
    static ngAcceptInputType_pageLinks: unknown;
    static ngAcceptInputType_alwaysShowPaginator: unknown;
    static ngAcceptInputType_showCurrentPageReport: unknown;
    static ngAcceptInputType_showJumpToPageDropdown: unknown;
    static ngAcceptInputType_showFirstLastIcon: unknown;
    static ngAcceptInputType_showPageLinks: unknown;
    static ngAcceptInputType_defaultSortOrder: unknown;
    static ngAcceptInputType_resetPageOnSort: unknown;
    static ngAcceptInputType_customSort: unknown;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_rowHover: unknown;
    static ngAcceptInputType_loading: unknown;
    static ngAcceptInputType_showLoader: unknown;
    static ngAcceptInputType_scrollable: unknown;
    static ngAcceptInputType_virtualScroll: unknown;
    static ngAcceptInputType_virtualScrollItemSize: unknown;
    static ngAcceptInputType_virtualScrollDelay: unknown;
    static ngAcceptInputType_resizableColumns: unknown;
    static ngAcceptInputType_reorderableColumns: unknown;
    static ngAcceptInputType_filterDelay: unknown;
    static ngAcceptInputType_showGridlines: unknown;
}
declare class TTBody {
    tt: TreeTable;
    treeTableService: TreeTableService;
    cd: ChangeDetectorRef;
    columns: any[] | undefined;
    template: Nullable<TemplateRef<any>>;
    frozen: boolean | undefined;
    serializedNodes: any;
    scrollerOptions: any;
    subscription: Subscription;
    constructor(tt: TreeTable, treeTableService: TreeTableService, cd: ChangeDetectorRef);
    getScrollerOption(option: any, options?: any): any;
    getRowIndex(rowIndex: number): any;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTBody, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TTBody, "[pTreeTableBody]", never, { "columns": { "alias": "pTreeTableBody"; "required": false; }; "template": { "alias": "pTreeTableBodyTemplate"; "required": false; }; "frozen": { "alias": "frozen"; "required": false; }; "serializedNodes": { "alias": "serializedNodes"; "required": false; }; "scrollerOptions": { "alias": "scrollerOptions"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_frozen: unknown;
}
declare class TTScrollableView extends BaseComponent implements AfterViewInit, OnDestroy {
    tt: TreeTable;
    el: ElementRef;
    zone: NgZone;
    columns: any[] | undefined;
    frozen: boolean | undefined;
    scrollHeaderViewChild: Nullable<ElementRef>;
    scrollHeaderBoxViewChild: Nullable<ElementRef>;
    scrollBodyViewChild: Nullable<ElementRef>;
    scrollTableViewChild: Nullable<ElementRef>;
    scrollLoadingTableViewChild: Nullable<ElementRef>;
    scrollFooterViewChild: Nullable<ElementRef>;
    scrollFooterBoxViewChild: Nullable<ElementRef>;
    scrollableAlignerViewChild: Nullable<ElementRef>;
    scroller: Nullable<Scroller>;
    headerScrollListener: VoidListener;
    bodyScrollListener: VoidListener;
    footerScrollListener: VoidListener;
    frozenSiblingBody: Nullable<Element>;
    totalRecordsSubscription: Nullable<Subscription>;
    _scrollHeight: string | undefined | null;
    preventBodyScrollPropagation: boolean | undefined;
    _componentStyle: TreeTableStyle;
    get scrollHeight(): string | undefined | null;
    set scrollHeight(val: string | undefined | null);
    constructor(tt: TreeTable, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    bindEvents(): void;
    unbindEvents(): void;
    onHeaderScroll(): void;
    onFooterScroll(): void;
    onBodyScroll(event: any): void;
    scrollToVirtualIndex(index: number): void;
    scrollTo(options: ScrollToOptions): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTScrollableView, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TTScrollableView, "[ttScrollableView]", never, { "columns": { "alias": "ttScrollableView"; "required": false; }; "frozen": { "alias": "frozen"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_frozen: unknown;
}
declare class TTSortableColumn extends BaseComponent implements OnInit, OnDestroy {
    tt: TreeTable;
    field: string | undefined;
    ttSortableColumnDisabled: boolean | undefined;
    sorted: boolean | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TreeTableStyle;
    get ariaSorted(): "none" | "descending" | "ascending";
    constructor(tt: TreeTable);
    ngOnInit(): void;
    updateSortState(): void;
    onClick(event: MouseEvent): void;
    onEnterKey(event: MouseEvent): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTSortableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTSortableColumn, "[ttSortableColumn]", never, { "field": { "alias": "ttSortableColumn"; "required": false; }; "ttSortableColumnDisabled": { "alias": "ttSortableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttSortableColumnDisabled: unknown;
}
declare class TTSortIcon extends BaseComponent implements OnInit, OnDestroy {
    tt: TreeTable;
    cd: ChangeDetectorRef;
    field: string | undefined;
    ariaLabelDesc: string | undefined;
    ariaLabelAsc: string | undefined;
    subscription: Subscription | undefined;
    sortOrder: number | undefined;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick(event: Event): void;
    getMultiSortMetaIndex(): number;
    updateSortState(): void;
    getBadgeValue(): number;
    isMultiSorted(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTSortIcon, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TTSortIcon, "p-treeTableSortIcon, p-treetable-sort-icon, p-tree-table-sort-icon", never, { "field": { "alias": "field"; "required": false; }; "ariaLabelDesc": { "alias": "ariaLabelDesc"; "required": false; }; "ariaLabelAsc": { "alias": "ariaLabelAsc"; "required": false; }; }, {}, never, never, false, never>;
}
declare class TTResizableColumn implements AfterViewInit, OnDestroy {
    private document;
    private platformId;
    private renderer;
    tt: TreeTable;
    el: ElementRef;
    zone: NgZone;
    ttResizableColumnDisabled: boolean | undefined;
    resizer: HTMLSpanElement | undefined;
    resizerMouseDownListener: VoidListener;
    documentMouseMoveListener: VoidListener;
    documentMouseUpListener: VoidListener;
    constructor(document: Document, platformId: any, renderer: Renderer2, tt: TreeTable, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    bindDocumentEvents(): void;
    unbindDocumentEvents(): void;
    onMouseDown(event: MouseEvent): void;
    onDocumentMouseMove(event: MouseEvent): void;
    onDocumentMouseUp(event: MouseEvent): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTResizableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTResizableColumn, "[ttResizableColumn]", never, { "ttResizableColumnDisabled": { "alias": "ttResizableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttResizableColumnDisabled: unknown;
}
declare class TTReorderableColumn implements AfterViewInit, OnDestroy {
    private document;
    private platformId;
    private renderer;
    tt: TreeTable;
    el: ElementRef;
    zone: NgZone;
    ttReorderableColumnDisabled: boolean | undefined;
    dragStartListener: VoidListener;
    dragOverListener: VoidListener;
    dragEnterListener: VoidListener;
    dragLeaveListener: VoidListener;
    mouseDownListener: VoidListener;
    constructor(document: Document, platformId: any, renderer: Renderer2, tt: TreeTable, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    bindEvents(): void;
    unbindEvents(): void;
    onMouseDown(event: any): void;
    onDragStart(event: DragEvent): void;
    onDragOver(event: DragEvent): void;
    onDragEnter(event: DragEvent): void;
    onDragLeave(event: DragEvent): void;
    onDrop(event: DragEvent): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTReorderableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTReorderableColumn, "[ttReorderableColumn]", never, { "ttReorderableColumnDisabled": { "alias": "ttReorderableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttReorderableColumnDisabled: unknown;
}
declare class TTSelectableRow extends BaseComponent implements OnInit, OnDestroy {
    tt: TreeTable;
    tableService: TreeTableService;
    rowNode: any;
    ttSelectableRowDisabled: boolean | undefined;
    selected: boolean | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable, tableService: TreeTableService);
    ngOnInit(): void;
    onClick(event: Event): void;
    onKeyDown(event: KeyboardEvent): void;
    onTouchEnd(event: Event): void;
    onEnterKey(event: any): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTSelectableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTSelectableRow, "[ttSelectableRow]", never, { "rowNode": { "alias": "ttSelectableRow"; "required": false; }; "ttSelectableRowDisabled": { "alias": "ttSelectableRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttSelectableRowDisabled: unknown;
}
declare class TTSelectableRowDblClick extends BaseComponent implements OnInit, OnDestroy {
    tt: TreeTable;
    tableService: TreeTableService;
    rowNode: any;
    ttSelectableRowDisabled: boolean | undefined;
    selected: boolean | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable, tableService: TreeTableService);
    ngOnInit(): void;
    onClick(event: Event): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTSelectableRowDblClick, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTSelectableRowDblClick, "[ttSelectableRowDblClick]", never, { "rowNode": { "alias": "ttSelectableRowDblClick"; "required": false; }; "ttSelectableRowDisabled": { "alias": "ttSelectableRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttSelectableRowDisabled: unknown;
}
declare class TTContextMenuRow extends BaseComponent {
    tt: TreeTable;
    tableService: TreeTableService;
    rowNode: any | undefined;
    ttContextMenuRowDisabled: boolean | undefined;
    selected: boolean | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable, tableService: TreeTableService);
    onContextMenu(event: Event): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTContextMenuRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTContextMenuRow, "[ttContextMenuRow]", never, { "rowNode": { "alias": "ttContextMenuRow"; "required": false; }; "ttContextMenuRowDisabled": { "alias": "ttContextMenuRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttContextMenuRowDisabled: unknown;
}
declare class TTCheckbox extends BaseComponent {
    tt: TreeTable;
    tableService: TreeTableService;
    cd: ChangeDetectorRef;
    disabled: boolean | undefined;
    rowNode: any;
    checked: boolean | undefined;
    partialChecked: boolean | undefined;
    focused: boolean | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable, tableService: TreeTableService, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick(event: Event): void;
    onFocus(): void;
    onBlur(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTCheckbox, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TTCheckbox, "p-treeTableCheckbox, p-treetable-checkbox, p-tree-table-checkbox", never, { "disabled": { "alias": "disabled"; "required": false; }; "rowNode": { "alias": "value"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_disabled: unknown;
}
declare class TTHeaderCheckbox {
    tt: TreeTable;
    tableService: TreeTableService;
    private cd;
    checked: boolean | undefined;
    disabled: boolean | undefined;
    selectionChangeSubscription: Subscription;
    valueChangeSubscription: Subscription;
    constructor(tt: TreeTable, tableService: TreeTableService, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick(event: Event): void;
    ngOnDestroy(): void;
    updateCheckedState(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTHeaderCheckbox, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TTHeaderCheckbox, "p-treeTableHeaderCheckbox", never, {}, {}, never, never, false, never>;
}
declare class TTEditableColumn implements AfterViewInit {
    tt: TreeTable;
    el: ElementRef;
    zone: NgZone;
    data: any;
    field: any;
    ttEditableColumnDisabled: boolean | undefined;
    constructor(tt: TreeTable, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    onClick(event: MouseEvent): void;
    openCell(): void;
    closeEditingCell(): void;
    onKeyDown(event: KeyboardEvent): void;
    findCell(element: any): any;
    moveToPreviousCell(event: KeyboardEvent): void;
    moveToNextCell(event: KeyboardEvent): void;
    findPreviousEditableColumn(cell: any): Element | null;
    findNextEditableColumn(cell: Element): Element | null;
    isEnabled(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTEditableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTEditableColumn, "[ttEditableColumn]", never, { "data": { "alias": "ttEditableColumn"; "required": false; }; "field": { "alias": "ttEditableColumnField"; "required": false; }; "ttEditableColumnDisabled": { "alias": "ttEditableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_ttEditableColumnDisabled: unknown;
}
declare class TreeTableCellEditor extends BaseComponent implements AfterContentInit {
    tt: TreeTable;
    editableColumn: TTEditableColumn;
    templates: Nullable<QueryList<PrimeTemplate>>;
    inputTemplate: Nullable<TemplateRef<any>>;
    outputTemplate: Nullable<TemplateRef<any>>;
    constructor(tt: TreeTable, editableColumn: TTEditableColumn);
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeTableCellEditor, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TreeTableCellEditor, "p-treeTableCellEditor, p-treetablecelleditor, p-treetable-cell-editor", never, {}, {}, ["templates"], never, false, never>;
}
declare class TTRow extends BaseComponent {
    tt: TreeTable;
    el: ElementRef;
    zone: NgZone;
    get level(): any;
    get styleClass(): any;
    get expanded(): any;
    rowNode: any;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable, el: ElementRef, zone: NgZone);
    onKeyDown(event: KeyboardEvent): void;
    onArrowDownKey(event: KeyboardEvent): void;
    onArrowUpKey(event: KeyboardEvent): void;
    onArrowRightKey(event: KeyboardEvent): void;
    onArrowLeftKey(event: KeyboardEvent): void;
    onHomeKey(event: KeyboardEvent): void;
    onEndKey(event: KeyboardEvent): void;
    onTabKey(event: KeyboardEvent): void;
    expand(event: Event): void;
    collapse(event: Event): void;
    focusRowChange(firstFocusableRow: any, currentFocusedRow: any, lastVisibleDescendant?: any): void;
    restoreFocus(index?: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TTRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TTRow, "[ttRow]", never, { "rowNode": { "alias": "ttRow"; "required": false; }; }, {}, never, never, false, never>;
}
declare class TreeTableToggler extends BaseComponent {
    tt: TreeTable;
    rowNode: any;
    _componentStyle: TreeTableStyle;
    constructor(tt: TreeTable);
    get toggleButtonAriaLabel(): string;
    onClick(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeTableToggler, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TreeTableToggler, "p-treeTableToggler, p-treetabletoggler, p-treetable-toggler", never, { "rowNode": { "alias": "rowNode"; "required": false; }; }, {}, never, never, false, never>;
}
declare class TreeTableModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeTableModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TreeTableModule, [typeof TreeTable, typeof TreeTableToggler, typeof TTScrollableView, typeof TTBody, typeof TTSortableColumn, typeof TTSortIcon, typeof TTResizableColumn, typeof TTRow, typeof TTReorderableColumn, typeof TTSelectableRow, typeof TTSelectableRowDblClick, typeof TTContextMenuRow, typeof TTCheckbox, typeof TTHeaderCheckbox, typeof TTEditableColumn, typeof TreeTableCellEditor], [typeof i1.CommonModule, typeof i2.PaginatorModule, typeof i3.Ripple, typeof i4.Scroller, typeof i5.SpinnerIcon, typeof i5.ArrowDownIcon, typeof i5.ArrowUpIcon, typeof i5.SortAltIcon, typeof i5.SortAmountUpAltIcon, typeof i5.SortAmountDownIcon, typeof i6.BadgeModule, typeof i5.CheckIcon, typeof i5.ChevronDownIcon, typeof i5.ChevronRightIcon, typeof i7.Checkbox, typeof i8.SharedModule, typeof i9.FormsModule], [typeof TreeTable, typeof i8.SharedModule, typeof TreeTableToggler, typeof TTSortableColumn, typeof TTSortIcon, typeof TTResizableColumn, typeof TTRow, typeof TTReorderableColumn, typeof TTSelectableRow, typeof TTSelectableRowDblClick, typeof TTContextMenuRow, typeof TTCheckbox, typeof TTHeaderCheckbox, typeof TTEditableColumn, typeof TreeTableCellEditor, typeof i4.Scroller]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TreeTableModule>;
}

export { TTBody, TTCheckbox, TTContextMenuRow, TTEditableColumn, TTHeaderCheckbox, TTReorderableColumn, TTResizableColumn, TTRow, TTScrollableView, TTSelectableRow, TTSelectableRowDblClick, TTSortIcon, TTSortableColumn, TreeTable, TreeTableCellEditor, TreeTableClasses, TreeTableModule, TreeTableService, TreeTableStyle, TreeTableToggler };
export type { TreeTableColResizeEvent, TreeTableColumnReorderEvent, TreeTableContextMenuSelectEvent, TreeTableEditEvent, TreeTableFilterEvent, TreeTableFilterOptions, TreeTableHeaderCheckboxToggleEvent, TreeTableLazyLoadEvent, TreeTableNodeCollapseEvent, TreeTableNodeExpandEvent, TreeTableNodeSelectEvent, TreeTableNodeUnSelectEvent, TreeTablePaginatorState, TreeTableSortEvent, TreeTableTemplates };
