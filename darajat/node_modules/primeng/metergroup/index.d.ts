import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 * Defines valid templates in MeterGroup.
 * @group Templates
 */
interface MeterGroupTemplates {
    /**
     * Custom label template.
     */
    label(context: {
        /**
         * Current value of the component.
         */
        $implicit?: MeterItem;
        /**
         * Total percent of the metergroup items.
         */
        totalPercent?: number;
        /**
         * Array of sequential sum of values of metergroup items.
         */
        percentages?: number;
    }): TemplateRef<any>;
    /**
     * Custom meter item template.
     */
    meter(context: {
        /**
         * Current value of the component.
         */
        $implicit?: MeterItem;
        /**
         * Current index of the meter item.
         */
        index?: number;
        /**
         * Style class of the meter item
         */
        class?: string;
        /**
         * Current orientation of the component.
         */
        orientation?: string;
        /**
         * Current width of the meter item.
         */
        size?: string;
        /**
         * Total percent of the metergroup items
         */
        totalPercent?: number;
    }): TemplateRef<any>;
    /**
     * Custom start template.
     */
    start(context: {
        /**
         * Current value of the component.
         */
        $implicit?: MeterItem;
        /**
         * Total percent of the metergroup items.
         */
        totalPercent?: number;
        /**
         * Array of sequential sum of values of metergroup items.
         */
        percentages?: number;
    }): TemplateRef<any>;
    /**
     * Custom start template.
     */
    end(context: {
        /**
         * Current value of the component.
         */
        $implicit?: MeterItem;
        /**
         * Total percent of the metergroup items.
         */
        totalPercent?: number;
        /**
         * Array of sequential sum of values of metergroup items.
         */
        percentages?: number;
    }): TemplateRef<any>;
    /**
     * Custom icon template.
     */
    icon(context: {
        /**
         * Current value of the component.
         */
        $implicit?: MeterItem;
        /**
         * Style class of the icon.
         */
        class?: string;
    }): TemplateRef<any>;
}
/**
 * Represents a meter item configuration.
 * @group Interface
 */
interface MeterItem {
    /**
     * Label of the meter item.
     */
    label?: string;
    /**
     * Value of the meter item.
     */
    value?: number;
    /**
     * Color of the meter item.
     */
    color?: string;
    /**
     * Icon of the meter item.
     */
    icon?: string;
}

/**
 *
 * MeterGroup is a group of process status indicators.
 *
 * [Live Demo](https://www.primeng.org/metergroup)
 *
 * @module metergroupstyle
 *
 */
declare enum MeterGroupClasses {
    /**
     * Class name of the root element
     */
    root = "p-metergroup",
    /**
     * Class name of the meters element
     */
    meters = "p-metergroup-meters",
    /**
     * Class name of the meter element
     */
    meter = "p-metergroup-meter",
    /**
     * Class name of the label list element
     */
    labelList = "p-metergroup-label-list",
    /**
     * Class name of the label element
     */
    label = "p-metergroup-label",
    /**
     * Class name of the label icon element
     */
    labelIcon = "p-metergroup-label-icon",
    /**
     * Class name of the label marker element
     */
    labelMarker = "p-metergroup-label-marker",
    /**
     * Class name of the label text element
     */
    labelText = "p-metergroup-label-text"
}
declare class MeterGroupStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-metergroup-horizontal': boolean;
            'p-metergroup-vertical': boolean;
        })[];
        meters: string;
        meter: string;
        labelList: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-metergroup-label-list-vertical': boolean;
            'p-metergroup-label-list-horizontal': boolean;
        })[];
        label: string;
        labelIcon: string;
        labelMarker: string;
        labelText: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<MeterGroupStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<MeterGroupStyle>;
}
interface MeterGroupStyle extends BaseStyle {
}

declare class MeterGroupLabel extends BaseComponent {
    value: any[];
    labelPosition: 'start' | 'end';
    labelOrientation: 'horizontal' | 'vertical';
    min: number;
    max: number;
    iconTemplate: TemplateRef<any> | undefined;
    parentInstance: MeterGroup;
    static ɵfac: i0.ɵɵFactoryDeclaration<MeterGroupLabel, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<MeterGroupLabel, "p-meterGroupLabel, p-metergrouplabel", never, { "value": { "alias": "value"; "required": false; }; "labelPosition": { "alias": "labelPosition"; "required": false; }; "labelOrientation": { "alias": "labelOrientation"; "required": false; }; "min": { "alias": "min"; "required": false; }; "max": { "alias": "max"; "required": false; }; "iconTemplate": { "alias": "iconTemplate"; "required": false; }; }, {}, never, never, true, never>;
}
/**
 * MeterGroup displays scalar measurements within a known range.
 * @group Components
 */
declare class MeterGroup extends BaseComponent implements AfterContentInit {
    /**
     * Current value of the metergroup.
     * @group Props
     */
    value: MeterItem[] | undefined;
    /**
     * Mininum boundary value.
     * @group Props
     */
    min: number;
    /**
     * Maximum boundary value.
     * @group Props
     */
    max: number;
    /**
     * Specifies the layout of the component, valid values are 'horizontal' and 'vertical'.
     * @group Props
     */
    orientation: 'horizontal' | 'vertical';
    /**
     * Specifies the label position of the component, valid values are 'start' and 'end'.
     * @group Props
     */
    labelPosition: 'start' | 'end';
    /**
     * Specifies the label orientation of the component, valid values are 'horizontal' and 'vertical'.
     * @group Props
     */
    labelOrientation: 'horizontal' | 'vertical' | undefined;
    /**
     * Style class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    get vertical(): boolean;
    labelTemplate: TemplateRef<any> | undefined;
    meterTemplate: TemplateRef<any> | undefined;
    endTemplate: TemplateRef<any> | undefined;
    startTemplate: TemplateRef<any> | undefined;
    iconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _labelTemplate: TemplateRef<any> | undefined;
    _meterTemplate: TemplateRef<any> | undefined;
    _endTemplate: TemplateRef<any> | undefined;
    _startTemplate: TemplateRef<any> | undefined;
    _iconTemplate: TemplateRef<any> | undefined;
    _componentStyle: MeterGroupStyle;
    constructor();
    ngAfterViewInit(): void;
    ngAfterContentInit(): void;
    percent(meter?: number): number;
    percentValue(meter: any): string;
    meterStyle(val: any): {
        backgroundColor: any;
        width: string;
        height: string;
    };
    totalPercent(): number;
    percentages(): any[];
    trackByFn(index: number): number;
    static ɵfac: i0.ɵɵFactoryDeclaration<MeterGroup, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<MeterGroup, "p-meterGroup, p-metergroup, p-meter-group", never, { "value": { "alias": "value"; "required": false; }; "min": { "alias": "min"; "required": false; }; "max": { "alias": "max"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; "labelPosition": { "alias": "labelPosition"; "required": false; }; "labelOrientation": { "alias": "labelOrientation"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; }, {}, ["labelTemplate", "meterTemplate", "endTemplate", "startTemplate", "iconTemplate", "templates"], never, true, never>;
}
declare class MeterGroupModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MeterGroupModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MeterGroupModule, never, [typeof MeterGroup, typeof i1.SharedModule], [typeof MeterGroup, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MeterGroupModule>;
}

export { MeterGroup, MeterGroupClasses, MeterGroupLabel, MeterGroupModule, MeterGroupStyle };
export type { MeterGroupTemplates, MeterItem };
