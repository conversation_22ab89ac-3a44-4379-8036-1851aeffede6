import * as i0 from '@angular/core';
import { TemplateRef, EventEmitter, ElementRef, AfterContentInit, QueryList, OnChanges, SimpleChanges } from '@angular/core';
import * as i1 from 'primeng/api';
import { MenuItem, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { BaseStyle } from 'primeng/base';

/**
 *
 * PanelMenu is a hybrid of Accordion and Tree components.
 *
 * [Live Demo](https://www.primeng.org/panelmenu/)
 *
 * @module panelmenustyle
 *
 */
declare enum PanelMenuClasses {
    /**
     * Class name of the root element
     */
    root = "p-panelmenu",
    /**
     * Class name of the panel element
     */
    panel = "p-panelmenu-panel",
    /**
     * Class name of the header element
     */
    header = "p-panelmenu-header",
    /**
     * Class name of the header content element
     */
    headerContent = "p-panelmenu-header-content",
    /**
     * Class name of the header link element
     */
    headerLink = "p-panelmenu-header-link",
    /**
     * Class name of the header icon element
     */
    headerIcon = "p-panelmenu-header-icon",
    /**
     * Class name of the header label element
     */
    headerLabel = "p-panelmenu-header-label",
    /**
     * Class name of the content container element
     */
    contentContainer = "p-panelmenu-content-container",
    /**
     * Class name of the content element
     */
    content = "p-panelmenu-content",
    /**
     * Class name of the root list element
     */
    rootList = "p-panelmenu-root-list",
    /**
     * Class name of the item element
     */
    item = "p-panelmenu-item",
    /**
     * Class name of the item content element
     */
    itemContent = "p-panelmenu-item-content",
    /**
     * Class name of the item link element
     */
    itemLink = "p-panelmenu-item-link",
    /**
     * Class name of the item icon element
     */
    itemIcon = "p-panelmenu-item-icon",
    /**
     * Class name of the item label element
     */
    itemLabel = "p-panelmenu-item-label",
    /**
     * Class name of the submenu icon element
     */
    submenuIcon = "p-panelmenu-submenu-icon",
    /**
     * Class name of the submenu element
     */
    submenu = "p-panelmenu-submenu",
    separator = "p-menuitem-separator"
}
declare class PanelMenuStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: () => string[];
        panel: string;
        header: ({ instance, item }: {
            instance: any;
            item: any;
        }) => (string | {
            'p-panelmenu-header-active': boolean;
            'p-disabled': any;
        })[];
        headerContent: string;
        headerLink: string;
        headerIcon: string;
        headerLabel: string;
        contentContainer: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => (string | {
            'p-panelmenu-expanded': any;
        })[];
        content: string;
        rootList: string;
        item: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => (string | {
            'p-focus': boolean;
            'p-disabled': any;
        })[];
        itemContent: string;
        itemLink: string;
        itemIcon: string;
        itemLabel: string;
        submenuIcon: string;
        submenu: string;
        separator: string;
        badge: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelMenuStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<PanelMenuStyle>;
}
interface PanelMenuStyle extends BaseStyle {
}

declare class PanelMenuSub extends BaseComponent {
    panelId: string | undefined;
    focusedItemId: string | undefined;
    items: any[];
    itemTemplate: TemplateRef<any> | undefined;
    level: number;
    activeItemPath: any[];
    root: boolean | undefined;
    tabindex: number | undefined;
    transitionOptions: string | undefined;
    parentExpanded: boolean | undefined;
    itemToggle: EventEmitter<any>;
    menuFocus: EventEmitter<any>;
    menuBlur: EventEmitter<any>;
    menuKeyDown: EventEmitter<any>;
    listViewChild: ElementRef;
    panelMenu: PanelMenu;
    _componentStyle: PanelMenuStyle;
    getItemId(processedItem: any): any;
    getItemKey(processedItem: any): any;
    getItemClass(processedItem: any): {
        'p-panelmenu-item': boolean;
        'p-disabled': any;
        'p-focus': boolean;
    };
    getItemProp(processedItem: any, name?: any, params?: any): any;
    getItemLabel(processedItem: any): any;
    isItemExpanded(processedItem: any): any;
    isItemActive(processedItem: any): any;
    isItemVisible(processedItem: any): boolean;
    isItemDisabled(processedItem: any): any;
    isItemFocused(processedItem: any): boolean;
    isItemGroup(processedItem: any): boolean;
    getAnimation(processedItem: any): {
        value: string;
        params: {
            transitionParams: string;
            height: string;
        };
    };
    getAriaSetSize(): number;
    getAriaPosInset(index: any): number;
    onItemClick(event: any, processedItem: any): void;
    onItemToggle(event: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelMenuSub, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<PanelMenuSub, "p-panelMenuSub, p-panelmenu-sub", never, { "panelId": { "alias": "panelId"; "required": false; }; "focusedItemId": { "alias": "focusedItemId"; "required": false; }; "items": { "alias": "items"; "required": false; }; "itemTemplate": { "alias": "itemTemplate"; "required": false; }; "level": { "alias": "level"; "required": false; }; "activeItemPath": { "alias": "activeItemPath"; "required": false; }; "root": { "alias": "root"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "transitionOptions": { "alias": "transitionOptions"; "required": false; }; "parentExpanded": { "alias": "parentExpanded"; "required": false; }; }, { "itemToggle": "itemToggle"; "menuFocus": "menuFocus"; "menuBlur": "menuBlur"; "menuKeyDown": "menuKeyDown"; }, never, never, true, never>;
    static ngAcceptInputType_level: unknown;
    static ngAcceptInputType_root: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_parentExpanded: unknown;
}
declare class PanelMenuList extends BaseComponent implements OnChanges {
    panelId: string | undefined;
    id: string | undefined;
    items: any[];
    itemTemplate: TemplateRef<any> | undefined;
    parentExpanded: boolean | undefined;
    expanded: boolean | undefined;
    transitionOptions: string | undefined;
    root: boolean | undefined;
    tabindex: number | undefined;
    activeItem: any;
    itemToggle: EventEmitter<any>;
    headerFocus: EventEmitter<any>;
    subMenuViewChild: PanelMenuSub;
    searchTimeout: any;
    searchValue: any;
    focused: boolean | undefined;
    focusedItem: i0.WritableSignal<any>;
    activeItemPath: i0.WritableSignal<any[]>;
    processedItems: i0.WritableSignal<any[]>;
    visibleItems: i0.Signal<any[]>;
    get focusedItemId(): any;
    ngOnChanges(changes: SimpleChanges): void;
    getItemProp(processedItem: any, name: any): any;
    getItemLabel(processedItem: any): any;
    isItemVisible(processedItem: any): boolean;
    isItemDisabled(processedItem: any): any;
    isItemActive(processedItem: any): boolean;
    isItemGroup(processedItem: any): boolean;
    isElementInPanel(event: any, element: any): any;
    isItemMatched(processedItem: any): any;
    isVisibleItem(processedItem: any): boolean;
    isValidItem(processedItem: any): boolean;
    findFirstItem(): any;
    findLastItem(): any;
    findItemByEventTarget(target: EventTarget): undefined | any;
    createProcessedItems(items: any, level?: number, parent?: {}, parentKey?: string): any[];
    findProcessedItemByItemKey(key: any, processedItems?: any, level?: number): any;
    flatItems(processedItems: any, processedFlattenItems?: any[]): any[];
    changeFocusedItem(event: any): void;
    scrollInView(): void;
    onFocus(event: any): void;
    onBlur(event: any): void;
    onItemToggle(event: any): void;
    onKeyDown(event: any): void;
    onArrowDownKey(event: any): void;
    onArrowUpKey(event: any): void;
    onArrowLeftKey(event: any): void;
    onArrowRightKey(event: any): void;
    onHomeKey(event: any): void;
    onEndKey(event: any): void;
    onEnterKey(event: any): void;
    onSpaceKey(event: any): void;
    findNextItem(processedItem: any): any;
    findPrevItem(processedItem: any): any;
    searchItems(event: any, char: any): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelMenuList, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<PanelMenuList, "p-panelMenuList, p-panel-menu-list", never, { "panelId": { "alias": "panelId"; "required": false; }; "id": { "alias": "id"; "required": false; }; "items": { "alias": "items"; "required": false; }; "itemTemplate": { "alias": "itemTemplate"; "required": false; }; "parentExpanded": { "alias": "parentExpanded"; "required": false; }; "expanded": { "alias": "expanded"; "required": false; }; "transitionOptions": { "alias": "transitionOptions"; "required": false; }; "root": { "alias": "root"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "activeItem": { "alias": "activeItem"; "required": false; }; }, { "itemToggle": "itemToggle"; "headerFocus": "headerFocus"; }, never, never, true, never>;
    static ngAcceptInputType_parentExpanded: unknown;
    static ngAcceptInputType_expanded: unknown;
    static ngAcceptInputType_root: unknown;
    static ngAcceptInputType_tabindex: unknown;
}
/**
 * PanelMenu is a hybrid of Accordion and Tree components.
 * @group Components
 */
declare class PanelMenu extends BaseComponent implements AfterContentInit {
    /**
     * An array of menuitems.
     * @group Props
     */
    model: MenuItem[] | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Whether multiple tabs can be activated at the same time or not.
     * @group Props
     */
    multiple: boolean;
    /**
     * Transition options of the animation.
     * @group Props
     */
    transitionOptions: string;
    /**
     * Current id state as a string.
     * @group Props
     */
    id: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    containerViewChild: ElementRef | undefined;
    /**
     * Template option of submenu icon.
     * @group Templates
     */
    submenuIconTemplate: TemplateRef<any> | undefined;
    /**
     * Template option of header icon.
     * @group Templates
     */
    headerIconTemplate: TemplateRef<any> | undefined;
    /**
     * Template option of item.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _submenuIconTemplate: TemplateRef<any> | undefined;
    _headerIconTemplate: TemplateRef<any> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    animating: boolean | undefined;
    activeItem: i0.WritableSignal<any>;
    _componentStyle: PanelMenuStyle;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    /**
     * Collapses open panels.
     * @group Method
     */
    collapseAll(): void;
    onToggleDone(): void;
    changeActiveItem(event: any, item: any, index?: number, selfActive?: boolean): void;
    getAnimation(item: MenuItem): {
        value: string;
        params: {
            transitionParams: string;
            height: string;
        };
    };
    getItemProp(item: any, name: any): any;
    getItemLabel(item: any): any;
    isItemActive(item: any): any;
    isItemVisible(item: any): boolean;
    isItemDisabled(item: any): any;
    isItemGroup(item: any): boolean;
    getPanelId(index: any, item?: any): any;
    getHeaderId(item: any, index: any): string;
    getContentId(item: any, index: any): string;
    updateFocusedHeader(event: any): void;
    changeFocusedHeader(event: any, element: any): void;
    findNextHeader(panelElement: any, selfCheck?: boolean): any;
    findPrevHeader(panelElement: any, selfCheck?: boolean): any;
    findFirstHeader(): any;
    findLastHeader(): any;
    onHeaderClick(event: any, item: any, index: any): void;
    onHeaderKeyDown(event: any, item: any, index: any): void;
    onHeaderArrowDownKey(event: any): void;
    onHeaderArrowUpKey(event: any): void;
    onHeaderHomeKey(event: any): void;
    onHeaderEndKey(event: any): void;
    onHeaderEnterKey(event: any, item: any, index: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelMenu, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<PanelMenu, "p-panelMenu, p-panelmenu, p-panel-menu", never, { "model": { "alias": "model"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "multiple": { "alias": "multiple"; "required": false; }; "transitionOptions": { "alias": "transitionOptions"; "required": false; }; "id": { "alias": "id"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; }, {}, ["submenuIconTemplate", "headerIconTemplate", "itemTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_multiple: unknown;
    static ngAcceptInputType_tabindex: unknown;
}
declare class PanelMenuModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelMenuModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<PanelMenuModule, never, [typeof PanelMenu, typeof i1.SharedModule], [typeof PanelMenu, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<PanelMenuModule>;
}

/**
 * Defines valid templates in PanelMenu.
 * @group Templates
 */
interface PanelMenuTemplates {
    /**
     * Custom item template.
     */
    item(context: {
        /**
         * Item instance.
         */
        $implicit: MenuItem;
    }): TemplateRef<{
        $implicit: MenuItem;
    }>;
    /**
     * Custom template of submenuicon.
     */
    submenuicon(): TemplateRef<any>;
}

export { PanelMenu, PanelMenuClasses, PanelMenuList, PanelMenuModule, PanelMenuStyle, PanelMenuSub };
export type { PanelMenuTemplates };
