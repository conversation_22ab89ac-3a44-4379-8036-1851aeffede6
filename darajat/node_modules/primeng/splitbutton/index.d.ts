import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, ElementRef, EventEmitter, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { MenuItem, TooltipOptions, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { TieredMenu } from 'primeng/tieredmenu';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 * Defines valid templates in SplitButton.
 * @group Templates
 */
interface SplitButtonTemplates {
    /**
     * Custom template of content.
     */
    content(): TemplateRef<any>;
    /**
     * Custom template of dropdownicon.
     */
    dropdownicon(): TemplateRef<any>;
}
/**
 * Defines ButtonProps interface.
 */
interface ButtonProps {
    ariaLabel?: string;
}
/**
 * Defines MenuButtonProps interface.
 */
interface MenuButtonProps {
    ariaLabel?: string;
    ariaHasPopup?: boolean;
    ariaExpanded?: boolean;
    ariaControls?: string;
}

/**
 *
 * SplitButton groups a set of commands in an overlay with a default command.
 *
 * [Live Demo](https://www.primeng.org/splitbutton/)
 *
 * @module splitbuttonstyle
 *
 */
declare enum SplitButtonClasses {
    /**
     * Class name of the root element
     */
    root = "p-splitbutton",
    /**
     * Class name of the button element
     */
    pcButton = "p-splitbutton-button",
    /**
     * Class name of the dropdown element
     */
    pcDropdown = "p-splitbutton-dropdown"
}
declare class SplitButtonStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            [x: string]: any;
            'p-splitbutton-raised': any;
            'p-splitbutton-rounded': any;
            'p-splitbutton-outlined': any;
            'p-splitbutton-text': any;
        })[];
        pcButton: string;
        pcDropdown: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SplitButtonStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SplitButtonStyle>;
}
interface SplitButtonStyle extends BaseStyle {
}

type SplitButtonIconPosition = 'left' | 'right';
/**
 * SplitButton groups a set of commands in an overlay with a default command.
 * @group Components
 */
declare class SplitButton extends BaseComponent implements AfterContentInit {
    /**
     * MenuModel instance to define the overlay items.
     * @group Props
     */
    model: MenuItem[] | undefined;
    /**
     * Defines the style of the button.
     * @group Props
     */
    severity: 'success' | 'info' | 'warn' | 'danger' | 'help' | 'primary' | 'secondary' | 'contrast' | null | undefined;
    /**
     * Add a shadow to indicate elevation.
     * @group Props
     */
    raised: boolean;
    /**
     * Add a circular border radius to the button.
     * @group Props
     */
    rounded: boolean;
    /**
     * Add a textual class to the button without a background initially.
     * @group Props
     */
    text: boolean;
    /**
     * Add a border class without a background initially.
     * @group Props
     */
    outlined: boolean;
    /**
     * Defines the size of the button.
     * @group Props
     */
    size: 'small' | 'large' | undefined | null;
    /**
     * Add a plain textual class to the button without a background initially.
     * @group Props
     */
    plain: boolean;
    /**
     * Name of the icon.
     * @group Props
     */
    icon: string | undefined;
    /**
     * Position of the icon.
     * @group Props
     */
    iconPos: SplitButtonIconPosition;
    /**
     * Text of the button.
     * @group Props
     */
    label: string | undefined;
    /**
     * Tooltip for the main button.
     * @group Props
     */
    tooltip: string | undefined;
    /**
     * Tooltip options for the main button.
     * @group Props
     */
    tooltipOptions: TooltipOptions | undefined;
    /**
     * Class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the overlay menu.
     * @group Props
     */
    menuStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the overlay menu.
     * @group Props
     */
    menuStyleClass: string | undefined;
    /**
     * Name of the dropdown icon.
     * @group Props
     */
    dropdownIcon: string | undefined;
    /**
     *  Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @group Props
     */
    appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;
    /**
     * Indicates the direction of the element.
     * @group Props
     */
    dir: string | undefined;
    /**
     * Defines a string that labels the expand button for accessibility.
     * @group Props
     */
    expandAriaLabel: string | undefined;
    /**
     * Transition options of the show animation.
     * @group Props
     */
    showTransitionOptions: string;
    /**
     * Transition options of the hide animation.
     * @group Props
     */
    hideTransitionOptions: string;
    /**
     * Button Props
     */
    buttonProps: ButtonProps | undefined;
    /**
     * Menu Button Props
     */
    menuButtonProps: MenuButtonProps | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * When present, it specifies that the element should be disabled.
     * @group Props
     */
    set disabled(v: boolean | undefined);
    get disabled(): boolean | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * When present, it specifies that the menu button element should be disabled.
     * @group Props
     */
    menuButtonDisabled: boolean;
    /**
     * When present, it specifies that the button element should be disabled.
     * @group Props
     */
    buttonDisabled: boolean;
    /**
     * Callback to invoke when default command button is clicked.
     * @param {MouseEvent} event - Mouse event.
     * @group Emits
     */
    onClick: EventEmitter<MouseEvent>;
    /**
     * Callback to invoke when overlay menu is hidden.
     * @group Emits
     */
    onMenuHide: EventEmitter<any>;
    /**
     * Callback to invoke when overlay menu is shown.
     * @group Emits
     */
    onMenuShow: EventEmitter<any>;
    /**
     * Callback to invoke when dropdown button is clicked.
     * @param {MouseEvent} event - Mouse event.
     * @group Emits
     */
    onDropdownClick: EventEmitter<MouseEvent>;
    buttonViewChild: ElementRef | undefined;
    menu: TieredMenu | undefined;
    /**
     * Template of the content.
     * @group Templates
     */
    contentTemplate: TemplateRef<any> | undefined;
    /**
     * Template of the dropdownicon.
     * @group Templates
     **/
    dropdownIconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    ariaId: string | undefined;
    isExpanded: i0.WritableSignal<boolean>;
    private _disabled;
    _componentStyle: SplitButtonStyle;
    _contentTemplate: TemplateRef<any> | undefined;
    _dropdownIconTemplate: TemplateRef<any> | undefined;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    onDefaultButtonClick(event: MouseEvent): void;
    onDropdownButtonClick(event?: MouseEvent): void;
    onDropdownButtonKeydown(event: KeyboardEvent): void;
    onHide(): void;
    onShow(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SplitButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SplitButton, "p-splitbutton, p-splitButton, p-split-button", never, { "model": { "alias": "model"; "required": false; }; "severity": { "alias": "severity"; "required": false; }; "raised": { "alias": "raised"; "required": false; }; "rounded": { "alias": "rounded"; "required": false; }; "text": { "alias": "text"; "required": false; }; "outlined": { "alias": "outlined"; "required": false; }; "size": { "alias": "size"; "required": false; }; "plain": { "alias": "plain"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "iconPos": { "alias": "iconPos"; "required": false; }; "label": { "alias": "label"; "required": false; }; "tooltip": { "alias": "tooltip"; "required": false; }; "tooltipOptions": { "alias": "tooltipOptions"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "menuStyle": { "alias": "menuStyle"; "required": false; }; "menuStyleClass": { "alias": "menuStyleClass"; "required": false; }; "dropdownIcon": { "alias": "dropdownIcon"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; }; "dir": { "alias": "dir"; "required": false; }; "expandAriaLabel": { "alias": "expandAriaLabel"; "required": false; }; "showTransitionOptions": { "alias": "showTransitionOptions"; "required": false; }; "hideTransitionOptions": { "alias": "hideTransitionOptions"; "required": false; }; "buttonProps": { "alias": "buttonProps"; "required": false; }; "menuButtonProps": { "alias": "menuButtonProps"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "menuButtonDisabled": { "alias": "menuButtonDisabled"; "required": false; }; "buttonDisabled": { "alias": "buttonDisabled"; "required": false; }; }, { "onClick": "onClick"; "onMenuHide": "onMenuHide"; "onMenuShow": "onMenuShow"; "onDropdownClick": "onDropdownClick"; }, ["contentTemplate", "dropdownIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_raised: unknown;
    static ngAcceptInputType_rounded: unknown;
    static ngAcceptInputType_text: unknown;
    static ngAcceptInputType_outlined: unknown;
    static ngAcceptInputType_plain: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_menuButtonDisabled: unknown;
    static ngAcceptInputType_buttonDisabled: unknown;
}
declare class SplitButtonModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SplitButtonModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SplitButtonModule, never, [typeof SplitButton, typeof i1.SharedModule], [typeof SplitButton, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SplitButtonModule>;
}

export { SplitButton, SplitButtonClasses, SplitButtonModule, SplitButtonStyle };
export type { ButtonProps, MenuButtonProps, SplitButtonTemplates };
