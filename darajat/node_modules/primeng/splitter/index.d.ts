import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, EventEmitter, QueryList, ElementRef } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { VoidListener, Nullable } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 * Custom panel resize start event.
 * @see {@link Splitter.onResizeStart}
 * @group Events
 */
interface SplitterResizeStartEvent {
    /**
     * Browser event.
     */
    originalEvent: TouchEvent | MouseEvent;
    /**
     * Collapsed state of the panel.
     */
    sizes: number[];
}
/**
 * Custom panel resize end event.
 * @see {@link Splitter.onResizeEnd}
 * @extends {SplitterResizeStartEvent}
 * @group Events
 */
interface SplitterResizeEndEvent extends SplitterResizeStartEvent {
}
/**
 * Defines valid templates in Panel.
 * @group Templates
 */
interface SplitterTemplates {
    /**
     * Custom panel template.
     */
    panel(): TemplateRef<any>;
}

/**
 *
 * Splitter is utilized to separate and resize panels.
 *
 * [Live Demo](https://www.primeng.org/splitter/)
 *
 * @module splitterstyle
 *
 */
declare enum SplitterClasses {
    /**
     * Class name of the root element
     */
    root = "p-splitter",
    /**
     * Class name of the gutter element
     */
    gutter = "p-splitter-gutter",
    /**
     * Class name of the gutter handle element
     */
    gutterHandle = "p-splitter-gutter-handle"
}
declare class SplitterStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => string[];
        panel: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-splitterpanel-nested': any;
        })[];
        gutter: string;
        gutterHandle: string;
    };
    inlineStyles: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            display: string;
            'flex-wrap': string;
            'flex-direction'?: undefined;
        } | {
            'flex-direction': string;
            display?: undefined;
            'flex-wrap'?: undefined;
        })[];
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SplitterStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SplitterStyle>;
}
interface SplitterStyle extends BaseStyle {
}

/**
 * Splitter is utilized to separate and resize panels.
 * @group Components
 */
declare class Splitter extends BaseComponent implements AfterContentInit {
    /**
     * Style class of the component.
     * @deprecated since v20. Use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Style class of the panel.
     * @group Props
     */
    panelStyleClass: string | undefined;
    /**
     * Inline style of the panel.
     * @group Props
     */
    panelStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Defines where a stateful splitter keeps its state, valid values are 'session' for sessionStorage and 'local' for localStorage.
     * @group Props
     */
    stateStorage: string | undefined;
    /**
     * Storage identifier of a stateful Splitter.
     * @group Props
     */
    stateKey: string | undefined | null;
    /**
     * Orientation of the panels. Valid values are 'horizontal' and 'vertical'.
     * @group Props
     */
    layout: string | undefined;
    /**
     * Size of the divider in pixels.
     * @group Props
     */
    gutterSize: number;
    /**
     * Step factor to increment/decrement the size of the panels while pressing the arrow keys.
     * @group Props
     */
    step: number;
    /**
     * Minimum size of the elements relative to 100%.
     * @group Props
     */
    minSizes: number[];
    /**
     * Size of the elements relative to 100%.
     * @group Props
     */
    get panelSizes(): number[];
    set panelSizes(val: number[]);
    /**
     * Callback to invoke when resize ends.
     * @param {SplitterResizeEndEvent} event - Custom panel resize end event
     * @group Emits
     */
    onResizeEnd: EventEmitter<SplitterResizeEndEvent>;
    /**
     * Callback to invoke when resize starts.
     * @param {SplitterResizeStartEvent} event - Custom panel resize start event
     * @group Emits
     */
    onResizeStart: EventEmitter<SplitterResizeStartEvent>;
    templates: QueryList<PrimeTemplate>;
    panelChildren: QueryList<ElementRef>;
    splitter: i0.Signal<any>;
    nestedState: i0.Signal<any>;
    panels: any[];
    dragging: boolean;
    mouseMoveListener: VoidListener;
    mouseUpListener: VoidListener;
    touchMoveListener: VoidListener;
    touchEndListener: VoidListener;
    size: Nullable<number>;
    gutterElement: Nullable<ElementRef | HTMLElement>;
    startPos: Nullable<number>;
    prevPanelElement: Nullable<ElementRef | HTMLElement>;
    nextPanelElement: Nullable<ElementRef | HTMLElement>;
    nextPanelSize: Nullable<number>;
    prevPanelSize: Nullable<number>;
    _panelSizes: number[];
    prevPanelIndex: Nullable<number>;
    timer: any;
    prevSize: any;
    _componentStyle: SplitterStyle;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    ngAfterViewInit(): void;
    resizeStart(event: TouchEvent | MouseEvent, index: number, isKeyDown?: boolean): void;
    onResize(event: MouseEvent, step?: number, isKeyDown?: boolean): void;
    resizeEnd(event: MouseEvent | TouchEvent): void;
    onGutterMouseDown(event: MouseEvent, index: number): void;
    onGutterTouchStart(event: TouchEvent, index: number): void;
    onGutterTouchMove(event: any): void;
    onGutterTouchEnd(event: TouchEvent): void;
    repeat(event: any, index: any, step: any): void;
    setTimer(event: any, index: any, step: any): void;
    clearTimer(): void;
    onGutterKeyUp(event: any): void;
    onGutterKeyDown(event: any, index: any): void;
    validateResize(newPrevPanelSize: number, newNextPanelSize: number): boolean;
    bindMouseListeners(): void;
    bindTouchListeners(): void;
    unbindMouseListeners(): void;
    unbindTouchListeners(): void;
    clear(): void;
    isStateful(): boolean;
    getStorage(): Storage;
    saveState(): void;
    restoreState(): boolean;
    gutterStyle(): {
        width: string;
        height?: undefined;
    } | {
        height: string;
        width?: undefined;
    };
    horizontal(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<Splitter, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Splitter, "p-splitter", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "panelStyleClass": { "alias": "panelStyleClass"; "required": false; }; "panelStyle": { "alias": "panelStyle"; "required": false; }; "stateStorage": { "alias": "stateStorage"; "required": false; }; "stateKey": { "alias": "stateKey"; "required": false; }; "layout": { "alias": "layout"; "required": false; }; "gutterSize": { "alias": "gutterSize"; "required": false; }; "step": { "alias": "step"; "required": false; }; "minSizes": { "alias": "minSizes"; "required": false; }; "panelSizes": { "alias": "panelSizes"; "required": false; }; }, { "onResizeEnd": "onResizeEnd"; "onResizeStart": "onResizeStart"; }, ["splitter", "templates", "panelChildren"], never, true, never>;
    static ngAcceptInputType_gutterSize: unknown;
    static ngAcceptInputType_step: unknown;
}
declare class SplitterModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SplitterModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SplitterModule, never, [typeof Splitter, typeof i1.SharedModule], [typeof Splitter, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SplitterModule>;
}

export { Splitter, SplitterClasses, SplitterModule, SplitterStyle };
export type { SplitterResizeEndEvent, SplitterResizeStartEvent, SplitterTemplates };
