import * as i0 from '@angular/core';
import { AfterViewInit, AfterContentInit, OnDestroy, ElementRef, TemplateRef, QueryList, NgZone } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 *
 * ScrollPanel is a cross browser, lightweight and themable alternative to native browser scrollbar.
 *
 * [Live Demo](https://www.primeng.org/scrollpanel/)
 *
 * @module scrollpanelstyle
 *
 */
declare enum ScrollPanelClasses {
    /**
     * Class name of the root element
     */
    root = "p-scrollpanel",
    /**
     * Class name of the content container element
     */
    contentContainer = "p-scrollpanel-content-container",
    /**
     * Class name of the content element
     */
    content = "p-scrollpanel-content",
    /**
     * Class name of the bar x element
     */
    barX = "p-scrollpanel-bar-x",
    /**
     * Class name of the bar y element
     */
    barY = "p-scrollpanel-bar-y"
}
declare class ScrollPanelStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: string;
        contentContainer: string;
        content: string;
        barX: string;
        barY: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollPanelStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ScrollPanelStyle>;
}
interface ScrollPanelStyle extends BaseStyle {
}

/**
 * ScrollPanel is a cross browser, lightweight and themable alternative to native browser scrollbar.
 * @group Components
 */
declare class ScrollPanel extends BaseComponent implements AfterViewInit, AfterContentInit, OnDestroy {
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Step factor to scroll the content while pressing the arrow keys.
     * @group Props
     */
    step: number;
    contentViewChild: ElementRef | undefined;
    xBarViewChild: ElementRef | undefined;
    yBarViewChild: ElementRef | undefined;
    /**
     * Defines template option for content.
     * @group Templates
     */
    contentTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _contentTemplate: TemplateRef<any> | undefined;
    scrollYRatio: number | undefined;
    scrollXRatio: number | undefined;
    timeoutFrame: any;
    initialized: boolean;
    lastPageY: number | undefined;
    lastPageX: number | undefined;
    isXBarClicked: boolean;
    isYBarClicked: boolean;
    lastScrollLeft: number;
    lastScrollTop: number;
    orientation: string;
    timer: any;
    contentId: string | undefined;
    windowResizeListener: VoidFunction | null | undefined;
    contentScrollListener: VoidFunction | null | undefined;
    mouseEnterListener: VoidFunction | null | undefined;
    xBarMouseDownListener: VoidFunction | null | undefined;
    yBarMouseDownListener: VoidFunction | null | undefined;
    documentMouseMoveListener: Nullable<(event?: any) => void>;
    documentMouseUpListener: Nullable<(event?: any) => void>;
    _componentStyle: ScrollPanelStyle;
    zone: NgZone;
    ngOnInit(): void;
    ngAfterViewInit(): void;
    ngAfterContentInit(): void;
    calculateContainerHeight(): void;
    moveBar(): void;
    onScroll(event: any): void;
    onKeyDown(event: any): void;
    onKeyUp(): void;
    repeat(bar: any, step: any): void;
    setTimer(bar: any, step: any): void;
    clearTimer(): void;
    bindDocumentMouseListeners(): void;
    unbindDocumentMouseListeners(): void;
    onYBarMouseDown(e: MouseEvent): void;
    onXBarMouseDown(e: MouseEvent): void;
    onDocumentMouseMove(e: MouseEvent): void;
    onMouseMoveForXBar(e: MouseEvent): void;
    onMouseMoveForYBar(e: MouseEvent): void;
    /**
     * Scrolls the top location to the given value.
     * @param scrollTop
     * @group Method
     */
    scrollTop(scrollTop: number): void;
    onFocus(event: any): void;
    onBlur(): void;
    onDocumentMouseUp(e: Event): void;
    requestAnimationFrame(f: VoidFunction): void;
    unbindListeners(): void;
    ngOnDestroy(): void;
    /**
     * Refreshes the position and size of the scrollbar.
     * @group Method
     */
    refresh(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollPanel, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ScrollPanel, "p-scroll-panel, p-scrollPanel, p-scrollpanel", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "step": { "alias": "step"; "required": false; }; }, {}, ["contentTemplate", "templates"], ["*"], true, never>;
    static ngAcceptInputType_step: unknown;
}
declare class ScrollPanelModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollPanelModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ScrollPanelModule, never, [typeof ScrollPanel, typeof i1.SharedModule], [typeof ScrollPanel, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ScrollPanelModule>;
}

/**
 * Defines valid templates in ScrollPanel.
 * @group Templates
 */
interface ScrollPanelTemplates {
    /**
     * Custom content template.
     */
    content(): TemplateRef<any>;
}

export { ScrollPanel, ScrollPanelClasses, ScrollPanelModule, ScrollPanelStyle };
export type { ScrollPanelTemplates };
