import * as i0 from '@angular/core';
import { After<PERSON><PERSON>ntInit, EventEmitter, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Message groups a collection of contents in tabs.
 *
 * [Live Demo](https://www.primeng.org/message/)
 *
 * @module messagestyle
 *
 */
declare enum MessageClasses {
    /**
     * Class name of the root element
     */
    root = "p-message",
    /**
     * Class name of the content element
     */
    content = "p-message-content",
    /**
     * Class name of the icon element
     */
    icon = "p-message-icon",
    /**
     * Class name of the text element
     */
    text = "p-message-text",
    /**
     * Class name of the close button element
     */
    closeButton = "p-message-close-button",
    /**
     * Class name of the close icon element
     */
    closeIcon = "p-message-close-icon"
}
declare class MessageStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-message-sm': boolean;
            'p-message-lg': boolean;
        })[];
        content: string;
        icon: string;
        text: string;
        closeButton: string;
        closeIcon: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<MessageStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<MessageStyle>;
}
interface MessageStyle extends BaseStyle {
}

/**
 * Message groups a collection of contents in tabs.
 * @group Components
 */
declare class Message extends BaseComponent implements AfterContentInit {
    /**
     * Severity level of the message.
     * @defaultValue 'info'
     * @group Props
     */
    severity: string | 'success' | 'info' | 'warn' | 'error' | 'secondary' | 'contrast' | undefined | null;
    /**
     * Text content.
     * @deprecated since v20.0.0. Use content projection instead '<p-message>Content</p-message>'.
     * @group Props
     */
    text: string | undefined;
    /**
     * Whether displaying messages would be escaped or not.
     * @deprecated since v20.0.0. Use content projection instead '<p-message>Content</p-message>'.
     * @group Props
     */
    escape: boolean;
    /**
     * Inline style of the component.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the component.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Whether the message can be closed manually using the close icon.
     * @group Props
     * @defaultValue false
     */
    closable: boolean;
    /**
     * Icon to display in the message.
     * @group Props
     * @defaultValue undefined
     */
    icon: string | undefined;
    /**
     * Icon to display in the message close button.
     * @group Props
     * @defaultValue undefined
     */
    closeIcon: string | undefined;
    /**
     * Delay in milliseconds to close the message automatically.
     * @defaultValue undefined
     */
    life: number | undefined;
    /**
     * Transition options of the show animation.
     * @defaultValue '300ms ease-out'
     * @group Props
     */
    showTransitionOptions: string;
    /**
     * Transition options of the hide animation.
     * @defaultValue '200ms cubic-bezier(0.86, 0, 0.07, 1)'
     * @group Props
     */
    hideTransitionOptions: string;
    /**
     * Defines the size of the component.
     * @group Props
     */
    size: 'large' | 'small' | undefined;
    /**
     * Specifies the input variant of the component.
     * @group Props
     */
    variant: 'outlined' | 'text' | 'simple' | undefined;
    /**
     * Emits when the message is closed.
     * @param {{ originalEvent: Event }} event - The event object containing the original event.
     * @group Emits
     */
    onClose: EventEmitter<{
        originalEvent: Event;
    }>;
    get closeAriaLabel(): string;
    visible: i0.WritableSignal<boolean>;
    _componentStyle: MessageStyle;
    /**
     * Custom template of the message container.
     * @group Templates
     */
    containerTemplate: TemplateRef<any> | undefined;
    /**
     * Custom template of the message icon.
     * @group Templates
     */
    iconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom template of the close icon.
     * @group Templates
     */
    closeIconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _containerTemplate: TemplateRef<any> | undefined;
    _iconTemplate: TemplateRef<any> | undefined;
    _closeIconTemplate: TemplateRef<any> | undefined;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    /**
     * Closes the message.
     * @param {Event} event - Browser event.
     * @group Method
     */
    close(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Message, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Message, "p-message", never, { "severity": { "alias": "severity"; "required": false; }; "text": { "alias": "text"; "required": false; }; "escape": { "alias": "escape"; "required": false; }; "style": { "alias": "style"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "closable": { "alias": "closable"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "closeIcon": { "alias": "closeIcon"; "required": false; }; "life": { "alias": "life"; "required": false; }; "showTransitionOptions": { "alias": "showTransitionOptions"; "required": false; }; "hideTransitionOptions": { "alias": "hideTransitionOptions"; "required": false; }; "size": { "alias": "size"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; }, { "onClose": "onClose"; }, ["containerTemplate", "iconTemplate", "closeIconTemplate", "templates"], ["*"], true, never>;
    static ngAcceptInputType_escape: unknown;
    static ngAcceptInputType_closable: unknown;
}
declare class MessageModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MessageModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MessageModule, never, [typeof Message, typeof i1.SharedModule], [typeof Message, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MessageModule>;
}

export { Message, MessageClasses, MessageModule, MessageStyle };
