import * as i0 from '@angular/core';
import { OnDestroy, NgZone, PipeTransform, OnInit, AfterContentInit, EventEmitter, ElementRef, TemplateRef, QueryList } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import * as i1 from 'primeng/api';
import { PrimeTemplate, OverlayService } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { BaseInput } from 'primeng/baseinput';
import { ConnectedOverlayScrollHandler } from 'primeng/dom';
import { Fluid } from 'primeng/fluid';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import { Subscription } from 'rxjs';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Password displays strength indicator for password fields.
 *
 * [Live Demo](https://www.primeng.org/password/)
 *
 * @module passwordstyle
 *
 */
declare enum PasswordClasses {
    /**
     * Class name of the root element
     */
    root = "p-password",
    /**
     * Class name of the pt input element
     */
    pcInputText = "p-password-input",
    /**
     * Class name of the mask icon element
     */
    maskIcon = "p-password-mask-icon",
    /**
     * Class name of the unmask icon element
     */
    unmaskIcon = "p-password-unmask-icon",
    /**
     * Class name of the overlay element
     */
    overlay = "p-password-overlay",
    /**
     * Class name of the meter element
     */
    meter = "p-password-meter",
    /**
     * Class name of the meter label element
     */
    meterLabel = "p-password-meter-label",
    /**
     * Class name of the meter text element
     */
    meterText = "p-password-meter-text",
    /**
     * Class name of the clear icon
     */
    clearIcon = "p-password-clear-icon"
}
declare class PasswordStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-inputwrapper-filled': any;
            'p-variant-filled': boolean;
            'p-inputwrapper-focus': any;
            'p-password-fluid': any;
        })[];
        rootDirective: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-inputwrapper-filled': any;
            'p-variant-filled': boolean;
            'p-password-fluid-directive': any;
        })[];
        pcInputText: string;
        maskIcon: string;
        unmaskIcon: string;
        overlay: string;
        content: string;
        meter: string;
        meterLabel: ({ instance }: {
            instance: any;
        }) => string;
        meterText: string;
        clearIcon: string;
    };
    inlineStyles: {
        root: ({ instance }: {
            instance: any;
        }) => {
            position: string;
        };
        overlay: {
            position: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<PasswordStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<PasswordStyle>;
}
interface PasswordStyle extends BaseStyle {
}

type Meter = {
    strength: string;
    width: string;
};
/**
 * Password directive.
 * @group Components
 */
declare class PasswordDirective extends BaseEditableHolder implements OnDestroy {
    zone: NgZone;
    /**
     * Text to prompt password entry. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    promptLabel: string;
    /**
     * Text for a weak password. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    weakLabel: string;
    /**
     * Text for a medium password. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    mediumLabel: string;
    /**
     * Text for a strong password. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    strongLabel: string;
    /**
     * Whether to show the strength indicator or not.
     * @group Props
     */
    feedback: boolean;
    /**
     * Sets the visibility of the password field.
     * @defaultValue false
     * @type boolean
     * @group Props
     */
    set showPassword(show: boolean);
    /**
     * Specifies the input variant of the component.
     * @defaultValue 'outlined'
     * @group Props
     */
    variant: i0.InputSignal<"filled" | "outlined">;
    /**
     * Spans 100% width of the container when enabled.
     * @defaultValue false
     * @group Props
     */
    fluid: i0.InputSignalWithTransform<boolean, unknown>;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: i0.InputSignal<"small" | "large">;
    pcFluid: Fluid;
    $variant: i0.Signal<"filled" | "outlined">;
    get hasFluid(): boolean;
    panel: Nullable<HTMLDivElement>;
    meter: Nullable<HTMLDivElement>;
    info: Nullable<HTMLDivElement>;
    filled: Nullable<boolean>;
    content: Nullable<HTMLDivElement>;
    label: Nullable<HTMLLabelElement>;
    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;
    documentResizeListener: VoidListener;
    _componentStyle: PasswordStyle;
    constructor(zone: NgZone);
    onInput(e: Event): void;
    createPanel(): void;
    showOverlay(): void;
    hideOverlay(): void;
    onFocus(): void;
    onBlur(): void;
    labelSignal: i0.WritableSignal<string>;
    onKeyup(e: Event): void;
    updateMeter(): void;
    getWidth(label: string): "" | "33.33%" | "66.66%" | "100%";
    strengthClass(label: any): string;
    testStrength(str: string): number;
    normalize(x: number, y: number): number;
    bindScrollListener(): void;
    unbindScrollListener(): void;
    bindDocumentResizeListener(): void;
    unbindDocumentResizeListener(): void;
    onWindowResize(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<PasswordDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<PasswordDirective, "[pPassword]", never, { "promptLabel": { "alias": "promptLabel"; "required": false; }; "weakLabel": { "alias": "weakLabel"; "required": false; }; "mediumLabel": { "alias": "mediumLabel"; "required": false; }; "strongLabel": { "alias": "strongLabel"; "required": false; }; "feedback": { "alias": "feedback"; "required": false; }; "showPassword": { "alias": "showPassword"; "required": false; }; "variant": { "alias": "variant"; "required": false; "isSignal": true; }; "fluid": { "alias": "fluid"; "required": false; "isSignal": true; }; "size": { "alias": "pSize"; "required": false; "isSignal": true; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_feedback: unknown;
}
type Mapper<T, G> = (item: T, ...args: any[]) => G;
declare class MapperPipe implements PipeTransform {
    transform<T, G>(value: T, mapper: Mapper<T, G>, ...args: unknown[]): G;
    static ɵfac: i0.ɵɵFactoryDeclaration<MapperPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<MapperPipe, "mapper", true>;
}
declare const Password_VALUE_ACCESSOR: any;
/**
 * Password displays strength indicator for password fields.
 * @group Components
 */
declare class Password extends BaseInput implements OnInit, AfterContentInit {
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Specifies one or more IDs in the DOM that labels the input field.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Label of the input for accessibility.
     * @group Props
     */
    label: string | undefined;
    /**
     * Text to prompt password entry. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    promptLabel: string | undefined;
    /**
     * Regex value for medium regex.
     * @group Props
     */
    mediumRegex: string;
    /**
     * Regex value for strong regex.
     * @group Props
     */
    strongRegex: string;
    /**
     * Text for a weak password. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    weakLabel: string | undefined;
    /**
     * Text for a medium password. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    mediumLabel: string | undefined;
    /**
     * specifies the maximum number of characters allowed in the input element.
     * @deprecated since v20.0.0, use maxlength instead.
     * @group Props
     */
    maxLength: number | undefined;
    /**
     * Text for a strong password. Defaults to PrimeNG I18N API configuration.
     * @group Props
     */
    strongLabel: string | undefined;
    /**
     * Identifier of the accessible input element.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * Whether to show the strength indicator or not.
     * @group Props
     */
    feedback: boolean;
    /**
     * Whether to show an icon to display the password as plain text.
     * @group Props
     */
    toggleMask: boolean | undefined;
    /**
     * Style class of the input field.
     * @group Props
     */
    inputStyleClass: string | undefined;
    /**
     * Style class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the input field.
     * @group Props
     */
    inputStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Transition options of the show animation.
     * @group Props
     */
    showTransitionOptions: string;
    /**
     * Transition options of the hide animation.
     * @group Props
     */
    hideTransitionOptions: string;
    /**
     * Specify automated assistance in filling out password by browser.
     * @group Props
     */
    autocomplete: string | undefined;
    /**
     * Advisory information to display on input.
     * @group Props
     */
    placeholder: string | undefined;
    /**
     * When enabled, a clear icon is displayed to clear the value.
     * @group Props
     */
    showClear: boolean;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex?: number;
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    /**
     * Callback to invoke when the component receives focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the component loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Callback to invoke when clear button is clicked.
     * @group Emits
     */
    onClear: EventEmitter<any>;
    input: ElementRef;
    contentTemplate: Nullable<TemplateRef<any>>;
    footerTemplate: Nullable<TemplateRef<any>>;
    headerTemplate: Nullable<TemplateRef<any>>;
    clearIconTemplate: Nullable<TemplateRef<any>>;
    hideIconTemplate: Nullable<TemplateRef<any>>;
    showIconTemplate: Nullable<TemplateRef<any>>;
    templates: QueryList<PrimeTemplate>;
    $appendTo: i0.Signal<any>;
    _contentTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _clearIconTemplate: TemplateRef<any> | undefined;
    _hideIconTemplate: TemplateRef<any> | undefined;
    _showIconTemplate: TemplateRef<any> | undefined;
    overlayVisible: boolean;
    meter: Nullable<Meter>;
    infoText: Nullable<string>;
    focused: boolean;
    unmasked: boolean;
    mediumCheckRegExp: RegExp;
    strongCheckRegExp: RegExp;
    resizeListener: VoidListener;
    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;
    overlay: any;
    value: Nullable<string>;
    translationSubscription: Nullable<Subscription>;
    _componentStyle: PasswordStyle;
    overlayService: OverlayService;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    onAnimationStart(event: AnimationEvent): void;
    onAnimationEnd(event: AnimationEvent): void;
    appendContainer(): void;
    alignOverlay(): void;
    onInput(event: Event): void;
    onInputFocus(event: Event): void;
    onInputBlur(event: Event): void;
    onKeyUp(event: KeyboardEvent): void;
    updateUI(value: string): void;
    onMaskToggle(): void;
    onOverlayClick(event: Event): void;
    testStrength(str: string): number;
    bindScrollListener(): void;
    bindResizeListener(): void;
    unbindScrollListener(): void;
    unbindResizeListener(): void;
    promptText(): any;
    weakText(): any;
    mediumText(): any;
    strongText(): any;
    restoreAppend(): void;
    inputType(unmasked: boolean): "password" | "text";
    getTranslation(option: string): any;
    clear(): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Password, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Password, "p-password", never, { "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "label": { "alias": "label"; "required": false; }; "promptLabel": { "alias": "promptLabel"; "required": false; }; "mediumRegex": { "alias": "mediumRegex"; "required": false; }; "strongRegex": { "alias": "strongRegex"; "required": false; }; "weakLabel": { "alias": "weakLabel"; "required": false; }; "mediumLabel": { "alias": "mediumLabel"; "required": false; }; "maxLength": { "alias": "maxLength"; "required": false; }; "strongLabel": { "alias": "strongLabel"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "feedback": { "alias": "feedback"; "required": false; }; "toggleMask": { "alias": "toggleMask"; "required": false; }; "inputStyleClass": { "alias": "inputStyleClass"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "inputStyle": { "alias": "inputStyle"; "required": false; }; "showTransitionOptions": { "alias": "showTransitionOptions"; "required": false; }; "hideTransitionOptions": { "alias": "hideTransitionOptions"; "required": false; }; "autocomplete": { "alias": "autocomplete"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "showClear": { "alias": "showClear"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, { "onFocus": "onFocus"; "onBlur": "onBlur"; "onClear": "onClear"; }, ["contentTemplate", "footerTemplate", "headerTemplate", "clearIconTemplate", "hideIconTemplate", "showIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_maxLength: unknown;
    static ngAcceptInputType_feedback: unknown;
    static ngAcceptInputType_toggleMask: unknown;
    static ngAcceptInputType_showClear: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_tabindex: unknown;
}
declare class PasswordModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<PasswordModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<PasswordModule, never, [typeof Password, typeof PasswordDirective, typeof i1.SharedModule], [typeof PasswordDirective, typeof Password, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<PasswordModule>;
}

export { MapperPipe, Password, PasswordClasses, PasswordDirective, PasswordModule, PasswordStyle, Password_VALUE_ACCESSOR };
