import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';
import * as i0 from '@angular/core';
import * as i1 from 'primeng/api';

/**
 *
 * Skeleton is a placeholder to display instead of the actual content.
 *
 * [Live Demo](https://www.primeng.org/skeleton/)
 *
 * @module skeletonstyle
 *
 */
declare enum SkeletonClasses {
    /**
     * Class name of the root element
     */
    root = "p-skeleton"
}
declare class SkeletonStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-skeleton-circle': boolean;
            'p-skeleton-animation-none': boolean;
        })[];
    };
    inlineStyles: {
        root: {
            position: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SkeletonStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SkeletonStyle>;
}
interface SkeletonStyle extends BaseStyle {
}

/**
 * Skeleton is a placeholder to display instead of the actual content.
 * @group Components
 */
declare class Skeleton extends BaseComponent {
    /**
     * Class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Shape of the element.
     * @group Props
     */
    shape: string;
    /**
     * Type of the animation.
     * @gruop Props
     */
    animation: string;
    /**
     * Border radius of the element, defaults to value from theme.
     * @group Props
     */
    borderRadius: string | undefined;
    /**
     * Size of the skeleton.
     * @group Props
     */
    size: string | undefined;
    /**
     * Width of the element.
     * @group Props
     */
    width: string;
    /**
     * Height of the element.
     * @group Props
     */
    height: string;
    _componentStyle: SkeletonStyle;
    get containerStyle(): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<Skeleton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Skeleton, "p-skeleton", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "shape": { "alias": "shape"; "required": false; }; "animation": { "alias": "animation"; "required": false; }; "borderRadius": { "alias": "borderRadius"; "required": false; }; "size": { "alias": "size"; "required": false; }; "width": { "alias": "width"; "required": false; }; "height": { "alias": "height"; "required": false; }; }, {}, never, never, true, never>;
}
declare class SkeletonModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SkeletonModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SkeletonModule, never, [typeof Skeleton, typeof i1.SharedModule], [typeof Skeleton, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SkeletonModule>;
}

export { Skeleton, SkeletonClasses, SkeletonModule, SkeletonStyle };
