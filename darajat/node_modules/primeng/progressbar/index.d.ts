import * as i0 from '@angular/core';
import { AfterContentInit, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * ProgressBar is a process status indicator.
 *
 * [Live Demo](https://www.primeng.org/progressbar)
 *
 * @module progressbarstyle
 *
 */
declare enum ProgressBarClasses {
    /**
     * Class name of the root element
     */
    root = "p-progressbar",
    /**
     * Class name of the value element
     */
    value = "p-progressbar-value",
    /**
     * Class name of the label element
     */
    label = "p-progressbar-label"
}
declare class ProgressBarStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-progressbar-determinate': boolean;
            'p-progressbar-indeterminate': boolean;
        })[];
        value: string;
        label: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ProgressBarStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ProgressBarStyle>;
}
interface ProgressBarStyle extends BaseStyle {
}

/**
 * ProgressBar is a process status indicator.
 * @group Components
 */
declare class ProgressBar extends BaseComponent implements AfterContentInit {
    /**
     * Current value of the progress.
     * @group Props
     */
    value: number | undefined;
    /**
     * Whether to display the progress bar value.
     * @group Props
     */
    showValue: boolean;
    /**
     * Style class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Style class of the value element.
     * @group Props
     */
    valueStyleClass: string | undefined;
    /**
     * Unit sign appended to the value.
     * @group Props
     */
    unit: string;
    /**
     * Defines the mode of the progress
     * @group Props
     */
    mode: string;
    /**
     * Color for the background of the progress.
     * @group Props
     */
    color: string | undefined;
    /**
     * Template of the content.
     * @group templates
     */
    contentTemplate: TemplateRef<any> | undefined;
    _componentStyle: ProgressBarStyle;
    templates: QueryList<PrimeTemplate> | undefined;
    _contentTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ProgressBar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ProgressBar, "p-progressBar, p-progressbar, p-progress-bar", never, { "value": { "alias": "value"; "required": false; }; "showValue": { "alias": "showValue"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "valueStyleClass": { "alias": "valueStyleClass"; "required": false; }; "unit": { "alias": "unit"; "required": false; }; "mode": { "alias": "mode"; "required": false; }; "color": { "alias": "color"; "required": false; }; }, {}, ["contentTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_value: unknown;
    static ngAcceptInputType_showValue: unknown;
}
declare class ProgressBarModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ProgressBarModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ProgressBarModule, never, [typeof ProgressBar, typeof i1.SharedModule], [typeof ProgressBar, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ProgressBarModule>;
}

export { ProgressBar, ProgressBarClasses, ProgressBarModule, ProgressBarStyle };
