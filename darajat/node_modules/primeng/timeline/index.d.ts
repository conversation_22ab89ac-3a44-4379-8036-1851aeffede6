import * as i0 from '@angular/core';
import { AfterContentInit, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { BlockableUI } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Nullable } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Timeline visualizes a series of chained events.
 *
 * [Live Demo](https://primeng.org/timeline)
 *
 * @module timelinestyle
 *
 */
declare enum TimelineClasses {
    /**
     * Class name of the root element
     */
    root = "p-timeline",
    /**
     * Class name of the event element
     */
    event = "p-timeline-event",
    /**
     * Class name of the event opposite element
     */
    eventOpposite = "p-timeline-event-opposite",
    /**
     * Class name of the event separator element
     */
    eventSeparator = "p-timeline-event-separator",
    /**
     * Class name of the event marker element
     */
    eventMarker = "p-timeline-event-marker",
    /**
     * Class name of the event connector element
     */
    eventConnector = "p-timeline-event-connector",
    /**
     * Class name of the event content element
     */
    eventContent = "p-timeline-event-content"
}
declare class TimelineStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => string[];
        event: string;
        eventOpposite: string;
        eventSeparator: string;
        eventMarker: string;
        eventConnector: string;
        eventContent: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TimelineStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TimelineStyle>;
}
interface TimelineStyle extends BaseStyle {
}

/**
 * Timeline visualizes a series of chained events.
 * @group Components
 */
declare class Timeline extends BaseComponent implements AfterContentInit, BlockableUI {
    /**
     * An array of events to display.
     * @group Props
     */
    value: any[] | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Position of the timeline bar relative to the content. Valid values are "left", "right" for vertical layout and "top", "bottom" for horizontal layout.
     * @group Props
     */
    align: string;
    /**
     * Orientation of the timeline.
     * @group Props
     */
    layout: 'vertical' | 'horizontal';
    /**
     * Custom content template.
     * @group Templates
     */
    contentTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom opposite item template.
     * @group Templates
     */
    oppositeTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom marker template.
     * @group Templates
     */
    markerTemplate: Nullable<TemplateRef<any>>;
    templates: Nullable<QueryList<any>>;
    _contentTemplate: TemplateRef<any> | undefined;
    _oppositeTemplate: TemplateRef<any> | undefined;
    _markerTemplate: TemplateRef<any> | undefined;
    _componentStyle: TimelineStyle;
    getBlockableElement(): HTMLElement;
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Timeline, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Timeline, "p-timeline", never, { "value": { "alias": "value"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "align": { "alias": "align"; "required": false; }; "layout": { "alias": "layout"; "required": false; }; }, {}, ["contentTemplate", "oppositeTemplate", "markerTemplate", "templates"], never, true, never>;
}
declare class TimelineModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TimelineModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TimelineModule, never, [typeof Timeline, typeof i1.SharedModule], [typeof Timeline, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TimelineModule>;
}

/**
 * Defines valid templates in Timeline.
 * @group Templates
 */
interface TimelineTemplates {
    /**
     * Custom content template.
     * @param {Object} context - item data.
     */
    content(context: {
        /**
         * Item instance.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom opposite item template.
     * @param {Object} context - item data.
     */
    opposite(context: {
        /**
         * Item instance.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom marker template.
     * @param {Object} context - item data.
     */
    marker(context: {
        /**
         * Item instance.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
}

export { Timeline, TimelineClasses, TimelineModule, TimelineStyle };
export type { TimelineTemplates };
