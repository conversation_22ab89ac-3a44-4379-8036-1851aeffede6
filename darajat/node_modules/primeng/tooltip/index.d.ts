import * as i0 from '@angular/core';
import { AfterViewInit, OnDestroy, NgZone, TemplateRef, ViewContainerRef, SimpleChanges } from '@angular/core';
import { TooltipOptions } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Nullable } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Tooltip directive provides advisory information for a component.
 *
 * [Live Demo](https://www.primeng.org/tooltip)
 *
 * @module tooltipstyle
 *
 */
declare enum TooltipClasses {
    /**
     * Class name of the root element
     */
    root = "p-tooltip",
    /**
     * Class name of the arrow element
     */
    arrow = "p-tooltip-arrow",
    /**
     * Class name of the text element
     */
    text = "p-tooltip-text"
}
declare class TooltipStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: string;
        arrow: string;
        text: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TooltipStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TooltipStyle>;
}
interface TooltipStyle extends BaseStyle {
}

/**
 * Tooltip directive provides advisory information for a component.
 * @group Components
 */
declare class Tooltip extends BaseComponent implements AfterViewInit, OnDestroy {
    zone: NgZone;
    private viewContainer;
    /**
     * Position of the tooltip.
     * @group Props
     */
    tooltipPosition: 'right' | 'left' | 'top' | 'bottom' | string | undefined;
    /**
     * Event to show the tooltip.
     * @group Props
     */
    tooltipEvent: 'hover' | 'focus' | 'both' | string | any;
    /**
     * Type of CSS position.
     * @group Props
     */
    positionStyle: string | undefined;
    /**
     * Style class of the tooltip.
     * @group Props
     */
    tooltipStyleClass: string | undefined;
    /**
     * Whether the z-index should be managed automatically to always go on top or have a fixed value.
     * @group Props
     */
    tooltipZIndex: string | undefined;
    /**
     * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.
     * @group Props
     */
    escape: boolean;
    /**
     * Delay to show the tooltip in milliseconds.
     * @group Props
     */
    showDelay: number | undefined;
    /**
     * Delay to hide the tooltip in milliseconds.
     * @group Props
     */
    hideDelay: number | undefined;
    /**
     * Time to wait in milliseconds to hide the tooltip even it is active.
     * @group Props
     */
    life: number | undefined;
    /**
     * Specifies the additional vertical offset of the tooltip from its default position.
     * @group Props
     */
    positionTop: number | undefined;
    /**
     * Specifies the additional horizontal offset of the tooltip from its default position.
     * @group Props
     */
    positionLeft: number | undefined;
    /**
     * Whether to hide tooltip when hovering over tooltip content.
     * @group Props
     */
    autoHide: boolean;
    /**
     * Automatically adjusts the element position when there is not enough space on the selected position.
     * @group Props
     */
    fitContent: boolean;
    /**
     * Whether to hide tooltip on escape key press.
     * @group Props
     */
    hideOnEscape: boolean;
    /**
     * Content of the tooltip.
     * @group Props
     */
    content: string | TemplateRef<HTMLElement> | undefined;
    /**
     * When present, it specifies that the component should be disabled.
     * @defaultValue false
     * @group Props
     */
    get disabled(): boolean;
    set disabled(val: boolean);
    /**
     * Specifies the tooltip configuration options for the component.
     * @group Props
     */
    tooltipOptions: TooltipOptions | undefined;
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    $appendTo: i0.Signal<any>;
    _tooltipOptions: {
        tooltipLabel: any;
        tooltipPosition: string;
        tooltipEvent: string;
        appendTo: string;
        positionStyle: any;
        tooltipStyleClass: any;
        tooltipZIndex: string;
        escape: boolean;
        disabled: any;
        showDelay: any;
        hideDelay: any;
        positionTop: any;
        positionLeft: any;
        life: any;
        autoHide: boolean;
        hideOnEscape: boolean;
        id: string;
    };
    _disabled: boolean | undefined;
    container: any;
    styleClass: string | undefined;
    tooltipText: any;
    showTimeout: any;
    hideTimeout: any;
    active: boolean | undefined;
    mouseEnterListener: Nullable<Function>;
    mouseLeaveListener: Nullable<Function>;
    containerMouseleaveListener: Nullable<Function>;
    clickListener: Nullable<Function>;
    focusListener: Nullable<Function>;
    blurListener: Nullable<Function>;
    documentEscapeListener: Nullable<Function>;
    scrollHandler: any;
    resizeListener: any;
    _componentStyle: TooltipStyle;
    interactionInProgress: boolean;
    constructor(zone: NgZone, viewContainer: ViewContainerRef);
    ngAfterViewInit(): void;
    ngOnChanges(simpleChange: SimpleChanges): void;
    isAutoHide(): boolean;
    onMouseEnter(e: Event): void;
    onMouseLeave(e: MouseEvent): void;
    onFocus(e: Event): void;
    onBlur(e: Event): void;
    onInputClick(e: Event): void;
    activate(): void;
    deactivate(): void;
    create(): void;
    bindContainerMouseleaveListener(): void;
    unbindContainerMouseleaveListener(): void;
    show(): void;
    hide(): void;
    updateText(): void;
    align(): void;
    getHostOffset(): {
        left: any;
        top: any;
    };
    private get activeElement();
    alignRight(): void;
    alignLeft(): void;
    alignTop(): void;
    getArrowElement(): any;
    alignBottom(): void;
    alignTooltip(offsetLeft: any, offsetTop: any): void;
    setOption(option: any): void;
    getOption(option: string): any;
    getTarget(el: Element): Element;
    preAlign(position: string): void;
    isOutOfBounds(): boolean;
    onWindowResize(e: Event): void;
    bindDocumentResizeListener(): void;
    unbindDocumentResizeListener(): void;
    bindScrollListener(): void;
    unbindScrollListener(): void;
    unbindEvents(): void;
    remove(): void;
    clearShowTimeout(): void;
    clearHideTimeout(): void;
    clearTimeouts(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Tooltip, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<Tooltip, "[pTooltip]", never, { "tooltipPosition": { "alias": "tooltipPosition"; "required": false; }; "tooltipEvent": { "alias": "tooltipEvent"; "required": false; }; "positionStyle": { "alias": "positionStyle"; "required": false; }; "tooltipStyleClass": { "alias": "tooltipStyleClass"; "required": false; }; "tooltipZIndex": { "alias": "tooltipZIndex"; "required": false; }; "escape": { "alias": "escape"; "required": false; }; "showDelay": { "alias": "showDelay"; "required": false; }; "hideDelay": { "alias": "hideDelay"; "required": false; }; "life": { "alias": "life"; "required": false; }; "positionTop": { "alias": "positionTop"; "required": false; }; "positionLeft": { "alias": "positionLeft"; "required": false; }; "autoHide": { "alias": "autoHide"; "required": false; }; "fitContent": { "alias": "fitContent"; "required": false; }; "hideOnEscape": { "alias": "hideOnEscape"; "required": false; }; "content": { "alias": "pTooltip"; "required": false; }; "disabled": { "alias": "tooltipDisabled"; "required": false; }; "tooltipOptions": { "alias": "tooltipOptions"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_escape: unknown;
    static ngAcceptInputType_showDelay: unknown;
    static ngAcceptInputType_hideDelay: unknown;
    static ngAcceptInputType_life: unknown;
    static ngAcceptInputType_positionTop: unknown;
    static ngAcceptInputType_positionLeft: unknown;
    static ngAcceptInputType_autoHide: unknown;
    static ngAcceptInputType_fitContent: unknown;
    static ngAcceptInputType_hideOnEscape: unknown;
}
declare class TooltipModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TooltipModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TooltipModule, never, [typeof Tooltip], [typeof Tooltip]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TooltipModule>;
}

export { Tooltip, TooltipClasses, TooltipModule, TooltipStyle };
