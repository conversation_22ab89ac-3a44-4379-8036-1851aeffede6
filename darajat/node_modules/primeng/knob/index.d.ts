import * as i0 from '@angular/core';
import { EventEmitter } from '@angular/core';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { VoidListener } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';
import * as i1 from 'primeng/api';

/**
 *
 * Knob is a form component to define number inputs with a dial.
 *
 * [Live Demo](https://www.primeng.org/knob/)
 *
 * @module knobstyle
 *
 */
declare enum KnobClasses {
    /**
     * Class name of the root element
     */
    root = "p-knob",
    /**
     * Class name of the range element
     */
    range = "p-knob-range",
    /**
     * Class name of the value element
     */
    value = "p-knob-value",
    /**
     * Class name of the text element
     */
    text = "p-knob-text"
}
declare class KnobStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        range: string;
        value: string;
        text: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<KnobStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<KnobStyle>;
}
interface KnobStyle extends BaseStyle {
}

declare const KNOB_VALUE_ACCESSOR: any;
/**
 * Knob is a form component to define number inputs with a dial.
 * @group Components
 */
declare class Knob extends BaseEditableHolder {
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Specifies one or more IDs in the DOM that labels the input field.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number;
    /**
     * Background of the value.
     * @group Props
     */
    valueColor: string;
    /**
     * Background color of the range.
     * @group Props
     */
    rangeColor: string;
    /**
     * Color of the value text.
     * @group Props
     */
    textColor: string;
    /**
     * Template string of the value.
     * @group Props
     */
    valueTemplate: string;
    /**
     * Size of the component in pixels.
     * @group Props
     */
    size: number;
    /**
     * Mininum boundary value.
     * @group Props
     */
    min: number;
    /**
     * Maximum boundary value.
     * @group Props
     */
    max: number;
    /**
     * Step factor to increment/decrement the value.
     * @group Props
     */
    step: number;
    /**
     * Width of the knob stroke.
     * @group Props
     */
    strokeWidth: number;
    /**
     * Whether the show the value inside the knob.
     * @group Props
     */
    showValue: boolean;
    /**
     * When present, it specifies that the component value cannot be edited.
     * @group Props
     */
    readonly: boolean;
    /**
     * Callback to invoke on value change.
     * @param {number} value - New value.
     * @group Emits
     */
    onChange: EventEmitter<number>;
    radius: number;
    midX: number;
    midY: number;
    minRadians: number;
    maxRadians: number;
    value: number;
    windowMouseMoveListener: VoidListener;
    windowMouseUpListener: VoidListener;
    windowTouchMoveListener: VoidListener;
    windowTouchEndListener: VoidListener;
    _componentStyle: KnobStyle;
    mapRange(x: number, inMin: number, inMax: number, outMin: number, outMax: number): number;
    onClick(event: MouseEvent): void;
    updateValue(offsetX: number, offsetY: number): void;
    updateModel(angle: number, start: number): void;
    onMouseDown(event: MouseEvent): void;
    onMouseUp(event: MouseEvent): void;
    onTouchStart(event: TouchEvent): void;
    onTouchEnd(event: TouchEvent): void;
    onMouseMove(event: MouseEvent): void;
    onTouchMove(event: Event): void;
    updateModelValue(newValue: any): void;
    onKeyDown(event: KeyboardEvent): void;
    rangePath(): string;
    valuePath(): string;
    zeroRadians(): number;
    valueRadians(): number;
    minX(): number;
    minY(): number;
    maxX(): number;
    maxY(): number;
    zeroX(): number;
    zeroY(): number;
    valueX(): number;
    valueY(): number;
    largeArc(): 0 | 1;
    sweep(): 0 | 1;
    valueToDisplay(): string;
    get _value(): number;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Knob, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Knob, "p-knob", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "valueColor": { "alias": "valueColor"; "required": false; }; "rangeColor": { "alias": "rangeColor"; "required": false; }; "textColor": { "alias": "textColor"; "required": false; }; "valueTemplate": { "alias": "valueTemplate"; "required": false; }; "size": { "alias": "size"; "required": false; }; "min": { "alias": "min"; "required": false; }; "max": { "alias": "max"; "required": false; }; "step": { "alias": "step"; "required": false; }; "strokeWidth": { "alias": "strokeWidth"; "required": false; }; "showValue": { "alias": "showValue"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; }, { "onChange": "onChange"; }, never, never, true, never>;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_size: unknown;
    static ngAcceptInputType_min: unknown;
    static ngAcceptInputType_max: unknown;
    static ngAcceptInputType_step: unknown;
    static ngAcceptInputType_strokeWidth: unknown;
    static ngAcceptInputType_showValue: unknown;
    static ngAcceptInputType_readonly: unknown;
}
declare class KnobModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<KnobModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<KnobModule, never, [typeof Knob, typeof i1.SharedModule], [typeof Knob, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<KnobModule>;
}

export { KNOB_VALUE_ACCESSOR, Knob, KnobClasses, KnobModule, KnobStyle };
