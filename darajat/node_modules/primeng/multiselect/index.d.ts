import * as _angular_core from '@angular/core';
import { TemplateRef, EventEmitter, OnInit, AfterViewInit, AfterContentInit, AfterViewChecked, Signal, ElementRef, QueryList, NgZone } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import * as i1 from 'primeng/api';
import { ScrollerOptions, FilterService, OverlayService, OverlayOptions, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { Checkbox } from 'primeng/checkbox';
import { Fluid } from 'primeng/fluid';
import { Overlay } from 'primeng/overlay';
import { Scroller } from 'primeng/scroller';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 * Callbacks to invoke on filter or reset.
 * @group Interface
 */
interface MultiSelectFilterOptions {
    filter?: (value?: any) => void;
    reset?: () => void;
}
/**
 * Custom change event.
 * @see {@link MultiSelect.onChange}
 * @group Events
 */
interface MultiSelectChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Current selected values.
     */
    value: any;
    /**
     * Toggled item value.
     */
    itemValue?: any;
}
/**
 * Custom change event.
 * @see {@link MultiSelect.onSelectAllChange}
 * @group Events
 */
interface MultiSelectSelectAllChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Boolean value indicates whether all data is selected.
     */
    checked: boolean;
}
/**
 * Custom filter event.
 * @see {@link MultiSelect.onFilter}
 * @group Events
 */
interface MultiSelectFilterEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Filter value.
     */
    filter: any;
}
/**
 * Custom focus event.
 * @see {@link MultiSelect.onFocus}
 * @group Events
 */
interface MultiSelectFocusEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
}
/**
 * Custom blur event.
 * @see {@link MultiSelect.onBlur}
 * @extends {MultiSelectFocusEvent}
 * @group Events
 */
interface MultiSelectBlurEvent extends MultiSelectFocusEvent {
}
/**
 * Custom lazy load event.
 * @see {@link MultiSelect.onLazyLoad}
 * @group Events
 */
interface MultiSelectLazyLoadEvent {
    /**
     * Index of the first element in viewport.
     */
    first: number;
    /**
     * Index of the last element in viewport.
     */
    last: number;
}
/**
 * Custom remove event.
 * @see {@link MultiSelect.onRemove}
 * @group Events
 */
interface MultiSelectRemoveEvent {
    /**
     * Value after the item removed.
     */
    newValue: object;
    /**
     * Removed value.
     */
    removed: MultiSelectItem;
}
/**
 * Defines valid templates in MultiSelect.
 * @group Templates
 */
interface MultiSelectTemplates {
    /**
     * Custom header template.
     * @param {Object} context - item data.
     */
    item(context: {
        /**
         * Data of the option.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom selected item template.
     * @param {Object} context - selected items data.
     */
    selecteditems(context: {
        /**
         * Selected option value.
         */
        $implicit: any;
        /**
         * Function to remove chip.
         */
        removeChip: (chip: MultiSelectItem, event: MouseEvent) => void;
    }): TemplateRef<{
        $implicit: any;
        removeChip: Function;
    }>;
    /**
     * Custom header template.
     */
    header(): TemplateRef<any>;
    /**
     * Custom filter template.
     * @param {MultiSelectFilterOptions} options - filter options.
     */
    filter(context: {
        /**
         * Filter options.
         */
        options: MultiSelectFilterOptions;
    }): TemplateRef<{
        options: MultiSelectFilterOptions;
    }>;
    /**
     * Custom footer template.
     */
    footer(): TemplateRef<any>;
    /**
     * Custom empty filter template.
     */
    emptyfilter(): TemplateRef<any>;
    /**
     * Custom empty template.
     */
    empty(): TemplateRef<any>;
    /**
     * Custom group template.
     */
    group(context: {
        /**
         * Data of the item.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom loader template. This template can be used with virtualScroll.
     * @param {ScrollerOptions} options - virtual scroller options.
     */
    loader(context: {
        /**
         * Virtual scroller options.
         */
        options: ScrollerOptions;
    }): TemplateRef<{
        options: ScrollerOptions;
    }>;
    /**
     * Custom dropdown trigger icon template.
     */
    dropdownicon(): TemplateRef<any>;
    /**
     * Custom clear icon template.
     */
    clearicon(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filtericon(): TemplateRef<any>;
    /**
     * Custom check icon template.
     * @deprecated Use headercheckboxicon or itemcheckboxicon instead.
     */
    checkicon(): TemplateRef<any>;
    /**
     * Custom check icon template for the header checkbox.
     */
    headercheckboxicon(context: {
        /**
         * Defines if all items are selected.
         */
        $implicit: boolean;
        /**
         * Defines if items are partially selected.
         */
        partialSelected: boolean;
    }): TemplateRef<{
        $implicit: boolean;
        partialSelected: boolean;
    }>;
    /**
     * Custom check icon template for the item checkbox.
     */
    itemcheckboxicon(context: {
        /**
         * Selection status of the item.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom close icon template.
     */
    closeicon(): TemplateRef<any>;
    /**
     * Custom remove token icon template.
     */
    removetokenicon(): TemplateRef<any>;
}

/**
 *
 * MultiSelect is used to select multiple items from a collection.
 *
 * [Live Demo](https://www.primeng.org/multiselect/)
 *
 * @module multiselectstyle
 *
 */
declare enum MultiSelectClasses {
    /**
     * Class name of the root element
     */
    root = "p-multiselect",
    /**
     * Class name of the label container element
     */
    labelContainer = "p-multiselect-label-container",
    /**
     * Class name of the label element
     */
    label = "p-multiselect-label",
    /**
     * Class name of the chip item element
     */
    chipItem = "p-multiselect-chip-item",
    /**
     * Class name of the chip element
     */
    pcChip = "p-multiselect-chip",
    /**
     * Class name of the chip icon element
     */
    chipIcon = "p-multiselect-chip-icon",
    /**
     * Class name of the dropdown element
     */
    dropdown = "p-multiselect-dropdown",
    /**
     * Class name of the loading icon element
     */
    loadingIcon = "p-multiselect-loading-icon",
    /**
     * Class name of the dropdown icon element
     */
    dropdownIcon = "p-multiselect-dropdown-icon",
    /**
     * Class name of the overlay element
     */
    overlay = "p-multiselect-overlay",
    /**
     * Class name of the header element
     */
    header = "p-multiselect-header",
    /**
     * Class name of the filter container element
     */
    pcFilterContainer = "p-multiselect-filter-container",
    /**
     * Class name of the filter element
     */
    pcFilter = "p-multiselect-filter",
    /**
     * Class name of the list container element
     */
    listContainer = "p-multiselect-list-container",
    /**
     * Class name of the list element
     */
    list = "p-multiselect-list",
    /**
     * Class name of the option group element
     */
    optionGroup = "p-multiselect-option-group",
    /**
     * Class name of the option element
     */
    option = "p-multiselect-option",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-multiselect-empty-message",
    /**
     * Class name of the clear icon
     */
    clearIcon = "p-autocomplete-clear-icon"
}
declare class MultiSelectStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-multiselect p-component p-inputwrapper': boolean;
            'p-multiselect-display-chip': boolean;
            'p-disabled': any;
            'p-invalid': any;
            'p-variant-filled': any;
            'p-focus': any;
            'p-inputwrapper-filled': any;
            'p-inputwrapper-focus': any;
            'p-multiselect-open': any;
            'p-multiselect-fluid': any;
            'p-multiselect-sm p-inputfield-sm': boolean;
            'p-multiselect-lg p-inputfield-lg': boolean;
        })[];
        labelContainer: string;
        label: ({ instance }: {
            instance: any;
        }) => {
            'p-multiselect-label': boolean;
            'p-placeholder': boolean;
            'p-multiselect-label-empty': boolean;
        };
        chipItem: string;
        pcChip: string;
        chipIcon: string;
        dropdown: string;
        loadingIcon: string;
        dropdownIcon: string;
        overlay: string;
        header: string;
        pcFilterContainer: string;
        pcFilter: string;
        listContainer: string;
        list: string;
        optionGroup: string;
        option: ({ instance }: {
            instance: any;
        }) => {
            'p-multiselect-option': boolean;
            'p-multiselect-option-selected': any;
            'p-disabled': any;
            'p-focus': any;
        };
        emptyMessage: string;
        clearIcon: string;
    };
    inlineStyles: {
        root: ({ instance }: {
            instance: any;
        }) => {
            position: string;
        };
    };
    static ɵfac: _angular_core.ɵɵFactoryDeclaration<MultiSelectStyle, never>;
    static ɵprov: _angular_core.ɵɵInjectableDeclaration<MultiSelectStyle>;
}
interface MultiSelectStyle extends BaseStyle {
}

declare const MULTISELECT_VALUE_ACCESSOR: any;
declare class MultiSelectItem extends BaseComponent {
    id: string | undefined;
    option: any;
    selected: boolean | undefined;
    label: string | undefined;
    disabled: boolean | undefined;
    itemSize: number | undefined;
    focused: boolean | undefined;
    ariaPosInset: string | undefined;
    ariaSetSize: string | undefined;
    variant: 'outlined' | 'filled';
    template: TemplateRef<any> | undefined;
    checkIconTemplate: TemplateRef<any> | undefined;
    itemCheckboxIconTemplate: TemplateRef<any> | undefined;
    highlightOnSelect: boolean | undefined;
    onClick: EventEmitter<any>;
    onMouseEnter: EventEmitter<any>;
    _componentStyle: MultiSelectStyle;
    onOptionClick(event: Event): void;
    onOptionMouseEnter(event: Event): void;
    static ɵfac: _angular_core.ɵɵFactoryDeclaration<MultiSelectItem, never>;
    static ɵcmp: _angular_core.ɵɵComponentDeclaration<MultiSelectItem, "p-multiSelectItem, p-multiselect-item", never, { "id": { "alias": "id"; "required": false; }; "option": { "alias": "option"; "required": false; }; "selected": { "alias": "selected"; "required": false; }; "label": { "alias": "label"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "itemSize": { "alias": "itemSize"; "required": false; }; "focused": { "alias": "focused"; "required": false; }; "ariaPosInset": { "alias": "ariaPosInset"; "required": false; }; "ariaSetSize": { "alias": "ariaSetSize"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; "template": { "alias": "template"; "required": false; }; "checkIconTemplate": { "alias": "checkIconTemplate"; "required": false; }; "itemCheckboxIconTemplate": { "alias": "itemCheckboxIconTemplate"; "required": false; }; "highlightOnSelect": { "alias": "highlightOnSelect"; "required": false; }; }, { "onClick": "onClick"; "onMouseEnter": "onMouseEnter"; }, never, never, true, never>;
    static ngAcceptInputType_selected: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_itemSize: unknown;
    static ngAcceptInputType_focused: unknown;
    static ngAcceptInputType_highlightOnSelect: unknown;
}
/**
 * MultiSelect is used to select multiple items from a collection.
 * @group Components
 */
declare class MultiSelect extends BaseEditableHolder implements OnInit, AfterViewInit, AfterContentInit, AfterViewChecked {
    private zone;
    filterService: FilterService;
    overlayService: OverlayService;
    /**
     * Unique identifier of the component
     * @group Props
     */
    id: string | undefined;
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Style class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the overlay panel.
     * @group Props
     */
    panelStyle: any;
    /**
     * Style class of the overlay panel element.
     * @group Props
     */
    panelStyleClass: string | undefined;
    /**
     * Identifier of the focus input to match a label defined for the component.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * When present, it specifies that the component cannot be edited.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * Whether to display options as grouped when nested options are provided.
     * @group Props
     */
    group: boolean | undefined;
    /**
     * When specified, displays an input field to filter the items on keyup.
     * @group Props
     */
    filter: boolean;
    /**
     * Defines placeholder of the filter input.
     * @group Props
     */
    filterPlaceHolder: string | undefined;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Specifies the visibility of the options panel.
     * @group Props
     */
    overlayVisible: boolean | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * A property to uniquely identify a value in options.
     * @group Props
     */
    dataKey: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Whether to show labels of selected item labels or use default label.
     * @group Props
     * @defaultValue true
     */
    set displaySelectedLabel(val: boolean);
    get displaySelectedLabel(): boolean;
    /**
     * Decides how many selected item labels to show at most.
     * @group Props
     * @defaultValue 3
     */
    set maxSelectedLabels(val: number | null | undefined);
    get maxSelectedLabels(): number | null | undefined;
    /**
     * Maximum number of selectable items.
     * @group Props
     */
    selectionLimit: number | undefined;
    /**
     * Label to display after exceeding max selected labels e.g. ({0} items selected), defaults "ellipsis" keyword to indicate a text-overflow.
     * @group Props
     */
    selectedItemsLabel: string | undefined;
    /**
     * Whether to show the checkbox at header to toggle all items at once.
     * @group Props
     */
    showToggleAll: boolean;
    /**
     * Text to display when filtering does not return any results.
     * @group Props
     */
    emptyFilterMessage: string;
    /**
     * Text to display when there is no data. Defaults to global value in i18n translation configuration.
     * @group Props
     */
    emptyMessage: string;
    /**
     * Clears the filter value when hiding the dropdown.
     * @group Props
     */
    resetFilterOnHide: boolean;
    /**
     * Icon class of the dropdown icon.
     * @group Props
     */
    dropdownIcon: string | undefined;
    /**
     * Icon class of the chip icon.
     * @group Props
     */
    chipIcon: string | undefined;
    /**
     * Name of the label field of an option.
     * @group Props
     */
    optionLabel: string | undefined;
    /**
     * Name of the value field of an option.
     * @group Props
     */
    optionValue: string | undefined;
    /**
     * Name of the disabled field of an option.
     * @group Props
     */
    optionDisabled: string | undefined;
    /**
     * Name of the label field of an option group.
     * @group Props
     */
    optionGroupLabel: string | undefined;
    /**
     * Name of the options field of an option group.
     * @group Props
     */
    optionGroupChildren: string;
    /**
     * Whether to show the header.
     * @group Props
     */
    showHeader: boolean;
    /**
     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.
     * @group Props
     */
    filterBy: string | undefined;
    /**
     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    lazy: boolean;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Whether the multiselect is in loading state.
     * @group Props
     */
    loading: boolean | undefined;
    /**
     * Height of an item in the list for VirtualScrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Icon to display in loading state.
     * @group Props
     */
    loadingIcon: string | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.
     * @group Props
     */
    overlayOptions: OverlayOptions | undefined;
    /**
     * Defines a string that labels the filter input.
     * @group Props
     */
    ariaFilterLabel: string | undefined;
    /**
     * Defines how the items are filtered.
     * @group Props
     */
    filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte';
    /**
     * Advisory information to display in a tooltip on hover.
     * @group Props
     */
    tooltip: string;
    /**
     * Position of the tooltip.
     * @group Props
     */
    tooltipPosition: 'top' | 'left' | 'right' | 'bottom';
    /**
     * Type of CSS position.
     * @group Props
     */
    tooltipPositionStyle: string;
    /**
     * Style class of the tooltip.
     * @group Props
     */
    tooltipStyleClass: string | undefined;
    /**
     * Applies focus to the filter element when the overlay is shown.
     * @group Props
     */
    autofocusFilter: boolean;
    /**
     * Defines how the selected items are displayed.
     * @group Props
     */
    display: string | 'comma' | 'chip';
    /**
     * Defines the autocomplete is active.
     * @group Props
     */
    autocomplete: string;
    /**
     * When enabled, a clear icon is displayed to clear the value.
     * @group Props
     */
    showClear: boolean;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Label to display when there are no selections.
     * @group Props
     */
    set placeholder(val: string | undefined);
    get placeholder(): Signal<string | undefined>;
    /**
     * An array of objects to display as the available options.
     * @group Props
     */
    get options(): any[] | undefined;
    set options(val: any[] | undefined);
    /**
     * When specified, filter displays with this value.
     * @group Props
     */
    get filterValue(): string | undefined | null;
    set filterValue(val: string | undefined | null);
    /**
     * Whether all data is selected.
     * @group Props
     */
    get selectAll(): boolean | undefined | null;
    set selectAll(value: boolean | undefined | null);
    /**
     * Indicates whether to focus on options when hovering over them, defaults to optionLabel.
     * @group Props
     */
    focusOnHover: boolean;
    /**
     * Fields used when filtering the options, defaults to optionLabel.
     * @group Props
     */
    filterFields: any[] | undefined;
    /**
     * Determines if the option will be selected on focus.
     * @group Props
     */
    selectOnFocus: boolean;
    /**
     * Whether to focus on the first visible or selected element when the overlay panel is shown.
     * @group Props
     */
    autoOptionFocus: boolean;
    /**
     * Whether the selected option will be add highlight class.
     * @group Props
     */
    highlightOnSelect: boolean;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: _angular_core.InputSignal<"small" | "large">;
    /**
     * Specifies the input variant of the component.
     * @defaultValue undefined
     * @group Props
     */
    variant: _angular_core.InputSignal<"outlined" | "filled">;
    /**
     * Spans 100% width of the container when enabled.
     * @defaultValue undefined
     * @group Props
     */
    fluid: _angular_core.InputSignalWithTransform<boolean, unknown>;
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: _angular_core.InputSignal<any>;
    /**
     * Callback to invoke when value changes.
     * @param {MultiSelectChangeEvent} event - Custom change event.
     * @group Emits
     */
    onChange: EventEmitter<MultiSelectChangeEvent>;
    /**
     * Callback to invoke when data is filtered.
     * @param {MultiSelectFilterEvent} event - Custom filter event.
     * @group Emits
     */
    onFilter: EventEmitter<MultiSelectFilterEvent>;
    /**
     * Callback to invoke when multiselect receives focus.
     * @param {MultiSelectFocusEvent} event - Custom focus event.
     * @group Emits
     */
    onFocus: EventEmitter<MultiSelectFocusEvent>;
    /**
     * Callback to invoke when multiselect loses focus.
     * @param {MultiSelectBlurEvent} event - Custom blur event.
     * @group Emits
     */
    onBlur: EventEmitter<MultiSelectBlurEvent>;
    /**
     * Callback to invoke when component is clicked.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onClick: EventEmitter<Event>;
    /**
     * Callback to invoke when input field is cleared.
     * @group Emits
     */
    onClear: EventEmitter<void>;
    /**
     * Callback to invoke when overlay panel becomes visible.
     * @param {AnimationEvent} event - Animation event.
     * @group Emits
     */
    onPanelShow: EventEmitter<AnimationEvent>;
    /**
     * Callback to invoke when overlay panel becomes hidden.
     * @param {AnimationEvent} event - Animation event.
     * @group Emits
     */
    onPanelHide: EventEmitter<AnimationEvent>;
    /**
     * Callback to invoke in lazy mode to load new data.
     * @param {MultiSelectLazyLoadEvent} event - Lazy load event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<MultiSelectLazyLoadEvent>;
    /**
     * Callback to invoke in lazy mode to load new data.
     * @param {MultiSelectRemoveEvent} event - Remove event.
     * @group Emits
     */
    onRemove: EventEmitter<MultiSelectRemoveEvent>;
    /**
     * Callback to invoke when all data is selected.
     * @param {MultiSelectSelectAllChangeEvent} event - Custom select event.
     * @group Emits
     */
    onSelectAllChange: EventEmitter<MultiSelectSelectAllChangeEvent>;
    overlayViewChild: Nullable<Overlay>;
    filterInputChild: Nullable<ElementRef>;
    focusInputViewChild: Nullable<ElementRef>;
    itemsViewChild: Nullable<ElementRef>;
    scroller: Nullable<Scroller>;
    lastHiddenFocusableElementOnOverlay: Nullable<ElementRef>;
    firstHiddenFocusableElementOnOverlay: Nullable<ElementRef>;
    headerCheckboxViewChild: Nullable<Checkbox>;
    footerFacet: any;
    headerFacet: any;
    _componentStyle: MultiSelectStyle;
    searchValue: Nullable<string>;
    searchTimeout: any;
    _selectAll: boolean | undefined | null;
    _placeholder: _angular_core.WritableSignal<string>;
    _disableTooltip: boolean;
    value: any[];
    _filteredOptions: any[] | undefined | null;
    focus: boolean | undefined;
    filtered: boolean | undefined;
    itemTemplate: TemplateRef<any> | undefined;
    groupTemplate: TemplateRef<any> | undefined;
    loaderTemplate: TemplateRef<any> | undefined;
    headerTemplate: TemplateRef<any> | undefined;
    filterTemplate: TemplateRef<any> | undefined;
    footerTemplate: TemplateRef<any> | undefined;
    emptyFilterTemplate: TemplateRef<any> | undefined;
    emptyTemplate: TemplateRef<any> | undefined;
    selectedItemsTemplate: TemplateRef<any> | undefined;
    loadingIconTemplate: TemplateRef<any> | undefined;
    filterIconTemplate: TemplateRef<any> | undefined;
    removeTokenIconTemplate: TemplateRef<any> | undefined;
    chipIconTemplate: TemplateRef<any> | undefined;
    clearIconTemplate: TemplateRef<any> | undefined;
    dropdownIconTemplate: TemplateRef<any> | undefined;
    itemCheckboxIconTemplate: TemplateRef<any> | undefined;
    headerCheckboxIconTemplate: TemplateRef<any> | undefined;
    templates: Nullable<QueryList<PrimeTemplate>>;
    _itemTemplate: TemplateRef<any> | undefined;
    _groupTemplate: TemplateRef<any> | undefined;
    _loaderTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _filterTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _emptyFilterTemplate: TemplateRef<any> | undefined;
    _emptyTemplate: TemplateRef<any> | undefined;
    _selectedItemsTemplate: TemplateRef<any> | undefined;
    _loadingIconTemplate: TemplateRef<any> | undefined;
    _filterIconTemplate: TemplateRef<any> | undefined;
    _removeTokenIconTemplate: TemplateRef<any> | undefined;
    _chipIconTemplate: TemplateRef<any> | undefined;
    _clearIconTemplate: TemplateRef<any> | undefined;
    _dropdownIconTemplate: TemplateRef<any> | undefined;
    _itemCheckboxIconTemplate: TemplateRef<any> | undefined;
    _headerCheckboxIconTemplate: TemplateRef<any> | undefined;
    $variant: Signal<"outlined" | "filled">;
    $appendTo: Signal<any>;
    pcFluid: Fluid;
    get hasFluid(): boolean;
    ngAfterContentInit(): void;
    headerCheckboxFocus: boolean | undefined;
    filterOptions: MultiSelectFilterOptions | undefined;
    preventModelTouched: boolean | undefined;
    focused: boolean;
    itemsWrapper: any;
    _displaySelectedLabel: boolean;
    _maxSelectedLabels: number;
    modelValue: _angular_core.WritableSignal<any>;
    _filterValue: _angular_core.WritableSignal<any>;
    _options: _angular_core.WritableSignal<any[]>;
    startRangeIndex: _angular_core.WritableSignal<number>;
    focusedOptionIndex: _angular_core.WritableSignal<number>;
    selectedOptions: any;
    clickInProgress: boolean;
    get emptyMessageLabel(): string;
    get emptyFilterMessageLabel(): string;
    get isVisibleClearIcon(): boolean | undefined;
    get toggleAllAriaLabel(): string;
    get listLabel(): string;
    private getAllVisibleAndNonVisibleOptions;
    visibleOptions: Signal<any>;
    label: Signal<any>;
    chipSelectedItems: Signal<any>;
    constructor(zone: NgZone, filterService: FilterService, overlayService: OverlayService);
    ngOnInit(): void;
    maxSelectionLimitReached(): boolean;
    ngAfterViewInit(): void;
    ngAfterViewChecked(): void;
    flatOptions(options: any): any;
    autoUpdateModel(): void;
    /**
     * Updates the model value.
     * @group Method
     */
    updateModel(value: any, event?: any): void;
    onInputClick(event: any): void;
    onOptionSelect(event: any, isFocus?: boolean, index?: number): void;
    findSelectedOptionIndex(): any;
    onOptionSelectRange(event: any, start?: number, end?: number): void;
    searchFields(): string[];
    findNearestSelectedOptionIndex(index: any, firstCheckUp?: boolean): any;
    findPrevSelectedOptionIndex(index: any): number;
    findFirstFocusedOptionIndex(): any;
    findFirstOptionIndex(): any;
    findFirstSelectedOptionIndex(): any;
    findNextSelectedOptionIndex(index: any): any;
    equalityKey(): string;
    hasSelectedOption(): boolean;
    isValidSelectedOption(option: any): any;
    isOptionGroup(option: any): any;
    isValidOption(option: any): boolean;
    isOptionDisabled(option: any): any;
    isSelected(option: any): any;
    isOptionMatched(option: any): any;
    isEmpty(): boolean;
    getOptionIndex(index: any, scrollerOptions: any): any;
    getAriaPosInset(index: any): any;
    get ariaSetSize(): any;
    getLabelByValue(value: any): any;
    getSelectedItemsLabel(): any;
    getOptionLabel(option: any): any;
    getOptionValue(option: any): any;
    getOptionGroupLabel(optionGroup: any): any;
    getOptionGroupChildren(optionGroup: any): any;
    onKeyDown(event: KeyboardEvent): void;
    onFilterKeyDown(event: KeyboardEvent): void;
    onArrowLeftKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onArrowDownKey(event: any): void;
    onArrowUpKey(event: any, pressedInInputText?: boolean): void;
    onHomeKey(event: any, pressedInInputText?: boolean): void;
    onEndKey(event: any, pressedInInputText?: boolean): void;
    onPageDownKey(event: any): void;
    onPageUpKey(event: any): void;
    onEnterKey(event: any): void;
    onEscapeKey(event: any): void;
    onTabKey(event: any, pressedInInputText?: boolean): void;
    onShiftKey(): void;
    onContainerClick(event: any): void;
    onFirstHiddenFocus(event: any): void;
    onInputFocus(event: Event): void;
    onInputBlur(event: Event): void;
    onFilterInputChange(event: Event): void;
    onLastHiddenFocus(event: any): void;
    onOptionMouseEnter(event: any, index: any): void;
    onFilterBlur(event: any): void;
    onToggleAll(event: any): void;
    changeFocusedOptionIndex(event: any, index: any): void;
    get virtualScrollerDisabled(): boolean;
    scrollInView(index?: number): void;
    get focusedOptionId(): string;
    allSelected(): any;
    partialSelected(): boolean;
    /**
     * Displays the panel.
     * @group Method
     */
    show(isFocus?: any): void;
    /**
     * Hides the panel.
     * @group Method
     */
    hide(isFocus?: any): void;
    onOverlayAnimationStart(event: AnimationEvent): void;
    resetFilter(): void;
    close(event: Event): void;
    clear(event: Event): void;
    labelContainerMouseLeave(): void;
    removeOption(optionValue: any, event: any): void;
    findNextOptionIndex(index: any): any;
    findPrevOptionIndex(index: any): any;
    findLastSelectedOptionIndex(): number;
    findLastFocusedOptionIndex(): number;
    findLastOptionIndex(): number;
    searchOptions(event: any, char: any): boolean;
    hasFocusableElements(): boolean;
    hasFilter(): boolean;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: _angular_core.ɵɵFactoryDeclaration<MultiSelect, never>;
    static ɵcmp: _angular_core.ɵɵComponentDeclaration<MultiSelect, "p-multiSelect, p-multiselect, p-multi-select", never, { "id": { "alias": "id"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "panelStyle": { "alias": "panelStyle"; "required": false; }; "panelStyleClass": { "alias": "panelStyleClass"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "group": { "alias": "group"; "required": false; }; "filter": { "alias": "filter"; "required": false; }; "filterPlaceHolder": { "alias": "filterPlaceHolder"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "overlayVisible": { "alias": "overlayVisible"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "dataKey": { "alias": "dataKey"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "displaySelectedLabel": { "alias": "displaySelectedLabel"; "required": false; }; "maxSelectedLabels": { "alias": "maxSelectedLabels"; "required": false; }; "selectionLimit": { "alias": "selectionLimit"; "required": false; }; "selectedItemsLabel": { "alias": "selectedItemsLabel"; "required": false; }; "showToggleAll": { "alias": "showToggleAll"; "required": false; }; "emptyFilterMessage": { "alias": "emptyFilterMessage"; "required": false; }; "emptyMessage": { "alias": "emptyMessage"; "required": false; }; "resetFilterOnHide": { "alias": "resetFilterOnHide"; "required": false; }; "dropdownIcon": { "alias": "dropdownIcon"; "required": false; }; "chipIcon": { "alias": "chipIcon"; "required": false; }; "optionLabel": { "alias": "optionLabel"; "required": false; }; "optionValue": { "alias": "optionValue"; "required": false; }; "optionDisabled": { "alias": "optionDisabled"; "required": false; }; "optionGroupLabel": { "alias": "optionGroupLabel"; "required": false; }; "optionGroupChildren": { "alias": "optionGroupChildren"; "required": false; }; "showHeader": { "alias": "showHeader"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "loadingIcon": { "alias": "loadingIcon"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "overlayOptions": { "alias": "overlayOptions"; "required": false; }; "ariaFilterLabel": { "alias": "ariaFilterLabel"; "required": false; }; "filterMatchMode": { "alias": "filterMatchMode"; "required": false; }; "tooltip": { "alias": "tooltip"; "required": false; }; "tooltipPosition": { "alias": "tooltipPosition"; "required": false; }; "tooltipPositionStyle": { "alias": "tooltipPositionStyle"; "required": false; }; "tooltipStyleClass": { "alias": "tooltipStyleClass"; "required": false; }; "autofocusFilter": { "alias": "autofocusFilter"; "required": false; }; "display": { "alias": "display"; "required": false; }; "autocomplete": { "alias": "autocomplete"; "required": false; }; "showClear": { "alias": "showClear"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "options": { "alias": "options"; "required": false; }; "filterValue": { "alias": "filterValue"; "required": false; }; "selectAll": { "alias": "selectAll"; "required": false; }; "focusOnHover": { "alias": "focusOnHover"; "required": false; }; "filterFields": { "alias": "filterFields"; "required": false; }; "selectOnFocus": { "alias": "selectOnFocus"; "required": false; }; "autoOptionFocus": { "alias": "autoOptionFocus"; "required": false; }; "highlightOnSelect": { "alias": "highlightOnSelect"; "required": false; }; "size": { "alias": "size"; "required": false; "isSignal": true; }; "variant": { "alias": "variant"; "required": false; "isSignal": true; }; "fluid": { "alias": "fluid"; "required": false; "isSignal": true; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, { "onChange": "onChange"; "onFilter": "onFilter"; "onFocus": "onFocus"; "onBlur": "onBlur"; "onClick": "onClick"; "onClear": "onClear"; "onPanelShow": "onPanelShow"; "onPanelHide": "onPanelHide"; "onLazyLoad": "onLazyLoad"; "onRemove": "onRemove"; "onSelectAllChange": "onSelectAllChange"; }, ["footerFacet", "headerFacet", "itemTemplate", "groupTemplate", "loaderTemplate", "headerTemplate", "filterTemplate", "footerTemplate", "emptyFilterTemplate", "emptyTemplate", "selectedItemsTemplate", "loadingIconTemplate", "filterIconTemplate", "removeTokenIconTemplate", "chipIconTemplate", "clearIconTemplate", "dropdownIconTemplate", "itemCheckboxIconTemplate", "headerCheckboxIconTemplate", "templates"], ["p-header", "p-footer"], true, never>;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_group: unknown;
    static ngAcceptInputType_filter: unknown;
    static ngAcceptInputType_overlayVisible: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_selectionLimit: unknown;
    static ngAcceptInputType_showToggleAll: unknown;
    static ngAcceptInputType_resetFilterOnHide: unknown;
    static ngAcceptInputType_showHeader: unknown;
    static ngAcceptInputType_lazy: unknown;
    static ngAcceptInputType_virtualScroll: unknown;
    static ngAcceptInputType_loading: unknown;
    static ngAcceptInputType_virtualScrollItemSize: unknown;
    static ngAcceptInputType_autofocusFilter: unknown;
    static ngAcceptInputType_showClear: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_focusOnHover: unknown;
    static ngAcceptInputType_selectOnFocus: unknown;
    static ngAcceptInputType_autoOptionFocus: unknown;
    static ngAcceptInputType_highlightOnSelect: unknown;
}
declare class MultiSelectModule {
    static ɵfac: _angular_core.ɵɵFactoryDeclaration<MultiSelectModule, never>;
    static ɵmod: _angular_core.ɵɵNgModuleDeclaration<MultiSelectModule, never, [typeof MultiSelect, typeof i1.SharedModule], [typeof MultiSelect, typeof i1.SharedModule]>;
    static ɵinj: _angular_core.ɵɵInjectorDeclaration<MultiSelectModule>;
}

export { MULTISELECT_VALUE_ACCESSOR, MultiSelect, MultiSelectClasses, MultiSelectItem, MultiSelectModule, MultiSelectStyle };
export type { MultiSelectBlurEvent, MultiSelectChangeEvent, MultiSelectFilterEvent, MultiSelectFilterOptions, MultiSelectFocusEvent, MultiSelectLazyLoadEvent, MultiSelectRemoveEvent, MultiSelectSelectAllChangeEvent, MultiSelectTemplates };
