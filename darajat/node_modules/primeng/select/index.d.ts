import * as i0 from '@angular/core';
import { TemplateRef, EventEmitter, OnInit, AfterViewInit, AfterContentInit, AfterViewChecked, NgZone, Signal, ElementRef, QueryList } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import * as i1 from 'primeng/api';
import { ScrollerOptions, FilterService, OverlayOptions, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { BaseInput } from 'primeng/baseinput';
import { Overlay } from 'primeng/overlay';
import { Scroller } from 'primeng/scroller';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 * Filter callbacks of the select.
 * @group Interface
 */
interface SelectFilterOptions {
    /**
     * Filter function.
     */
    filter?: (value?: any) => void;
    /**
     * Reset function.
     */
    reset?: () => void;
}
/**
 * Custom change event.
 * @see {@link Select.onChange}
 * @group Events
 */
interface SelectChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Selected option value
     */
    value: any;
}
/**
 * Custom filter event.
 * @see {@link Select.onFilter}
 * @group Events
 */
interface SelectFilterEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Filter value.
     */
    filter: any;
}
/**
 * Custom lazy load event.
 * @see {@link Select.onLazyLoad}
 * @group Events
 */
interface SelectLazyLoadEvent {
    /**
     * Index of the first element in viewport.
     */
    first: number;
    /**
     * Index of the last element in viewport.
     */
    last: number;
}
/**
 * Defines valid templates in Select.
 * @group Templates
 */
interface SelectTemplates {
    /**
     * Custom item template.
     * @param {Object} context - item data.
     */
    item(context: {
        /**
         * Data of the option.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom selected item template.
     * @param {Object} context - selected item data.
     */
    selectedItem(context: {
        /**
         * Selected option value.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom header template.
     */
    header(): TemplateRef<any>;
    /**
     * Custom filter template.
     * @param {SelectFilterOptions} options - filter options.
     */
    filter(context: {
        /**
         * Filter options.
         */
        options: SelectFilterOptions;
    }): TemplateRef<{
        options: SelectFilterOptions;
    }>;
    /**
     * Custom footer template.
     */
    footer(): TemplateRef<any>;
    /**
     * Custom empty filter template.
     */
    emptyfilter(): TemplateRef<any>;
    /**
     * Custom empty template.
     */
    empty(): TemplateRef<any>;
    /**
     * Custom group template.
     */
    group(context: {
        /**
         * Group option.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom loader template. This template can be used with virtualScroll.
     * @param {ScrollerOptions} options - virtual scroller options.
     */
    loader(context: {
        /**
         * Virtual scroller options.
         */
        options: ScrollerOptions;
    }): TemplateRef<{
        options: ScrollerOptions;
    }>;
    /**
     * Custom select icon template.
     */
    dropdownicon(): TemplateRef<any>;
    /**
     * Custom clear icon template.
     */
    clearicon(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filtericon(): TemplateRef<any>;
}

/**
 *
 * Select also known as Select, is used to choose an item from a collection of options.
 *
 * [Live Demo](https://www.primeng.org/select/)
 *
 * @module selectstyle
 *
 */
declare enum SelectClasses {
    /**
     * Class name of the root element
     */
    root = "p-select",
    /**
     * Class name of the label element
     */
    label = "p-select-label",
    /**
     * Class name of the clear icon element
     */
    clearIcon = "p-select-clear-icon",
    /**
     * Class name of the dropdown element
     */
    dropdown = "p-select-dropdown",
    /**
     * Class name of the loadingicon element
     */
    loadingIcon = "p-select-loading-icon",
    /**
     * Class name of the dropdown icon element
     */
    dropdownIcon = "p-select-dropdown-icon",
    /**
     * Class name of the overlay element
     */
    overlay = "p-select-overlay",
    /**
     * Class name of the header element
     */
    header = "p-select-header",
    /**
     * Class name of the filter element
     */
    pcFilter = "p-select-filter",
    /**
     * Class name of the list container element
     */
    listContainer = "p-select-list-container",
    /**
     * Class name of the list element
     */
    list = "p-select-list",
    /**
     * Class name of the option group element
     */
    optionGroup = "p-select-option-group",
    /**
     * Class name of the option group label element
     */
    optionGroupLabel = "p-select-option-group-label",
    /**
     * Class name of the option element
     */
    option = "p-select-option",
    /**
     * Class name of the option label element
     */
    optionLabel = "p-select-option-label",
    /**
     * Class name of the option check icon element
     */
    optionCheckIcon = "p-select-option-check-icon",
    /**
     * Class name of the option blank icon element
     */
    optionBlankIcon = "p-select-option-blank-icon",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-select-empty-message"
}
declare class SelectStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
            'p-variant-filled': boolean;
            'p-focus': any;
            'p-invalid': any;
            'p-inputwrapper-filled': any;
            'p-inputwrapper-focus': any;
            'p-select-open': any;
            'p-select-fluid': any;
            'p-select-sm p-inputfield-sm': boolean;
            'p-select-lg p-inputfield-lg': boolean;
        })[];
        label: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-placeholder': boolean;
            'p-select-label-empty': boolean;
        })[];
        clearIcon: string;
        dropdown: string;
        loadingIcon: string;
        dropdownIcon: string;
        overlay: string;
        header: string;
        pcFilter: string;
        listContainer: string;
        list: string;
        optionGroup: string;
        optionGroupLabel: string;
        option: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-select-option-selected': boolean;
            'p-disabled': any;
            'p-focus': any;
        })[];
        optionLabel: string;
        optionCheckIcon: string;
        optionBlankIcon: string;
        emptyMessage: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SelectStyle>;
}
interface SelectStyle extends BaseStyle {
}

declare const SELECT_VALUE_ACCESSOR: any;
declare class SelectItem extends BaseComponent {
    id: string | undefined;
    option: any;
    selected: boolean | undefined;
    focused: boolean | undefined;
    label: string | undefined;
    disabled: boolean | undefined;
    visible: boolean | undefined;
    itemSize: number | undefined;
    ariaPosInset: string | undefined;
    ariaSetSize: string | undefined;
    template: TemplateRef<any> | undefined;
    checkmark: boolean;
    onClick: EventEmitter<any>;
    onMouseEnter: EventEmitter<any>;
    _componentStyle: SelectStyle;
    onOptionClick(event: Event): void;
    onOptionMouseEnter(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectItem, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SelectItem, "p-selectItem", never, { "id": { "alias": "id"; "required": false; }; "option": { "alias": "option"; "required": false; }; "selected": { "alias": "selected"; "required": false; }; "focused": { "alias": "focused"; "required": false; }; "label": { "alias": "label"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "visible": { "alias": "visible"; "required": false; }; "itemSize": { "alias": "itemSize"; "required": false; }; "ariaPosInset": { "alias": "ariaPosInset"; "required": false; }; "ariaSetSize": { "alias": "ariaSetSize"; "required": false; }; "template": { "alias": "template"; "required": false; }; "checkmark": { "alias": "checkmark"; "required": false; }; }, { "onClick": "onClick"; "onMouseEnter": "onMouseEnter"; }, never, never, true, never>;
    static ngAcceptInputType_selected: unknown;
    static ngAcceptInputType_focused: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_visible: unknown;
    static ngAcceptInputType_itemSize: unknown;
    static ngAcceptInputType_checkmark: unknown;
}
/**
 * Select is used to choose an item from a collection of options.
 * @group Components
 */
declare class Select extends BaseInput implements OnInit, AfterViewInit, AfterContentInit, AfterViewChecked {
    zone: NgZone;
    filterService: FilterService;
    /**
     * Unique identifier of the component
     * @group Props
     */
    id: string | undefined;
    /**
     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * When specified, displays an input field to filter the items on keyup.
     * @group Props
     */
    filter: boolean | undefined;
    /**
     * Inline style of the overlay panel element.
     * @group Props
     */
    panelStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Style class of the overlay panel element.
     * @group Props
     */
    panelStyleClass: string | undefined;
    /**
     * When present, it specifies that the component cannot be edited.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * When present, custom value instead of predefined options can be entered using the editable input field.
     * @group Props
     */
    editable: boolean | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * Default text to display when no option is selected.
     * @group Props
     */
    set placeholder(val: string | undefined);
    get placeholder(): Signal<string | undefined>;
    /**
     * Icon to display in loading state.
     * @group Props
     */
    loadingIcon: string | undefined;
    /**
     * Placeholder text to show when filter input is empty.
     * @group Props
     */
    filterPlaceholder: string | undefined;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Identifier of the accessible input element.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * A property to uniquely identify a value in options.
     * @group Props
     */
    dataKey: string | undefined;
    /**
     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.
     * @group Props
     */
    filterBy: string | undefined;
    /**
     * Fields used when filtering the options, defaults to optionLabel.
     * @group Props
     */
    filterFields: any[] | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Clears the filter value when hiding the select.
     * @group Props
     */
    resetFilterOnHide: boolean;
    /**
     * Whether the selected option will be shown with a check mark.
     * @group Props
     */
    checkmark: boolean;
    /**
     * Icon class of the select icon.
     * @group Props
     */
    dropdownIcon: string | undefined;
    /**
     * Whether the select is in loading state.
     * @group Props
     */
    loading: boolean | undefined;
    /**
     * Name of the label field of an option.
     * @group Props
     */
    optionLabel: string | undefined;
    /**
     * Name of the value field of an option.
     * @group Props
     */
    optionValue: string | undefined;
    /**
     * Name of the disabled field of an option.
     * @group Props
     */
    optionDisabled: string | undefined;
    /**
     * Name of the label field of an option group.
     * @group Props
     */
    optionGroupLabel: string | undefined;
    /**
     * Name of the options field of an option group.
     * @group Props
     */
    optionGroupChildren: string;
    /**
     * Whether to display options as grouped when nested options are provided.
     * @group Props
     */
    group: boolean | undefined;
    /**
     * When enabled, a clear icon is displayed to clear the value.
     * @group Props
     */
    showClear: boolean | undefined;
    /**
     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.
     * @group Props
     */
    emptyFilterMessage: string;
    /**
     * Text to display when there is no data. Defaults to global value in i18n translation configuration.
     * @group Props
     */
    emptyMessage: string;
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    lazy: boolean;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Height of an item in the list for VirtualScrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.
     * @group Props
     */
    overlayOptions: OverlayOptions | undefined;
    /**
     * Defines a string that labels the filter input.
     * @group Props
     */
    ariaFilterLabel: string | undefined;
    /**
     * Used to define a aria label attribute the current element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Defines how the items are filtered.
     * @group Props
     */
    filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte';
    /**
     * Advisory information to display in a tooltip on hover.
     * @group Props
     */
    tooltip: string;
    /**
     * Position of the tooltip.
     * @group Props
     */
    tooltipPosition: 'top' | 'left' | 'right' | 'bottom';
    /**
     * Type of CSS position.
     * @group Props
     */
    tooltipPositionStyle: string;
    /**
     * Style class of the tooltip.
     * @group Props
     */
    tooltipStyleClass: string | undefined;
    /**
     * Fields used when filtering the options, defaults to optionLabel.
     * @group Props
     */
    focusOnHover: boolean;
    /**
     * Determines if the option will be selected on focus.
     * @group Props
     */
    selectOnFocus: boolean;
    /**
     * Whether to focus on the first visible or selected element when the overlay panel is shown.
     * @group Props
     */
    autoOptionFocus: boolean;
    /**
     * Applies focus to the filter element when the overlay is shown.
     * @group Props
     */
    autofocusFilter: boolean;
    /**
     * When specified, filter displays with this value.
     * @group Props
     */
    get filterValue(): string | undefined | null;
    set filterValue(val: string | undefined | null);
    /**
     * An array of objects to display as the available options.
     * @group Props
     */
    get options(): any[] | undefined;
    set options(val: any[] | undefined);
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    /**
     * Callback to invoke when value of select changes.
     * @param {SelectChangeEvent} event - custom change event.
     * @group Emits
     */
    onChange: EventEmitter<SelectChangeEvent>;
    /**
     * Callback to invoke when data is filtered.
     * @param {SelectFilterEvent} event - custom filter event.
     * @group Emits
     */
    onFilter: EventEmitter<SelectFilterEvent>;
    /**
     * Callback to invoke when select gets focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when select loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Callback to invoke when component is clicked.
     * @param {MouseEvent} event - Mouse event.
     * @group Emits
     */
    onClick: EventEmitter<MouseEvent>;
    /**
     * Callback to invoke when select overlay gets visible.
     * @param {AnimationEvent} event - Animation event.
     * @group Emits
     */
    onShow: EventEmitter<AnimationEvent>;
    /**
     * Callback to invoke when select overlay gets hidden.
     * @param {AnimationEvent} event - Animation event.
     * @group Emits
     */
    onHide: EventEmitter<AnimationEvent>;
    /**
     * Callback to invoke when select clears the value.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onClear: EventEmitter<Event>;
    /**
     * Callback to invoke in lazy mode to load new data.
     * @param {SelectLazyLoadEvent} event - Lazy load event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<SelectLazyLoadEvent>;
    _componentStyle: SelectStyle;
    filterViewChild: Nullable<ElementRef>;
    focusInputViewChild: Nullable<ElementRef>;
    editableInputViewChild: Nullable<ElementRef>;
    itemsViewChild: Nullable<ElementRef>;
    scroller: Nullable<Scroller>;
    overlayViewChild: Nullable<Overlay>;
    firstHiddenFocusableElementOnOverlay: Nullable<ElementRef>;
    lastHiddenFocusableElementOnOverlay: Nullable<ElementRef>;
    itemsWrapper: Nullable<HTMLDivElement>;
    $appendTo: Signal<any>;
    /**
     * Custom item template.
     * @group Templates
     */
    itemTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom group template.
     * @group Templates
     */
    groupTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom loader template.
     * @group Templates
     */
    loaderTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom selected item template.
     * @group Templates
     */
    selectedItemTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom header template.
     * @group Templates
     */
    headerTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom filter template.
     * @group Templates
     */
    filterTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom footer template.
     * @group Templates
     */
    footerTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom empty filter template.
     * @group Templates
     */
    emptyFilterTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom empty template.
     * @group Templates
     */
    emptyTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom dropdown icon template.
     * @group Templates
     */
    dropdownIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom loading icon template.
     * @group Templates
     */
    loadingIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom clear icon template.
     * @group Templates
     */
    clearIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom filter icon template.
     * @group Templates
     */
    filterIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom on icon template.
     * @group Templates
     */
    onIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom off icon template.
     * @group Templates
     */
    offIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom cancel icon template.
     * @group Templates
     */
    cancelIconTemplate: Nullable<TemplateRef<any>>;
    templates: QueryList<PrimeTemplate> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    _selectedItemTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _filterTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _emptyFilterTemplate: TemplateRef<any> | undefined;
    _emptyTemplate: TemplateRef<any> | undefined;
    _groupTemplate: TemplateRef<any> | undefined;
    _loaderTemplate: TemplateRef<any> | undefined;
    _dropdownIconTemplate: TemplateRef<any> | undefined;
    _loadingIconTemplate: TemplateRef<any> | undefined;
    _clearIconTemplate: TemplateRef<any> | undefined;
    _filterIconTemplate: TemplateRef<any> | undefined;
    _cancelIconTemplate: TemplateRef<any> | undefined;
    _onIconTemplate: TemplateRef<any> | undefined;
    _offIconTemplate: TemplateRef<any> | undefined;
    filterOptions: SelectFilterOptions | undefined;
    _options: i0.WritableSignal<any[]>;
    _placeholder: i0.WritableSignal<string>;
    value: any;
    hover: Nullable<boolean>;
    focused: Nullable<boolean>;
    overlayVisible: Nullable<boolean>;
    optionsChanged: Nullable<boolean>;
    panel: Nullable<HTMLDivElement>;
    dimensionsUpdated: Nullable<boolean>;
    hoveredItem: any;
    selectedOptionUpdated: Nullable<boolean>;
    _filterValue: i0.WritableSignal<any>;
    searchValue: Nullable<string>;
    searchIndex: Nullable<number>;
    searchTimeout: any;
    previousSearchChar: Nullable<string>;
    currentSearchChar: Nullable<string>;
    preventModelTouched: Nullable<boolean>;
    focusedOptionIndex: i0.WritableSignal<number>;
    labelId: Nullable<string>;
    listId: Nullable<string>;
    clicked: i0.WritableSignal<boolean>;
    get emptyMessageLabel(): string;
    get emptyFilterMessageLabel(): string;
    get isVisibleClearIcon(): boolean | undefined;
    get listLabel(): string;
    get focusedOptionId(): string;
    visibleOptions: Signal<any>;
    label: Signal<any>;
    selectedOption: any;
    constructor(zone: NgZone, filterService: FilterService);
    private isModelValueNotSet;
    private getAllVisibleAndNonVisibleOptions;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    ngAfterViewChecked(): void;
    flatOptions(options: any): any;
    autoUpdateModel(): void;
    onOptionSelect(event: any, option: any, isHide?: boolean, preventChange?: boolean): void;
    onOptionMouseEnter(event: any, index: any): void;
    updateModel(value: any, event?: any): void;
    allowModelChange(): number;
    isSelected(option: any): boolean;
    private isOptionValueEqualsModelValue;
    ngAfterViewInit(): void;
    updatePlaceHolderForFloatingLabel(): void;
    updateEditableLabel(): void;
    clearEditableLabel(): void;
    getOptionIndex(index: any, scrollerOptions: any): any;
    getOptionLabel(option: any): any;
    getOptionValue(option: any): any;
    isSelectedOptionEmpty(): boolean;
    isOptionDisabled(option: any): any;
    getOptionGroupLabel(optionGroup: any): any;
    getOptionGroupChildren(optionGroup: any): any;
    getAriaPosInset(index: any): any;
    get ariaSetSize(): any;
    /**
     * Callback to invoke on filter reset.
     * @group Method
     */
    resetFilter(): void;
    onContainerClick(event: any): void;
    isEmpty(): boolean;
    onEditableInput(event: Event): void;
    /**
     * Displays the panel.
     * @group Method
     */
    show(isFocus?: any): void;
    onOverlayAnimationStart(event: AnimationEvent): void;
    /**
     * Hides the panel.
     * @group Method
     */
    hide(isFocus?: any): void;
    onInputFocus(event: Event): void;
    onInputBlur(event: Event): void;
    onKeyDown(event: KeyboardEvent, search?: boolean): void;
    onFilterKeyDown(event: any): void;
    onFilterBlur(event: any): void;
    onArrowDownKey(event: KeyboardEvent): void;
    changeFocusedOptionIndex(event: any, index: any): void;
    get virtualScrollerDisabled(): boolean;
    scrollInView(index?: number): void;
    hasSelectedOption(): boolean;
    isValidSelectedOption(option: any): boolean;
    equalityKey(): string;
    findFirstFocusedOptionIndex(): any;
    findFirstOptionIndex(): any;
    findSelectedOptionIndex(): any;
    findNextOptionIndex(index: any): any;
    findPrevOptionIndex(index: any): any;
    findLastOptionIndex(): number;
    findLastFocusedOptionIndex(): any;
    isValidOption(option: any): boolean;
    isOptionGroup(option: any): any;
    onArrowUpKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onArrowLeftKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onDeleteKey(event: KeyboardEvent): void;
    onHomeKey(event: any, pressedInInputText?: boolean): void;
    onEndKey(event: any, pressedInInputText?: boolean): void;
    onPageDownKey(event: KeyboardEvent): void;
    onPageUpKey(event: KeyboardEvent): void;
    onSpaceKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onEnterKey(event: any, pressedInInput?: boolean): void;
    onEscapeKey(event: KeyboardEvent): void;
    onTabKey(event: any, pressedInInputText?: boolean): void;
    onFirstHiddenFocus(event: any): void;
    onLastHiddenFocus(event: any): void;
    hasFocusableElements(): boolean;
    onBackspaceKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    searchFields(): any[];
    searchOptions(event: any, char: any): boolean;
    isOptionMatched(option: any): any;
    onFilterInputChange(event: Event | any): void;
    applyFocus(): void;
    /**
     * Applies focus.
     * @group Method
     */
    focus(): void;
    /**
     * Clears the model.
     * @group Method
     */
    clear(event?: Event): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Select, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Select, "p-select", never, { "id": { "alias": "id"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "filter": { "alias": "filter"; "required": false; }; "panelStyle": { "alias": "panelStyle"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "panelStyleClass": { "alias": "panelStyleClass"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "editable": { "alias": "editable"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "loadingIcon": { "alias": "loadingIcon"; "required": false; }; "filterPlaceholder": { "alias": "filterPlaceholder"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "dataKey": { "alias": "dataKey"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "filterFields": { "alias": "filterFields"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "resetFilterOnHide": { "alias": "resetFilterOnHide"; "required": false; }; "checkmark": { "alias": "checkmark"; "required": false; }; "dropdownIcon": { "alias": "dropdownIcon"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "optionLabel": { "alias": "optionLabel"; "required": false; }; "optionValue": { "alias": "optionValue"; "required": false; }; "optionDisabled": { "alias": "optionDisabled"; "required": false; }; "optionGroupLabel": { "alias": "optionGroupLabel"; "required": false; }; "optionGroupChildren": { "alias": "optionGroupChildren"; "required": false; }; "group": { "alias": "group"; "required": false; }; "showClear": { "alias": "showClear"; "required": false; }; "emptyFilterMessage": { "alias": "emptyFilterMessage"; "required": false; }; "emptyMessage": { "alias": "emptyMessage"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "overlayOptions": { "alias": "overlayOptions"; "required": false; }; "ariaFilterLabel": { "alias": "ariaFilterLabel"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "filterMatchMode": { "alias": "filterMatchMode"; "required": false; }; "tooltip": { "alias": "tooltip"; "required": false; }; "tooltipPosition": { "alias": "tooltipPosition"; "required": false; }; "tooltipPositionStyle": { "alias": "tooltipPositionStyle"; "required": false; }; "tooltipStyleClass": { "alias": "tooltipStyleClass"; "required": false; }; "focusOnHover": { "alias": "focusOnHover"; "required": false; }; "selectOnFocus": { "alias": "selectOnFocus"; "required": false; }; "autoOptionFocus": { "alias": "autoOptionFocus"; "required": false; }; "autofocusFilter": { "alias": "autofocusFilter"; "required": false; }; "filterValue": { "alias": "filterValue"; "required": false; }; "options": { "alias": "options"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, { "onChange": "onChange"; "onFilter": "onFilter"; "onFocus": "onFocus"; "onBlur": "onBlur"; "onClick": "onClick"; "onShow": "onShow"; "onHide": "onHide"; "onClear": "onClear"; "onLazyLoad": "onLazyLoad"; }, ["itemTemplate", "groupTemplate", "loaderTemplate", "selectedItemTemplate", "headerTemplate", "filterTemplate", "footerTemplate", "emptyFilterTemplate", "emptyTemplate", "dropdownIconTemplate", "loadingIconTemplate", "clearIconTemplate", "filterIconTemplate", "onIconTemplate", "offIconTemplate", "cancelIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_filter: unknown;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_editable: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_resetFilterOnHide: unknown;
    static ngAcceptInputType_checkmark: unknown;
    static ngAcceptInputType_loading: unknown;
    static ngAcceptInputType_group: unknown;
    static ngAcceptInputType_showClear: unknown;
    static ngAcceptInputType_lazy: unknown;
    static ngAcceptInputType_virtualScroll: unknown;
    static ngAcceptInputType_virtualScrollItemSize: unknown;
    static ngAcceptInputType_focusOnHover: unknown;
    static ngAcceptInputType_selectOnFocus: unknown;
    static ngAcceptInputType_autoOptionFocus: unknown;
    static ngAcceptInputType_autofocusFilter: unknown;
}
declare class SelectModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SelectModule, never, [typeof Select, typeof i1.SharedModule], [typeof Select, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SelectModule>;
}

export { SELECT_VALUE_ACCESSOR, Select, SelectClasses, SelectItem, SelectModule, SelectStyle };
export type { SelectChangeEvent, SelectFilterEvent, SelectFilterOptions, SelectLazyLoadEvent, SelectTemplates };
