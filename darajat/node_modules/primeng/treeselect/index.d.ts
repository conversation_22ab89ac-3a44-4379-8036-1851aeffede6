import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, EventEmitter, ElementRef, QueryList } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import * as i1 from 'primeng/api';
import { TreeNode, OverlayOptions, ScrollerOptions, PrimeTemplate } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { Fluid } from 'primeng/fluid';
import { Overlay } from 'primeng/overlay';
import { TreeFilterEvent, TreeNodeUnSelectEvent, TreeNodeSelectEvent, Tree } from 'primeng/tree';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 *
 * TreeSelect is a form component to choose from hierarchical data.
 *
 * [Live Demo](https://www.primeng.org/treeselect/)
 *
 * @module treeselectstyle
 *
 */
declare enum TreeSelectClasses {
    /**
     * Class name of the root element
     */
    root = "p-treeselect",
    /**
     * Class name of the label container element
     */
    labelContainer = "p-treeselect-label-container",
    /**
     * Class name of the label element
     */
    label = "p-treeselect-label",
    /**
     * Class name of the chip item element
     */
    chipItem = "p-treeselect-chip-item",
    /**
     * Class name of the chip element
     */
    pcChip = "p-treeselect-chip",
    /**
     * Class name of the dropdown element
     */
    dropdown = "p-treeselect-dropdown",
    /**
     * Class name of the dropdown icon element
     */
    dropdownIcon = "p-treeselect-dropdown-icon",
    /**
     * Class name of the panel element
     */
    panel = "p-treeselect-overlay",
    /**
     * Class name of the tree container element
     */
    treeContainer = "p-treeselect-tree-container",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-treeselect-empty-message"
}
declare class TreeSelectStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-treeselect-display-chip': boolean;
            'p-disabled': any;
            'p-invalid': any;
            'p-focus': any;
            'p-variant-filled': boolean;
            'p-inputwrapper-filled': boolean;
            'p-inputwrapper-focus': any;
            'p-treeselect-open': any;
            'p-treeselect-clearable': any;
            'p-treeselect-fluid': any;
            'p-treeselect-sm p-inputfield-sm': boolean;
            'p-treeselect-lg p-inputfield-lg': boolean;
        })[];
        labelContainer: string;
        label: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-placeholder': boolean;
            'p-treeselect-label-empty': any;
        })[];
        chip: string;
        pcChip: string;
        dropdown: string;
        dropdownIcon: string;
        panel: string;
        treeContainer: string;
        emptyMessage: string;
    };
    inlineStyles: {
        root: ({ instance }: {
            instance: any;
        }) => any;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeSelectStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TreeSelectStyle>;
}
interface TreeSelectStyle extends BaseStyle {
}

/**
 * Custom node collapse event.
 * @see {@link TreeSelect.onNodeCollapse}
 * @group Events
 */
interface TreeSelectNodeCollapseEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Node instance.
     */
    node: TreeNode;
}
/**
 * Custom node expand event.
 * @see {@link TreeSelect.onNodeExpand}
 * @group Events
 */
interface TreeSelectNodeExpandEvent extends TreeSelectNodeCollapseEvent {
}
/**
 * Defines valid templates in TreeSelect.
 * @group Templates
 */
interface TreeSelectTemplates {
    /**
     * Custom value template.
     * @param {Object} context - value data.
     */
    value(context: {
        /**
         * Value of the component.
         */
        $implicit: TreeNode<any> | null;
        /**
         * Placeholder of the component.
         */
        placeholder: string;
    }): TemplateRef<{
        $implicit: TreeNode<any> | null;
        placeholder: string;
    }>;
    /**
     * Custom header template.
     * @param {Object} context - header data.
     */
    header(context: {
        /**
         * Value of the component.
         */
        $implicit: TreeNode<any> | null | undefined;
        /**
         * Placeholder of the component.
         */
        options: TreeNode<any>[] | undefined;
    }): TemplateRef<{
        $implicit: TreeNode<any> | null;
        options: TreeNode[] | null | undefined;
    }>;
    /**
     * Custom empty template.
     */
    empty(): TemplateRef<any>;
    /**
     * Custom footer template.
     */
    footer(): TemplateRef<any>;
    /**
     * Custom clear icon template.
     */
    clearicon(): TemplateRef<any>;
    /**
     * Custom dropdown trigger icon template.
     */
    triggericon(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filtericon(): TemplateRef<any>;
    /**
     * Custom close icon template.
     */
    closeicon(): TemplateRef<any>;
    /**
     * Custom node toggler template.
     * @param {Object} context - toggler icon data.
     */
    itemtogglericon(context: {
        /**
         * Expanded state of the node.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: TreeNode<any> | null;
    }>;
    /**
     * Custom node checkbox icon template.
     */
    itemcheckboxicon(): TemplateRef<any>;
    /**
     * Custom loading icon template.
     */
    itemloadingicon(): TemplateRef<any>;
}

declare const TREESELECT_VALUE_ACCESSOR: any;
/**
 * TreeSelect is a form component to choose from hierarchical data.
 * @group Components
 */
declare class TreeSelect extends BaseEditableHolder implements AfterContentInit {
    /**
     * Identifier of the underlying input element.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * Height of the viewport, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean;
    /**
     * Defines how the selected items are displayed.
     * @group Props
     */
    display: 'comma' | 'chip';
    /**
     * Defines the selection mode.
     * @group Props
     */
    selectionMode: 'single' | 'multiple' | 'checkbox';
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: string | undefined;
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Label to display when there are no selections.
     * @group Props
     */
    placeholder: string | undefined;
    /**
     * Style class of the overlay panel.
     * @group Props
     */
    panelClass: string | string[] | Set<string> | {
        [klass: string]: any;
    } | undefined;
    /**
     * Inline style of the panel element.
     * @group Props
     */
    panelStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the panel element.
     * @group Props
     */
    panelStyleClass: string | undefined;
    /**
     * Inline style of the container element.
     * @deprecated since v20.0.0, use `style` instead.
     * @group Props
     */
    containerStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the container element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    containerStyleClass: string | undefined;
    /**
     * Inline style of the label element.
     * @group Props
     */
    labelStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the label element.
     * @group Props
     */
    labelStyleClass: string | undefined;
    /**
     * Specifies the options for the overlay.
     * @group Props
     */
    overlayOptions: OverlayOptions | undefined;
    /**
     * Text to display when there are no options available. Defaults to value from PrimeNG locale configuration.
     * @group Props
     */
    emptyMessage: string;
    /**
     * When specified, displays an input field to filter the items.
     * @group Props
     */
    filter: boolean;
    /**
     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.
     * @group Props
     */
    filterBy: string;
    /**
     * Mode for filtering valid values are "lenient" and "strict". Default is lenient.
     * @group Props
     */
    filterMode: string;
    /**
     * Placeholder text to show when filter input is empty.
     * @group Props
     */
    filterPlaceholder: string | undefined;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Determines whether the filter input should be automatically focused when the component is rendered.
     * @group Props
     */
    filterInputAutoFocus: boolean;
    /**
     * Whether checkbox selections propagate to descendant nodes.
     * @group Props
     */
    propagateSelectionDown: boolean;
    /**
     * Whether checkbox selections propagate to ancestor nodes.
     * @group Props
     */
    propagateSelectionUp: boolean;
    /**
     * When enabled, a clear icon is displayed to clear the value.
     * @group Props
     */
    showClear: boolean;
    /**
     * Clears the filter value when hiding the dropdown.
     * @group Props
     */
    resetFilterOnHide: boolean;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Height of an item in the list for VirtualScrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * An array of treenodes.
     * @defaultValue undefined
     * @group Props
     */
    get options(): TreeNode[] | undefined;
    set options(options: TreeNode[] | undefined);
    /**
     * Displays a loader to indicate data load is in progress.
     * @group Props
     */
    loading: boolean | undefined;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: i0.InputSignal<"small" | "large">;
    /**
     * Specifies the input variant of the component.
     * @defaultValue undefined
     * @group Props
     */
    variant: i0.InputSignal<"filled" | "outlined">;
    /**
     * Spans 100% width of the container when enabled.
     * @defaultValue undefined
     * @group Props
     */
    fluid: i0.InputSignalWithTransform<boolean, unknown>;
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    /**
     * Callback to invoke when a node is expanded.
     * @param {TreeSelectNodeExpandEvent} event - Custom node expand event.
     * @group Emits
     */
    onNodeExpand: EventEmitter<TreeSelectNodeExpandEvent>;
    /**
     * Callback to invoke when a node is collapsed.
     * @param {TreeSelectNodeCollapseEvent} event - Custom node collapse event.
     * @group Emits
     */
    onNodeCollapse: EventEmitter<TreeSelectNodeCollapseEvent>;
    /**
     * Callback to invoke when the overlay is shown.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onShow: EventEmitter<any>;
    /**
     * Callback to invoke when the overlay is hidden.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onHide: EventEmitter<Event>;
    /**
     * Callback to invoke when input field is cleared.
     * @group Emits
     */
    onClear: EventEmitter<any>;
    /**
     * Callback to invoke when data is filtered.
     * @group Emits
     */
    onFilter: EventEmitter<TreeFilterEvent>;
    /**
     * Callback to invoke when treeselect gets focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when treeselect loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Callback to invoke when a node is unselected.
     * @param {TreeNodeUnSelectEvent} event - node unselect event.
     * @group Emits
     */
    onNodeUnselect: EventEmitter<TreeNodeUnSelectEvent>;
    /**
     * Callback to invoke when a node is selected.
     * @param {TreeNodeSelectEvent} event - node select event.
     * @group Emits
     */
    onNodeSelect: EventEmitter<TreeNodeSelectEvent>;
    $appendTo: i0.Signal<any>;
    focusInput: Nullable<ElementRef>;
    filterViewChild: Nullable<ElementRef>;
    treeViewChild: Nullable<Tree>;
    panelEl: Nullable<ElementRef>;
    overlayViewChild: Nullable<Overlay>;
    firstHiddenFocusableElementOnOverlay: Nullable<ElementRef>;
    lastHiddenFocusableElementOnOverlay: Nullable<ElementRef>;
    $variant: i0.Signal<"filled" | "outlined">;
    pcFluid: Fluid;
    get hasFluid(): boolean;
    filteredNodes: TreeNode[] | undefined | null;
    filterValue: Nullable<string>;
    serializedValue: Nullable<any[]>;
    /**
     * Custom value template.
     * @group Templates
     */
    valueTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom header template.
     * @group Templates
     */
    headerTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom empty message template.
     * @group Templates
     */
    emptyTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom footer template.
     * @group Templates
     */
    footerTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom clear icon template.
     * @group Templates
     */
    clearIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom trigger icon template.
     * @group Templates
     */
    triggerIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom dropdown icon template.
     * @group Templates
     */
    dropdownIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom filter icon template.
     * @group Templates
     */
    filterIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom close icon template.
     * @group Templates
     */
    closeIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom item toggler icon template.
     * @group Templates
     */
    itemTogglerIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom item checkbox icon template.
     * @group Templates
     */
    itemCheckboxIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom item loading icon template.
     * @group Templates
     */
    itemLoadingIconTemplate: Nullable<TemplateRef<any>>;
    templates: Nullable<QueryList<PrimeTemplate>>;
    _valueTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _emptyTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _clearIconTemplate: TemplateRef<any> | undefined;
    _triggerIconTemplate: TemplateRef<any> | undefined;
    _filterIconTemplate: TemplateRef<any> | undefined;
    _closeIconTemplate: TemplateRef<any> | undefined;
    _itemTogglerIconTemplate: TemplateRef<any> | undefined;
    _itemCheckboxIconTemplate: TemplateRef<any> | undefined;
    _itemLoadingIconTemplate: TemplateRef<any> | undefined;
    _dropdownIconTemplate: TemplateRef<any> | undefined;
    focused: Nullable<boolean>;
    overlayVisible: Nullable<boolean>;
    value: any | undefined;
    expandedNodes: any[];
    _options: TreeNode[] | undefined;
    templateMap: any;
    listId: string;
    _componentStyle: TreeSelectStyle;
    onHostClick(event: MouseEvent): void;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    onOverlayAnimationStart(event: AnimationEvent): void;
    onOverlayBeforeHide(event: any): void;
    onSelectionChange(event: any): void;
    onClick(event: any): void;
    onKeyDown(event: KeyboardEvent): void;
    onFilterInput(event: Event): void;
    onArrowDown(event: KeyboardEvent): void;
    onFirstHiddenFocus(event: any): void;
    onLastHiddenFocus(event: any): void;
    show(): void;
    hide(event?: any): void;
    clear(event: Event): void;
    checkValue(): boolean;
    onTabKey(event: any, pressedInInputText?: boolean): void;
    hasFocusableElements(): boolean;
    resetFilter(): void;
    updateTreeState(): void;
    updateTreeBranchState(node: TreeNode | null, path: any, selectedNodes: TreeNode[]): void;
    expandPath(expandedNodes: TreeNode[]): void;
    nodeExpand(event: {
        originalEvent: Event;
        node: TreeNode;
    }): void;
    nodeCollapse(event: {
        originalEvent: Event;
        node: TreeNode;
    }): void;
    resetExpandedNodes(): void;
    resetPartialSelected(nodes?: TreeNode<any>[]): void;
    findSelectedNodes(node: TreeNode, keys: any[], selectedNodes: TreeNode[]): void;
    isSelected(node: TreeNode): boolean;
    findIndexInSelection(node: TreeNode): number;
    onSelect(event: TreeNodeSelectEvent): void;
    onUnselect(event: TreeNodeUnSelectEvent): void;
    onInputFocus(event: Event): void;
    onInputBlur(event: Event): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any): void;
    get emptyValue(): boolean;
    get emptyOptions(): boolean;
    get label(): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeSelect, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TreeSelect, "p-treeSelect, p-treeselect, p-tree-select", never, { "inputId": { "alias": "inputId"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "display": { "alias": "display"; "required": false; }; "selectionMode": { "alias": "selectionMode"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "panelClass": { "alias": "panelClass"; "required": false; }; "panelStyle": { "alias": "panelStyle"; "required": false; }; "panelStyleClass": { "alias": "panelStyleClass"; "required": false; }; "containerStyle": { "alias": "containerStyle"; "required": false; }; "containerStyleClass": { "alias": "containerStyleClass"; "required": false; }; "labelStyle": { "alias": "labelStyle"; "required": false; }; "labelStyleClass": { "alias": "labelStyleClass"; "required": false; }; "overlayOptions": { "alias": "overlayOptions"; "required": false; }; "emptyMessage": { "alias": "emptyMessage"; "required": false; }; "filter": { "alias": "filter"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "filterMode": { "alias": "filterMode"; "required": false; }; "filterPlaceholder": { "alias": "filterPlaceholder"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "filterInputAutoFocus": { "alias": "filterInputAutoFocus"; "required": false; }; "propagateSelectionDown": { "alias": "propagateSelectionDown"; "required": false; }; "propagateSelectionUp": { "alias": "propagateSelectionUp"; "required": false; }; "showClear": { "alias": "showClear"; "required": false; }; "resetFilterOnHide": { "alias": "resetFilterOnHide"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "options": { "alias": "options"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "size": { "alias": "size"; "required": false; "isSignal": true; }; "variant": { "alias": "variant"; "required": false; "isSignal": true; }; "fluid": { "alias": "fluid"; "required": false; "isSignal": true; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, { "onNodeExpand": "onNodeExpand"; "onNodeCollapse": "onNodeCollapse"; "onShow": "onShow"; "onHide": "onHide"; "onClear": "onClear"; "onFilter": "onFilter"; "onFocus": "onFocus"; "onBlur": "onBlur"; "onNodeUnselect": "onNodeUnselect"; "onNodeSelect": "onNodeSelect"; }, ["valueTemplate", "headerTemplate", "emptyTemplate", "footerTemplate", "clearIconTemplate", "triggerIconTemplate", "dropdownIconTemplate", "filterIconTemplate", "closeIconTemplate", "itemTogglerIconTemplate", "itemCheckboxIconTemplate", "itemLoadingIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_filter: unknown;
    static ngAcceptInputType_filterInputAutoFocus: unknown;
    static ngAcceptInputType_propagateSelectionDown: unknown;
    static ngAcceptInputType_propagateSelectionUp: unknown;
    static ngAcceptInputType_showClear: unknown;
    static ngAcceptInputType_resetFilterOnHide: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_loading: unknown;
}
declare class TreeSelectModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TreeSelectModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TreeSelectModule, never, [typeof TreeSelect, typeof i1.SharedModule], [typeof TreeSelect, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TreeSelectModule>;
}

export { TREESELECT_VALUE_ACCESSOR, TreeSelect, TreeSelectClasses, TreeSelectModule, TreeSelectStyle };
export type { TreeSelectNodeCollapseEvent, TreeSelectNodeExpandEvent, TreeSelectTemplates };
