import * as i0 from '@angular/core';
import { AfterViewInit, AfterContentInit, OnDestroy, EventEmitter, ElementRef, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { MenuItem, TooltipOptions, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { ButtonProps } from 'primeng/button';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * When pressed, a floating action button can display multiple primary actions that can be performed on a page.
 *
 * [Live Demo](https://www.primeng.org/speeddial/)
 *
 * @module speeddialstyle
 *
 */
declare enum SpeedDialClasses {
    /**
     * Class name of the root element
     */
    root = "p-speeddial",
    /**
     * Class name of the button element
     */
    pcButton = "p-speeddial-button",
    /**
     * Class name of the list element
     */
    list = "p-speeddial-list",
    /**
     * Class name of the item element
     */
    item = "p-speeddial-item",
    /**
     * Class name of the action element
     */
    action = "p-speeddial-action",
    /**
     * Class name of the action icon element
     */
    actionIcon = "p-speeddial-action-icon",
    /**
     * Class name of the mask element
     */
    mask = "p-speeddial-mask"
}
declare class SpeedDialStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            [x: string]: any;
            'p-speeddial-open': any;
            'p-disabled': any;
        })[];
        pcButton: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-speeddial-rotate': boolean;
        })[];
        list: string;
        item: ({ instance, item, i }: {
            instance: any;
            item: any;
            i: any;
        }) => (string | {
            'p-hidden': boolean;
            'p-focus': boolean;
        })[];
        action: string;
        actionIcon: string;
        mask: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-speeddial-mask-visible': any;
        })[];
    };
    inlineStyles: {
        root: ({ instance }: {
            instance: any;
        }) => {
            alignItems: string;
            justifyContent: string;
            flexDirection: string;
        };
        list: ({ instance }: {
            instance: any;
        }) => {
            flexDirection: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SpeedDialStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SpeedDialStyle>;
}
interface SpeedDialStyle extends BaseStyle {
}

/**
 * When pressed, a floating action button can display multiple primary actions that can be performed on a page.
 * @group Components
 */
declare class SpeedDial extends BaseComponent implements AfterViewInit, AfterContentInit, OnDestroy {
    /**
     * List of items id.
     * @group Props
     */
    id: string | undefined;
    /**
     * MenuModel instance to define the action items.
     * @group Props
     */
    model: MenuItem[] | null;
    /**
     * Specifies the visibility of the overlay.
     * @defaultValue false
     * @group Props
     */
    get visible(): boolean;
    set visible(value: boolean);
    /**
     * Inline style of the element.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the element.
     * @group Props
     */
    className: string | undefined;
    /**
     * Specifies the opening direction of actions.
     * @gruop Props
     */
    direction: 'up' | 'down' | 'left' | 'right' | 'up-left' | 'up-right' | 'down-left' | 'down-right' | undefined;
    /**
     * Transition delay step for each action item.
     * @group Props
     */
    transitionDelay: number;
    /**
     * Specifies the opening type of actions.
     * @group Props
     */
    type: 'linear' | 'circle' | 'semi-circle' | 'quarter-circle' | undefined;
    /**
     * Radius for *circle types.
     * @group Props
     */
    radius: number;
    /**
     * Whether to show a mask element behind the speeddial.
     * @group Props
     */
    mask: boolean;
    /**
     * Whether the component is disabled.
     * @group Props
     */
    disabled: boolean;
    /**
     * Whether the actions close when clicked outside.
     * @group Props
     */
    hideOnClickOutside: boolean;
    /**
     * Inline style of the button element.
     * @group Props
     */
    buttonStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the button element.
     * @group Props
     */
    buttonClassName: string | undefined;
    /**
     * Inline style of the mask element.
     * @group Props
     */
    maskStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the mask element.
     * @group Props
     */
    maskClassName: string | undefined;
    /**
     * Show icon of the button element.
     * @group Props
     */
    showIcon: string | undefined;
    /**
     * Hide icon of the button element.
     * @group Props
     */
    hideIcon: string | undefined;
    /**
     * Defined to rotate showIcon when hideIcon is not present.
     * @group Props
     */
    rotateAnimation: boolean;
    /**
     * Defines a string value that labels an interactive element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Identifier of the underlying input element.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Whether to display the tooltip on items. The modifiers of Tooltip can be used like an object in it. Valid keys are 'event' and 'position'.
     * @group Props
     */
    tooltipOptions: TooltipOptions;
    /**
     * Used to pass all properties of the ButtonProps to the Button component.
     * @group Props
     */
    buttonProps: ButtonProps;
    /**
     * Fired when the visibility of element changed.
     * @param {boolean} boolean - Visibility value.
     * @group Emits
     */
    onVisibleChange: EventEmitter<boolean>;
    /**
     * Fired when the visibility of element changed.
     * @param {boolean} boolean - Visibility value.
     * @group Emits
     */
    visibleChange: EventEmitter<boolean>;
    /**
     * Fired when the button element clicked.
     * @param {MouseEvent} event - Mouse event.
     * @group Emits
     */
    onClick: EventEmitter<MouseEvent>;
    /**
     * Fired when the actions are visible.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onShow: EventEmitter<Event>;
    /**
     * Fired when the actions are hidden.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onHide: EventEmitter<Event>;
    container: ElementRef | undefined;
    list: ElementRef | undefined;
    /**
     * Template of the button.
     * @group Templates
     */
    buttonTemplate: TemplateRef<any> | undefined;
    /**
     * Template of the item.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    /**
     * Template of the item.
     * @group Templates
     */
    iconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _buttonTemplate: TemplateRef<any> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    _iconTemplate: TemplateRef<any> | undefined;
    isItemClicked: boolean;
    _visible: boolean;
    documentClickListener: any;
    focusedOptionIndex: i0.WritableSignal<any>;
    focused: boolean;
    _componentStyle: SpeedDialStyle;
    get focusedOptionId(): any;
    getTooltipOptions(item: MenuItem): {
        tooltipLabel: string;
        disabled: boolean;
        tooltipPosition?: "right" | "left" | "top" | "bottom";
        tooltipEvent?: "hover" | "focus";
        appendTo?: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;
        positionStyle?: string;
        tooltipStyleClass?: string;
        tooltipZIndex?: string;
        escape?: boolean;
        positionTop?: number;
        positionLeft?: number;
        showDelay?: number;
        hideDelay?: number;
        life?: number;
        id?: string;
    };
    ngOnInit(): void;
    ngAfterViewInit(): void;
    ngAfterContentInit(): void;
    show(): void;
    hide(): void;
    onButtonClick(event: MouseEvent): void;
    onItemClick(e: Event, item: MenuItem): void;
    onKeyDown(event: KeyboardEvent): void;
    onFocus(event: any): void;
    onBlur(event: any): void;
    onArrowUp(event: any): void;
    onArrowDown(event: any): void;
    onArrowLeft(event: any): void;
    onArrowRight(event: any): void;
    onEndKey(event: any): void;
    onHomeKey(event: any): void;
    onEnterKey(event: any): void;
    onEscapeKey(event: KeyboardEvent): void;
    onTogglerKeydown(event: KeyboardEvent): void;
    onTogglerArrowUp(event: any): void;
    onTogglerArrowDown(event: any): void;
    navigateNextItem(event: any): void;
    navigatePrevItem(event: any): void;
    findPrevOptionIndex(index: any): number;
    findNextOptionIndex(index: any): number;
    changeFocusedOptionIndex(index: any): void;
    calculatePointStyle(index: number): {
        left: string;
        top: string;
        bottom?: undefined;
        right?: undefined;
    } | {
        left: string;
        bottom: string;
        top?: undefined;
        right?: undefined;
    } | {
        right: string;
        top: string;
        left?: undefined;
        bottom?: undefined;
    } | {
        right: string;
        bottom: string;
        left?: undefined;
        top?: undefined;
    } | {
        left?: undefined;
        top?: undefined;
        bottom?: undefined;
        right?: undefined;
    };
    calculateTransitionDelay(index: number): number;
    get buttonIconClass(): string;
    getItemStyle(index: number): {
        left: string;
        top: string;
        bottom?: undefined;
        right?: undefined;
        transitionDelay: string;
    } | {
        left: string;
        bottom: string;
        top?: undefined;
        right?: undefined;
        transitionDelay: string;
    } | {
        right: string;
        top: string;
        left?: undefined;
        bottom?: undefined;
        transitionDelay: string;
    } | {
        right: string;
        bottom: string;
        left?: undefined;
        top?: undefined;
        transitionDelay: string;
    } | {
        left?: undefined;
        top?: undefined;
        bottom?: undefined;
        right?: undefined;
        transitionDelay: string;
    };
    isClickableRouterLink(item: MenuItem): boolean;
    isOutsideClicked(event: Event): boolean;
    bindDocumentClickListener(): void;
    unbindDocumentClickListener(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SpeedDial, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SpeedDial, "p-speeddial, p-speedDial, p-speed-dial", never, { "id": { "alias": "id"; "required": false; }; "model": { "alias": "model"; "required": false; }; "visible": { "alias": "visible"; "required": false; }; "style": { "alias": "style"; "required": false; }; "className": { "alias": "className"; "required": false; }; "direction": { "alias": "direction"; "required": false; }; "transitionDelay": { "alias": "transitionDelay"; "required": false; }; "type": { "alias": "type"; "required": false; }; "radius": { "alias": "radius"; "required": false; }; "mask": { "alias": "mask"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "hideOnClickOutside": { "alias": "hideOnClickOutside"; "required": false; }; "buttonStyle": { "alias": "buttonStyle"; "required": false; }; "buttonClassName": { "alias": "buttonClassName"; "required": false; }; "maskStyle": { "alias": "maskStyle"; "required": false; }; "maskClassName": { "alias": "maskClassName"; "required": false; }; "showIcon": { "alias": "showIcon"; "required": false; }; "hideIcon": { "alias": "hideIcon"; "required": false; }; "rotateAnimation": { "alias": "rotateAnimation"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "tooltipOptions": { "alias": "tooltipOptions"; "required": false; }; "buttonProps": { "alias": "buttonProps"; "required": false; }; }, { "onVisibleChange": "onVisibleChange"; "visibleChange": "visibleChange"; "onClick": "onClick"; "onShow": "onShow"; "onHide": "onHide"; }, ["buttonTemplate", "itemTemplate", "iconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_transitionDelay: unknown;
    static ngAcceptInputType_radius: unknown;
    static ngAcceptInputType_mask: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_hideOnClickOutside: unknown;
    static ngAcceptInputType_rotateAnimation: unknown;
}
declare class SpeedDialModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SpeedDialModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SpeedDialModule, never, [typeof SpeedDial, typeof i1.SharedModule], [typeof SpeedDial, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SpeedDialModule>;
}

/**
 * Defines valid templates in SpeedDial.
 * @group Templates
 */
interface SpeedDialTemplates {
    /**
     * Custom template of item.
     */
    item(context: {
        /**
         * Data of the item.
         */
        $implicit: MenuItem[];
        /**
         * Index of the item.
         */
        index: number;
        /**
         * Item click function
         */
        toggleCallback: Function;
    }): TemplateRef<{
        $implicit: MenuItem[];
        index: number;
        toggleCallback: Function;
    }>;
    /**
     * Custom template of button.
     */
    button(context: {
        /**
         * Button click function
         */
        toggleCallback: Function;
    }): TemplateRef<{
        toggleCallback: Function;
    }>;
    /**
     * Custom template of icon.
     */
    icon(): TemplateRef<any>;
}

export { SpeedDial, SpeedDialClasses, SpeedDialModule, SpeedDialStyle };
export type { SpeedDialTemplates };
