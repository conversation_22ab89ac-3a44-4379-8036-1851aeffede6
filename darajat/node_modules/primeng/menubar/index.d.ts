import * as i0 from '@angular/core';
import { On<PERSON>nit, OnDestroy, TemplateRef, EventEmitter, AfterContentInit, ElementRef, Renderer2, ChangeDetectorRef, QueryList } from '@angular/core';
import * as rxjs from 'rxjs';
import { Subject, Subscription } from 'rxjs';
import * as i1 from 'primeng/api';
import { MenuItem, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { VoidListener } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

declare class MenuBarStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-menubar-mobile': any;
            'p-menubar-mobile-active': any;
        })[];
        start: string;
        button: string;
        rootList: string;
        item: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => (string | {
            'p-menubar-item-active': any;
            'p-focus': any;
            'p-disabled': any;
        })[];
        itemContent: string;
        itemLink: string;
        itemIcon: string;
        itemLabel: string;
        submenuIcon: string;
        submenu: string;
        separator: string;
        end: string;
    };
    inlineStyles: {
        submenu: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => {
            display: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<MenuBarStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<MenuBarStyle>;
}
/**
 *
 * Menubar is a horizontal menu component.
 *
 * [Live Demo](https://www.primeng.org/menubar/)
 *
 * @module menubarstyle
 *
 */
declare enum MenubarClasses {
    /**
     * Class name of the root element
     */
    root = "p-menubar",
    /**
     * Class name of the start element
     */
    start = "p-menubar-start",
    /**
     * Class name of the button element
     */
    button = "p-menubar-button",
    /**
     * Class name of the root list element
     */
    rootList = "p-menubar-root-list",
    /**
     * Class name of the item element
     */
    item = "p-menubar-item",
    /**
     * Class name of the item content element
     */
    itemContent = "p-menubar-item-content",
    /**
     * Class name of the item link element
     */
    itemLink = "p-menubar-item-link",
    /**
     * Class name of the item icon element
     */
    itemIcon = "p-menubar-item-icon",
    /**
     * Class name of the item label element
     */
    itemLabel = "p-menubar-item-label",
    /**
     * Class name of the submenu icon element
     */
    submenuIcon = "p-menubar-submenu-icon",
    /**
     * Class name of the submenu element
     */
    submenu = "p-menubar-submenu",
    /**
     * Class name of the separator element
     */
    separator = "p-menubar-separator",
    /**
     * Class name of the end element
     */
    end = "p-menubar-end"
}
interface MenubarStyle extends BaseStyle {
}

declare class MenubarService {
    autoHide: boolean | undefined;
    autoHideDelay: number | undefined;
    readonly mouseLeaves: Subject<boolean>;
    readonly mouseLeft$: rxjs.Observable<boolean>;
    static ɵfac: i0.ɵɵFactoryDeclaration<MenubarService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<MenubarService>;
}
declare class MenubarSub extends BaseComponent implements OnInit, OnDestroy {
    items: any[];
    itemTemplate: TemplateRef<any> | undefined;
    root: boolean;
    autoZIndex: boolean;
    baseZIndex: number;
    mobileActive: boolean | undefined;
    autoDisplay: boolean | undefined;
    menuId: string | undefined;
    ariaLabel: string | undefined;
    ariaLabelledBy: string | undefined;
    level: number;
    focusedItemId: string | undefined;
    activeItemPath: any[];
    inlineStyles: any;
    submenuiconTemplate: TemplateRef<any> | undefined;
    itemClick: EventEmitter<any>;
    itemMouseEnter: EventEmitter<any>;
    menuFocus: EventEmitter<any>;
    menuBlur: EventEmitter<any>;
    menuKeydown: EventEmitter<any>;
    mouseLeaveSubscriber: Subscription | undefined;
    menubarService: MenubarService;
    _componentStyle: MenuBarStyle;
    ngOnInit(): void;
    onItemClick(event: any, processedItem: any): void;
    getItemProp(processedItem: any, name: string, params?: any | null): any;
    getItemId(processedItem: any): string;
    getItemLabelId(processedItem: any): string;
    getItemLabel(processedItem: any): string;
    isItemVisible(processedItem: any): boolean;
    isItemActive(processedItem: any): boolean;
    isItemDisabled(processedItem: any): boolean;
    isItemFocused(processedItem: any): boolean;
    isItemGroup(processedItem: any): boolean;
    getAriaSetSize(): number;
    getAriaPosInset(index: number): number;
    onItemMouseEnter(param: any): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<MenubarSub, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<MenubarSub, "p-menubarSub, p-menubarsub, [pMenubarSub]", never, { "items": { "alias": "items"; "required": false; }; "itemTemplate": { "alias": "itemTemplate"; "required": false; }; "root": { "alias": "root"; "required": false; }; "autoZIndex": { "alias": "autoZIndex"; "required": false; }; "baseZIndex": { "alias": "baseZIndex"; "required": false; }; "mobileActive": { "alias": "mobileActive"; "required": false; }; "autoDisplay": { "alias": "autoDisplay"; "required": false; }; "menuId": { "alias": "menuId"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "level": { "alias": "level"; "required": false; }; "focusedItemId": { "alias": "focusedItemId"; "required": false; }; "activeItemPath": { "alias": "activeItemPath"; "required": false; }; "inlineStyles": { "alias": "inlineStyles"; "required": false; }; "submenuiconTemplate": { "alias": "submenuiconTemplate"; "required": false; }; }, { "itemClick": "itemClick"; "itemMouseEnter": "itemMouseEnter"; "menuFocus": "menuFocus"; "menuBlur": "menuBlur"; "menuKeydown": "menuKeydown"; }, never, never, true, never>;
    static ngAcceptInputType_root: unknown;
    static ngAcceptInputType_autoZIndex: unknown;
    static ngAcceptInputType_baseZIndex: unknown;
    static ngAcceptInputType_mobileActive: unknown;
    static ngAcceptInputType_autoDisplay: unknown;
    static ngAcceptInputType_level: unknown;
}
/**
 * Menubar is a horizontal menu component.
 * @group Components
 */
declare class Menubar extends BaseComponent implements AfterContentInit, OnDestroy, OnInit {
    document: Document;
    platformId: any;
    el: ElementRef;
    renderer: Renderer2;
    cd: ChangeDetectorRef;
    private menubarService;
    /**
     * An array of menuitems.
     * @group Props
     */
    set model(value: MenuItem[] | undefined);
    get model(): MenuItem[] | undefined;
    /**
     * Class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Whether to automatically manage layering.
     * @group Props
     */
    autoZIndex: boolean;
    /**
     * Base zIndex value to use in layering.
     * @group Props
     */
    baseZIndex: number;
    /**
     * Whether to show a root submenu on mouse over.
     * @defaultValue true
     * @group Props
     */
    autoDisplay: boolean | undefined;
    /**
     * Whether to hide a root submenu when mouse leaves.
     * @group Props
     */
    autoHide: boolean | undefined;
    /**
     * The breakpoint to define the maximum width boundary.
     * @group Props
     */
    breakpoint: string;
    /**
     * Delay to hide the root submenu in milliseconds when mouse leaves.
     * @group Props
     */
    autoHideDelay: number;
    /**
     * Current id state as a string.
     * @group Props
     */
    id: string | undefined;
    /**
     * Defines a string value that labels an interactive element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Identifier of the underlying input element.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Callback to execute when button is focused.
     * @param {FocusEvent} event - Focus event.
     * @group Emits
     */
    onFocus: EventEmitter<FocusEvent>;
    /**
     * Callback to execute when button loses focus.
     * @param {FocusEvent} event - Focus event.
     * @group Emits
     */
    onBlur: EventEmitter<FocusEvent>;
    menubutton: ElementRef | undefined;
    rootmenu: MenubarSub | undefined;
    mobileActive: boolean | undefined;
    private matchMediaListener;
    private query;
    queryMatches: boolean;
    outsideClickListener: VoidListener;
    resizeListener: VoidListener;
    mouseLeaveSubscriber: Subscription | undefined;
    dirty: boolean;
    focused: boolean;
    activeItemPath: i0.WritableSignal<any>;
    number: i0.WritableSignal<number>;
    focusedItemInfo: i0.WritableSignal<any>;
    searchValue: string;
    searchTimeout: any;
    _processedItems: any[];
    _componentStyle: MenuBarStyle;
    _model: MenuItem[] | undefined;
    get visibleItems(): any;
    get processedItems(): any[];
    get focusedItemId(): any;
    constructor(document: Document, platformId: any, el: ElementRef, renderer: Renderer2, cd: ChangeDetectorRef, menubarService: MenubarService);
    ngOnInit(): void;
    /**
     * Defines template option for start.
     * @group Templates
     */
    startTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for end.
     * @group Templates
     */
    endTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for item.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for item.
     * @group Templates
     */
    menuIconTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for submenu icon.
     * @group Templates
     */
    submenuIconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _startTemplate: TemplateRef<any> | undefined;
    _endTemplate: TemplateRef<any> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    _menuIconTemplate: TemplateRef<any> | undefined;
    _submenuIconTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    createProcessedItems(items: any, level?: number, parent?: any, parentKey?: any): any[];
    bindMatchMediaListener(): void;
    unbindMatchMediaListener(): void;
    getItemProp(item: any, name: string): any;
    menuButtonClick(event: MouseEvent): void;
    menuButtonKeydown(event: any): void;
    onItemClick(event: any): void;
    onItemMouseEnter(event: any): void;
    onMouseLeave(event: any): void;
    changeFocusedItemIndex(event: any, index: number): void;
    scrollInView(index?: number): void;
    onItemChange(event: any, type?: string | undefined): void;
    toggle(event: MouseEvent): void;
    hide(event?: any, isFocus?: boolean): void;
    show(): void;
    onMenuFocus(event: any): void;
    onMenuBlur(event: any): void;
    onKeyDown(event: KeyboardEvent): void;
    findVisibleItem(index: any): any;
    findFirstFocusedItemIndex(): any;
    findFirstItemIndex(): any;
    findSelectedItemIndex(): any;
    isProcessedItemGroup(processedItem: any): boolean;
    isSelected(processedItem: any): boolean;
    isValidSelectedItem(processedItem: any): boolean;
    isValidItem(processedItem: any): boolean;
    isItemDisabled(item: any): boolean;
    isItemSeparator(item: any): boolean;
    isItemMatched(processedItem: any): boolean;
    isProccessedItemGroup(processedItem: any): boolean;
    searchItems(event: any, char: string): boolean;
    getProccessedItemLabel(processedItem: any): any;
    getItemLabel(item: any): any;
    onArrowDownKey(event: KeyboardEvent): void;
    onArrowRightKey(event: KeyboardEvent): void;
    onArrowUpKey(event: KeyboardEvent): void;
    onArrowLeftKey(event: KeyboardEvent): void;
    onHomeKey(event: KeyboardEvent): void;
    onEndKey(event: KeyboardEvent): void;
    onSpaceKey(event: KeyboardEvent): void;
    onEscapeKey(event: KeyboardEvent): void;
    onTabKey(event: KeyboardEvent): void;
    onEnterKey(event: KeyboardEvent): void;
    findLastFocusedItemIndex(): any;
    findLastItemIndex(): number;
    findPrevItemIndex(index: number): number;
    findNextItemIndex(index: number): any;
    bindResizeListener(): void;
    bindOutsideClickListener(): void;
    unbindOutsideClickListener(): void;
    unbindResizeListener(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Menubar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Menubar, "p-menubar", never, { "model": { "alias": "model"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "autoZIndex": { "alias": "autoZIndex"; "required": false; }; "baseZIndex": { "alias": "baseZIndex"; "required": false; }; "autoDisplay": { "alias": "autoDisplay"; "required": false; }; "autoHide": { "alias": "autoHide"; "required": false; }; "breakpoint": { "alias": "breakpoint"; "required": false; }; "autoHideDelay": { "alias": "autoHideDelay"; "required": false; }; "id": { "alias": "id"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; }, { "onFocus": "onFocus"; "onBlur": "onBlur"; }, ["startTemplate", "endTemplate", "itemTemplate", "menuIconTemplate", "submenuIconTemplate", "templates"], ["*"], true, never>;
    static ngAcceptInputType_autoZIndex: unknown;
    static ngAcceptInputType_baseZIndex: unknown;
    static ngAcceptInputType_autoDisplay: unknown;
    static ngAcceptInputType_autoHide: unknown;
    static ngAcceptInputType_autoHideDelay: unknown;
}
declare class MenubarModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MenubarModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MenubarModule, never, [typeof Menubar, typeof i1.SharedModule], [typeof Menubar, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MenubarModule>;
}

/**
 * Defines valid templates in Menubar.
 * @group Templates
 */
interface MenubarTemplates {
    /**
     * Custom item template.
     */
    item(context: {
        /**
         * Item instance.
         */
        $implicit: MenuItem;
        /**
         * Whether root or not
         */
        root: boolean;
    }): TemplateRef<{
        $implicit: MenuItem;
        root: boolean;
    }>;
    /**
     * Custom template of start.
     */
    start(): TemplateRef<any>;
    /**
     * Custom template of end.
     */
    end(): TemplateRef<any>;
    /**
     * Custom template of menuicon.
     */
    menuicon(): TemplateRef<any>;
    /**
     * Custom template of submenuicon.
     */
    submenuicon(): TemplateRef<any>;
}

export { MenuBarStyle, Menubar, MenubarClasses, MenubarModule, MenubarService, MenubarSub };
export type { MenubarStyle, MenubarTemplates };
