import * as i0 from '@angular/core';
import { TemplateRef, OnInit, AfterContentInit, AfterViewChecked, OnDestroy, EventEmitter, ElementRef, QueryList, NgZone, SimpleChanges } from '@angular/core';
import * as i1 from 'primeng/api';
import { ScrollerOptions, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 * Options of the scroll direction.
 * @group Types
 */
type ScrollerToType = 'to-start' | 'to-end' | undefined;
/**
 * Options of the scroller orientation.
 * @group Types
 */
type ScrollerOrientationType = 'vertical' | 'horizontal' | 'both';
/**
 * Loader icon options.
 * @group Types
 */
interface ScrollerLoaderIconOptions {
    [klass: string]: any;
}
/**
 * Scroller content options.
 * @group Interface
 */
interface ScrollerContentOptions {
    contentStyleClass?: string;
    items?: any[];
    loading?: boolean;
    itemSize?: number;
    rows?: any[];
    columns?: any[];
    spacerStyle?: {
        [klass: string]: any;
    } | null | undefined;
    contentStyle?: {
        [klass: string]: any;
    } | null | undefined;
    vertical?: boolean;
    horizontal?: boolean;
    both?: boolean;
    getItemOptions?: (index: number) => ScrollerItemOptions;
    getLoaderOptions?: (index: number, options?: any) => ScrollerLoaderOptions;
}
/**
 * Scroller item options.
 * @group Interface
 */
interface ScrollerItemOptions {
    /**
     * Index of the item.
     */
    index?: number;
    /**
     * Item count.
     */
    count?: number;
    /**
     * Index of the first element in viewport.
     */
    first?: boolean;
    /**
     * Index of the last element in viewport.
     */
    last?: boolean;
    /**
     * Defines if index is even number.
     */
    even?: boolean;
    /**
     * Defines if index is odd number.
     */
    odd?: boolean;
}
/**
 * Loader settings.
 * @extends {ScrollerItemOptions}
 * @group Interface
 */
interface ScrollerLoaderOptions extends ScrollerItemOptions {
    [klass: string]: any;
}
/**
 * Custom lazy load event.
 * @see {@link Scroller.onLazyLoad}
 * @group Events
 */
interface ScrollerLazyLoadEvent {
    /**
     * First element index in viewport.
     */
    first: number;
    /**
     * Last element index in viewport.
     */
    last: number;
}
/**
 * Custom scroll index change event.
 * @see {@link Scroller.onScrollIndexChange}
 * @extends {ScrollerLazyLoadEvent}
 * @group Events
 */
interface ScrollerScrollIndexChangeEvent extends ScrollerLazyLoadEvent {
}
/**
 * Custom scroll event.
 * @see {@link Scroller.onScroll}
 * @group Events
 */
interface ScrollerScrollEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
}
/**
 * Defines valid templates in Scroller.
 * @group Templates
 */
interface ScrollerTemplates {
    /**
     * Custom content template.
     * @param {Object} context - Content data.
     */
    content(context: {
        /**
         * Loaded items.
         */
        $implicit: any[] | any | null | undefined;
        /**
         *
         */
        options: ScrollerContentOptions;
    }): TemplateRef<{
        $implicit: any[] | any | null | undefined;
        options: ScrollerContentOptions;
    }>;
    /**
     * Custom item template.
     * @param {Object} context - Item data.
     */
    item(context: {
        /**
         * Item instance.
         */
        $implicit: any;
        /**
         * Scroller item options.
         */
        options: ScrollerItemOptions;
    }): TemplateRef<{
        $implicit: any;
        options: ScrollerItemOptions;
    }>;
    /**
     * Custom loader template.
     * @param {Object} context - Options instance.
     */
    loader(context: {
        /**
         * Options.
         */
        options: ScrollerLoaderOptions;
    }): TemplateRef<{
        options: ScrollerLoaderOptions;
    }>;
    /**
     * Custom loader icon template.
     * @param {Object} context - Icon options.
     */
    loadericon(context: {
        /**
         * Options.
         */
        options: ScrollerLoaderIconOptions;
    }): TemplateRef<{
        options: ScrollerLoaderIconOptions;
    }>;
}

/**
 *
 * VirtualScroller is a performant approach to handle huge data efficiently.
 *
 * [Live Demo](https://www.primeng.org/scroller/)
 *
 * @module scrollerstyle
 *
 */
declare enum ScrollerClasses {
    /**
     * Class name of the root element
     */
    root = "p-virtualscroller",
    /**
     * Class name of the content element
     */
    content = "p-virtualscroller-content",
    /**
     * Class name of the spacer element
     */
    spacer = "p-virtualscroller-spacer",
    /**
     * Class name of the loader element
     */
    loader = "p-virtualscroller-loader",
    /**
     * Class name of the loading icon element
     */
    loadingIcon = "p-virtualscroller-loading-icon"
}
declare class ScrollerStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-virtualscroller-inline': any;
            'p-virtualscroller-both p-both-scroll': any;
            'p-virtualscroller-horizontal p-horizontal-scroll': any;
        })[];
        content: string;
        spacer: string;
        loader: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-virtualscroller-loader-mask': boolean;
        })[];
        loadingIcon: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollerStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ScrollerStyle>;
}
interface ScrollerStyle extends BaseStyle {
}

/**
 * Scroller is a performance-approach to handle huge data efficiently.
 * @group Components
 */
declare class Scroller extends BaseComponent implements OnInit, AfterContentInit, AfterViewChecked, OnDestroy {
    private zone;
    /**
     * Unique identifier of the element.
     * @group Props
     */
    get id(): string | undefined;
    set id(val: string | undefined);
    /**
     * Inline style of the component.
     * @group Props
     */
    get style(): any;
    set style(val: any);
    /**
     * Style class of the element.
     * @group Props
     */
    get styleClass(): string | undefined;
    set styleClass(val: string | undefined);
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    get tabindex(): number;
    set tabindex(val: number);
    /**
     * An array of objects to display.
     * @group Props
     */
    get items(): any[] | undefined | null;
    set items(val: any[] | undefined | null);
    /**
     * The height/width of item according to orientation.
     * @group Props
     */
    get itemSize(): number[] | number;
    set itemSize(val: number[] | number);
    /**
     * Height of the scroll viewport.
     * @group Props
     */
    get scrollHeight(): string | undefined;
    set scrollHeight(val: string | undefined);
    /**
     * Width of the scroll viewport.
     * @group Props
     */
    get scrollWidth(): string | undefined;
    set scrollWidth(val: string | undefined);
    /**
     * The orientation of scrollbar.
     * @group Props
     */
    get orientation(): 'vertical' | 'horizontal' | 'both';
    set orientation(val: 'vertical' | 'horizontal' | 'both');
    /**
     * Used to specify how many items to load in each load method in lazy mode.
     * @group Props
     */
    get step(): number;
    set step(val: number);
    /**
     * Delay in scroll before new data is loaded.
     * @group Props
     */
    get delay(): number;
    set delay(val: number);
    /**
     * Delay after window's resize finishes.
     * @group Props
     */
    get resizeDelay(): number;
    set resizeDelay(val: number);
    /**
     * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.
     * @group Props
     */
    get appendOnly(): boolean;
    set appendOnly(val: boolean);
    /**
     * Specifies whether the scroller should be displayed inline or not.
     * @group Props
     */
    get inline(): boolean;
    set inline(val: boolean);
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    get lazy(): boolean;
    set lazy(val: boolean);
    /**
     * If disabled, the scroller feature is eliminated and the content is displayed directly.
     * @group Props
     */
    get disabled(): boolean;
    set disabled(val: boolean);
    /**
     * Used to implement a custom loader instead of using the loader feature in the scroller.
     * @group Props
     */
    get loaderDisabled(): boolean;
    set loaderDisabled(val: boolean);
    /**
     * Columns to display.
     * @group Props
     */
    get columns(): any[] | undefined | null;
    set columns(val: any[] | undefined | null);
    /**
     * Used to implement a custom spacer instead of using the spacer feature in the scroller.
     * @group Props
     */
    get showSpacer(): boolean;
    set showSpacer(val: boolean);
    /**
     * Defines whether to show loader.
     * @group Props
     */
    get showLoader(): boolean;
    set showLoader(val: boolean);
    /**
     * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.
     * @group Props
     */
    get numToleratedItems(): number;
    set numToleratedItems(val: number);
    /**
     * Defines whether the data is loaded.
     * @group Props
     */
    get loading(): boolean | undefined;
    set loading(val: boolean | undefined);
    /**
     * Defines whether to dynamically change the height or width of scrollable container.
     * @group Props
     */
    get autoSize(): boolean;
    set autoSize(val: boolean);
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.
     * @group Props
     */
    get trackBy(): Function;
    set trackBy(val: Function);
    /**
     * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    get options(): ScrollerOptions | undefined;
    set options(val: ScrollerOptions | undefined);
    /**
     * Callback to invoke in lazy mode to load new data.
     * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<ScrollerLazyLoadEvent>;
    /**
     * Callback to invoke when scroll position changes.
     * @param {ScrollerScrollEvent} event - Custom scroll event.
     * @group Emits
     */
    onScroll: EventEmitter<ScrollerScrollEvent>;
    /**
     * Callback to invoke when scroll position and item's range in view changes.
     * @param {ScrollerScrollEvent} event - Custom scroll index change event.
     * @group Emits
     */
    onScrollIndexChange: EventEmitter<ScrollerScrollIndexChangeEvent>;
    elementViewChild: Nullable<ElementRef>;
    contentViewChild: Nullable<ElementRef>;
    height: string;
    _id: string | undefined;
    _style: {
        [klass: string]: any;
    } | null | undefined;
    _styleClass: string | undefined;
    _tabindex: number;
    _items: any[] | undefined | null;
    _itemSize: number | number[];
    _scrollHeight: string | undefined;
    _scrollWidth: string | undefined;
    _orientation: 'vertical' | 'horizontal' | 'both';
    _step: number;
    _delay: number;
    _resizeDelay: number;
    _appendOnly: boolean;
    _inline: boolean;
    _lazy: boolean;
    _disabled: boolean;
    _loaderDisabled: boolean;
    _columns: any[] | undefined | null;
    _showSpacer: boolean;
    _showLoader: boolean;
    _numToleratedItems: any;
    _loading: boolean | undefined;
    _autoSize: boolean;
    _trackBy: any;
    _options: ScrollerOptions | undefined;
    d_loading: boolean;
    d_numToleratedItems: any;
    contentEl: any;
    /**
     * Content template of the component.
     * @group Templates
     */
    contentTemplate: Nullable<TemplateRef<any>>;
    /**
     * Item template of the component.
     * @group Templates
     */
    itemTemplate: Nullable<TemplateRef<any>>;
    /**
     * Loader template of the component.
     * @group Templates
     */
    loaderTemplate: Nullable<TemplateRef<any>>;
    /**
     * Loader icon template of the component.
     * @group Templates
     */
    loaderIconTemplate: Nullable<TemplateRef<any>>;
    templates: Nullable<QueryList<PrimeTemplate>>;
    _contentTemplate: TemplateRef<any> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    _loaderTemplate: TemplateRef<any> | undefined;
    _loaderIconTemplate: TemplateRef<any> | undefined;
    first: any;
    last: any;
    page: number;
    isRangeChanged: boolean;
    numItemsInViewport: any;
    lastScrollPos: any;
    lazyLoadState: any;
    loaderArr: any[];
    spacerStyle: {
        [klass: string]: any;
    } | null | undefined;
    contentStyle: {
        [klass: string]: any;
    } | null | undefined;
    scrollTimeout: any;
    resizeTimeout: any;
    initialized: boolean;
    windowResizeListener: VoidListener;
    defaultWidth: number | undefined;
    defaultHeight: number | undefined;
    defaultContentWidth: number | undefined;
    defaultContentHeight: number | undefined;
    _contentStyleClass: any;
    get contentStyleClass(): any;
    set contentStyleClass(val: any);
    get vertical(): boolean;
    get horizontal(): boolean;
    get both(): boolean;
    get loadedItems(): any[];
    get loadedRows(): any[];
    get loadedColumns(): any;
    _componentStyle: ScrollerStyle;
    constructor(zone: NgZone);
    ngOnInit(): void;
    ngOnChanges(simpleChanges: SimpleChanges): void;
    ngAfterContentInit(): void;
    ngAfterViewInit(): void;
    ngAfterViewChecked(): void;
    ngOnDestroy(): void;
    viewInit(): void;
    init(): void;
    setContentEl(el?: HTMLElement): void;
    setInitialState(): void;
    getElementRef(): ElementRef<any>;
    getPageByFirst(first?: any): number;
    isPageChanged(first?: any): boolean;
    scrollTo(options: ScrollToOptions): void;
    scrollToIndex(index: number | number[], behavior?: ScrollBehavior): void;
    scrollInView(index: number, to: ScrollerToType, behavior?: ScrollBehavior): void;
    getRenderedRange(): {
        first: any;
        last: any;
        viewport: {
            first: any;
            last: any;
        };
    };
    calculateNumItems(): {
        numItemsInViewport: any;
        numToleratedItems: any;
    };
    calculateOptions(): void;
    calculateAutoSize(): void;
    getLast(last?: number, isCols?: boolean): number;
    getContentPosition(): {
        left: number;
        right: number;
        top: number;
        bottom: number;
        x: number;
        y: number;
    };
    setSize(): void;
    setSpacerSize(): void;
    setContentPosition(pos: any): void;
    onScrollPositionChange(event: Event): {
        first: number | {
            rows: number;
            cols: number;
        };
        last: any;
        isRangeChanged: boolean;
        scrollPos: any;
    };
    onScrollChange(event: Event): void;
    onContainerScroll(event: Event): void;
    bindResizeListener(): void;
    unbindResizeListener(): void;
    onWindowResize(): void;
    handleEvents(name: string, params: any): any;
    getContentOptions(): {
        contentStyleClass: string;
        items: any[];
        getItemOptions: (index: number) => {
            index: any;
            count: number;
            first: boolean;
            last: boolean;
            even: boolean;
            odd: boolean;
        };
        loading: boolean;
        getLoaderOptions: (index: number, options?: any) => any;
        itemSize: number | number[];
        rows: any[];
        columns: any;
        spacerStyle: {
            [klass: string]: any;
        };
        contentStyle: {
            [klass: string]: any;
        };
        vertical: boolean;
        horizontal: boolean;
        both: boolean;
    };
    getOptions(renderedIndex: number): {
        index: any;
        count: number;
        first: boolean;
        last: boolean;
        even: boolean;
        odd: boolean;
    };
    getLoaderOptions(index: number, extOptions: any): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<Scroller, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Scroller, "p-scroller, p-virtualscroller, p-virtual-scroller, p-virtualScroller", never, { "id": { "alias": "id"; "required": false; }; "style": { "alias": "style"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "items": { "alias": "items"; "required": false; }; "itemSize": { "alias": "itemSize"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "scrollWidth": { "alias": "scrollWidth"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; "step": { "alias": "step"; "required": false; }; "delay": { "alias": "delay"; "required": false; }; "resizeDelay": { "alias": "resizeDelay"; "required": false; }; "appendOnly": { "alias": "appendOnly"; "required": false; }; "inline": { "alias": "inline"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "loaderDisabled": { "alias": "loaderDisabled"; "required": false; }; "columns": { "alias": "columns"; "required": false; }; "showSpacer": { "alias": "showSpacer"; "required": false; }; "showLoader": { "alias": "showLoader"; "required": false; }; "numToleratedItems": { "alias": "numToleratedItems"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "autoSize": { "alias": "autoSize"; "required": false; }; "trackBy": { "alias": "trackBy"; "required": false; }; "options": { "alias": "options"; "required": false; }; }, { "onLazyLoad": "onLazyLoad"; "onScroll": "onScroll"; "onScrollIndexChange": "onScrollIndexChange"; }, ["contentTemplate", "itemTemplate", "loaderTemplate", "loaderIconTemplate", "templates"], ["*"], true, never>;
}
declare class ScrollerModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollerModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ScrollerModule, never, [typeof Scroller, typeof i1.SharedModule], [typeof Scroller, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ScrollerModule>;
}

export { Scroller, ScrollerClasses, ScrollerModule, ScrollerStyle };
export type { ScrollerContentOptions, ScrollerItemOptions, ScrollerLazyLoadEvent, ScrollerLoaderIconOptions, ScrollerLoaderOptions, ScrollerOrientationType, ScrollerScrollEvent, ScrollerScrollIndexChangeEvent, ScrollerTemplates, ScrollerToType };
