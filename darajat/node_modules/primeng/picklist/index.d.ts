import { CdkDragDrop } from '@angular/cdk/drag-drop';
import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, EventEmitter, ElementRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { FilterService, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { ButtonProps } from 'primeng/button';
import { Listbox, ListboxChangeEvent } from 'primeng/listbox';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 * Callbacks to invoke on filter.
 * @group Interface
 */
interface PickListFilterOptions {
    filter?: (value?: any) => void;
    reset?: () => void;
}
/**
 * Custom move to source event.
 * @see {@link PickList.onMoveToSource}
 * @group Events
 */
interface PickListMoveToSourceEvent {
    /**
     * Moved items.
     */
    items: any[];
}
/**
 * Custom move all to source event.
 * @see {@link PickList.onMoveAllToSource}
 * @extends {PickListMoveToSourceEvent}
 * @group Events
 */
interface PickListMoveAllToSourceEvent extends PickListMoveToSourceEvent {
}
/**
 * Custom move all to target event.
 * @see {@link PickList.onMoveAllToTarget}
 * @extends {PickListMoveToSourceEvent}
 * @group Events
 */
interface PickListMoveAllToTargetEvent extends PickListMoveToSourceEvent {
}
/**
 * Custom move to target event.
 * @see {@link PickList.onMoveToTarget}
 * @extends {PickListMoveToSourceEvent}
 * @group Events
 */
interface PickListMoveToTargetEvent extends PickListMoveToSourceEvent {
}
/**
 * Custom move source reorder event.
 * @see {@link PickList.onSourceReorder}
 * @extends {PickListMoveToSourceEvent}
 * @group Events
 */
interface PickListSourceReorderEvent extends PickListMoveToSourceEvent {
}
/**
 * Custom move target reorder event.
 * @see {@link PickList.onTargetReorder}
 * @extends {PickListMoveToSourceEvent}
 * @group Events
 */
interface PickListTargetReorderEvent extends PickListMoveToSourceEvent {
}
/**
 * Custom source select event.
 * @see {@link PickList.onSourceSelect}
 * @group Events
 */
interface PickListSourceSelectEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Selected items.
     */
    items: any[];
}
/**
 * Custom target select event.
 * @see {@link PickList.onTargetSelect}
 * @extends {PickListSourceSelectEvent}
 * @group Events
 */
interface PickListTargetSelectEvent extends PickListSourceSelectEvent {
}
/**
 * Custom source filter event.
 * @see {@link PickList.onSourceFilter}
 * @group Events
 */
interface PickListSourceFilterEvent {
    /**
     * Filter value.
     */
    query: string | null | undefined;
    /**
     * Filtered items.
     */
    value: any[] | null | undefined;
}
/**
 * Custom target filter event.
 * @see {@link PickList.onTargetFilter}
 * @extends {PickListSourceFilterEvent}
 * @group Events
 */
interface PickListTargetFilterEvent extends PickListSourceFilterEvent {
}
/**
 * Defines valid templates in PickList.
 * @group Templates
 */
interface PickListTemplates {
    /**
     * Custom item template.
     */
    item(context: {
        /**
         * Item instance.
         */
        $implicit: any;
        /**
         * Item index.
         */
        index: number;
    }): TemplateRef<{
        $implicit: any;
        index: number;
    }>;
    /**
     * Custom source header template.
     */
    sourceHeader(): TemplateRef<any>;
    /**
     * Custom target header template.
     */
    targetHeader(): TemplateRef<any>;
    /**
     * Custom source filter template.
     */
    sourceFilter(context: {
        /**
         * Source filter options.
         */
        options: PickListFilterOptions;
    }): TemplateRef<{
        options: PickListFilterOptions;
    }>;
    /**
     * Custom target filter template.
     */
    targetFilter(context: {
        /**
         * Target filter options.
         */
        options: PickListFilterOptions;
    }): TemplateRef<{
        options: PickListFilterOptions;
    }>;
    /**
     * Custom source list empty message template.
     */
    emptymessagesource(): TemplateRef<any>;
    /**
     * Custom source list empty filter message template.
     */
    emptyfiltermessagesource(): TemplateRef<any>;
    /**
     * Custom target list empty message template.
     */
    emptymessagetarget(): TemplateRef<any>;
    /**
     * Custom target list empty filter message template.
     */
    emptyfiltermessagetarget(): TemplateRef<any>;
    /**
     * Custom move up icon template.
     */
    moveupicon(): TemplateRef<any>;
    /**
     * Custom move top icon template.
     */
    movetopicon(): TemplateRef<any>;
    /**
     * Custom move down icon template.
     */
    movedownicon(): TemplateRef<any>;
    /**
     * Custom move bottom icon template.
     */
    movebottomicon(): TemplateRef<any>;
    /**
     * Custom move to target icon template.
     */
    movetotargeticon(context: {
        /**
         * Boolean value indicates if the view is changed according to breakpoints.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move all to target icon template.
     */
    movealltotargeticon(context: {
        /**
         * Boolean value indicates if the view is changed according to breakpoints.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move to source icon template.
     */
    movetosourceicon(context: {
        /**
         * Boolean value indicates if the view is changed according to breakpoints.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move all to source icon template.
     */
    movealltosourceicon(context: {
        /**
         * Boolean value indicates if the view is changed according to breakpoints.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom target filter icon template.
     */
    targetfiltericon(): TemplateRef<any>;
    /**
     * Custom source filter icon template.
     */
    sourcefiltericon(): TemplateRef<any>;
}

/**
 *
 * PickList is used to reorder items between different lists.
 *
 * [Live Demo](https://www.primeng.org/picklist)
 *
 * @module pickliststyle
 *
 */
declare enum PickListClasses {
    /**
     * Class name of the root element
     */
    root = "p-picklist",
    /**
     * Class name of the source controls element
     */
    sourceControls = "p-picklist-source-controls",
    /**
     * Class name of the source list container element
     */
    sourceListContainer = "p-picklist-source-list-container",
    /**
     * Class name of the transfer controls element
     */
    transferControls = "p-picklist-transfer-controls",
    /**
     * Class name of the target list container element
     */
    targetListContainer = "p-picklist-target-list-container",
    /**
     * Class name of the target controls element
     */
    targetControls = "p-picklist-target-controls"
}
declare class PickListStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: () => string[];
        sourceControls: string;
        sourceListContainer: string;
        transferControls: string;
        targetListContainer: string;
        targetControls: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<PickListStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<PickListStyle>;
}
interface PickListStyle extends BaseStyle {
}

/**
 * PickList is used to reorder items between different lists.
 * @group Components
 */
declare class PickList extends BaseComponent implements AfterContentInit {
    /**
     * An array of objects for the source list.
     * @group Props
     */
    source: any[] | undefined;
    /**
     * An array of objects for the target list.
     * @group Props
     */
    target: any[] | undefined;
    /**
     * Text for the source list caption
     * @group Props
     */
    sourceHeader: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * Defines a string that labels the move to right button for accessibility.
     * @group Props
     */
    rightButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to left button for accessibility.
     * @group Props
     */
    leftButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to all right button for accessibility.
     * @group Props
     */
    allRightButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to all left button for accessibility.
     * @group Props
     */
    allLeftButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to up button for accessibility.
     * @group Props
     */
    upButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to down button for accessibility.
     * @group Props
     */
    downButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to top button for accessibility.
     * @group Props
     */
    topButtonAriaLabel: string | undefined;
    /**
     * Defines a string that labels the move to bottom button for accessibility.
     * @group Props
     */
    bottomButtonAriaLabel: string | undefined;
    /**
     * Text for the target list caption
     * @group Props
     */
    targetHeader: string | undefined;
    /**
     * When enabled orderlist adjusts its controls based on screen size.
     * @group Props
     */
    responsive: boolean | undefined;
    /**
     * When specified displays an input field to filter the items on keyup and decides which field to search (Accepts multiple fields with a comma).
     * @group Props
     */
    filterBy: string | undefined;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity. Use sourceTrackBy or targetTrackBy in case different algorithms are needed per list.
     * @group Props
     */
    trackBy: Function;
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy in source list, default algorithm checks for object identity.
     * @group Props
     */
    sourceTrackBy: Function | undefined;
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy in target list, default algorithm checks for object identity.
     * @group Props
     */
    targetTrackBy: Function | undefined;
    /**
     * Whether to show filter input for source list when filterBy is enabled.
     * @group Props
     */
    showSourceFilter: boolean;
    /**
     * Whether to show filter input for target list when filterBy is enabled.
     * @group Props
     */
    showTargetFilter: boolean;
    /**
     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean;
    /**
     * Whether to enable dragdrop based reordering.
     * @group Props
     */
    dragdrop: boolean;
    /**
     * Inline style of the component.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the component.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the source list element.
     * @group Props
     */
    sourceStyle: any;
    /**
     * Inline style of the target list element.
     * @group Props
     */
    targetStyle: any;
    /**
     * Whether to show buttons of source list.
     * @group Props
     */
    showSourceControls: boolean;
    /**
     * Whether to show buttons of target list.
     * @group Props
     */
    showTargetControls: boolean;
    /**
     * Placeholder text on source filter input.
     * @group Props
     */
    sourceFilterPlaceholder: string | undefined;
    /**
     * Placeholder text on target filter input.
     * @group Props
     */
    targetFilterPlaceholder: string | undefined;
    /**
     * When present, it specifies that the component should be disabled.
     * @group Props
     */
    disabled: boolean;
    /**
     * Name of the disabled field of a target option or function to determine disabled state.
     * @group Props
     */
    sourceOptionDisabled: string | ((item: any) => boolean) | undefined;
    /**
     * Name of the disabled field of a target option or function to determine disabled state.
     * @group Props
     */
    targetOptionDisabled: string | ((item: any) => boolean) | undefined;
    /**
     * Defines a string that labels the filter input of source list.
     * @group Props
     */
    ariaSourceFilterLabel: string | undefined;
    /**
     * Defines a string that labels the filter input of target list.
     * @group Props
     */
    ariaTargetFilterLabel: string | undefined;
    /**
     * Defines how the items are filtered.
     * @group Props
     */
    filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte' | string;
    /**
     * Whether to displays rows with alternating colors.
     * @group Props
     */
    stripedRows: boolean | undefined;
    /**
     * Keeps selection on the transfer list.
     * @group Props
     */
    keepSelection: boolean;
    /**
     * Height of the viewport, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * Whether to focus on the first visible or selected element.
     * @group Props
     */
    autoOptionFocus: boolean;
    /**
     * Used to pass all properties of the ButtonProps to the Button component.
     * @group Props
     */
    buttonProps: ButtonProps;
    /**
     * Used to pass all properties of the ButtonProps to the move up button inside the component.
     * @group Props
     */
    moveUpButtonProps: ButtonProps;
    /**
     * 	Used to pass all properties of the ButtonProps to the move top button inside the component.
     * @group Props
     */
    moveTopButtonProps: ButtonProps;
    /**
     * 	Used to pass all properties of the ButtonProps to the move down button inside the component.
     * @group Props
     */
    moveDownButtonProps: ButtonProps;
    /**
     * 	Used to pass all properties of the ButtonProps to the move bottom button inside the component.
     * @group Props
     */
    moveBottomButtonProps: ButtonProps;
    /**
     * 	Used to pass all properties of the ButtonProps to the move to target button inside the component.
     * @group Props
     */
    moveToTargetProps: ButtonProps;
    /**
     * 	Used to pass all properties of the ButtonProps to the move all to target button inside the component.
     * @group Props
     */
    moveAllToTargetProps: ButtonProps;
    /**
     *  Used to pass all properties of the ButtonProps to the move to source button inside the component.
     * @group Props
     */
    moveToSourceProps: ButtonProps;
    /**
     *  Used to pass all properties of the ButtonProps to the move all to source button inside the component.
     * @group Props
     */
    moveAllToSourceProps: ButtonProps;
    /**
     * Indicates the width of the screen at which the component should change its behavior.
     * @group Props
     */
    get breakpoint(): string;
    set breakpoint(value: string);
    /**
     * Callback to invoke when items are moved from target to source.
     * @param {PickListMoveToSourceEvent} event - Custom move to source event.
     * @group Emits
     */
    onMoveToSource: EventEmitter<PickListMoveToSourceEvent>;
    /**
     * Callback to invoke when all items are moved from target to source.
     * @param {PickListMoveAllToSourceEvent} event - Custom move all to source event.
     * @group Emits
     */
    onMoveAllToSource: EventEmitter<PickListMoveAllToSourceEvent>;
    /**
     * Callback to invoke when all items are moved from source to target.
     * @param {PickListMoveAllToTargetEvent} event - Custom move all to target event.
     * @group Emits
     */
    onMoveAllToTarget: EventEmitter<PickListMoveAllToTargetEvent>;
    /**
     * Callback to invoke when items are moved from source to target.
     * @param {PickListMoveToTargetEvent} event - Custom move to target event.
     * @group Emits
     */
    onMoveToTarget: EventEmitter<PickListMoveToTargetEvent>;
    /**
     * Callback to invoke when items are reordered within source list.
     * @param {PickListSourceReorderEvent} event - Custom source reorder event.
     * @group Emits
     */
    onSourceReorder: EventEmitter<PickListSourceReorderEvent>;
    /**
     * Callback to invoke when items are reordered within target list.
     * @param {PickListTargetReorderEvent} event - Custom target reorder event.
     * @group Emits
     */
    onTargetReorder: EventEmitter<PickListTargetReorderEvent>;
    /**
     * Callback to invoke when items are selected within source list.
     * @param {PickListSourceSelectEvent} event - Custom source select event.
     * @group Emits
     */
    onSourceSelect: EventEmitter<PickListSourceSelectEvent>;
    /**
     * Callback to invoke when items are selected within target list.
     * @param {PickListTargetSelectEvent} event - Custom target select event.
     * @group Emits
     */
    onTargetSelect: EventEmitter<PickListTargetSelectEvent>;
    /**
     * Callback to invoke when the source list is filtered
     * @param {PickListSourceFilterEvent} event - Custom source filter event.
     * @group Emits
     */
    onSourceFilter: EventEmitter<PickListSourceFilterEvent>;
    /**
     * Callback to invoke when the target list is filtered
     * @param {PickListTargetFilterEvent} event - Custom target filter event.
     * @group Emits
     */
    onTargetFilter: EventEmitter<PickListTargetFilterEvent>;
    /**
     * Callback to invoke when the list is focused
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the list is blurred
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    listViewSourceChild: Listbox;
    listViewTargetChild: Listbox;
    sourceFilterViewChild: Nullable<ElementRef>;
    targetFilterViewChild: Nullable<ElementRef>;
    getButtonProps(direction: string): ButtonProps;
    get moveUpAriaLabel(): string;
    get moveTopAriaLabel(): string;
    get moveDownAriaLabel(): string;
    get moveBottomAriaLabel(): string;
    get moveToTargetAriaLabel(): string;
    get moveAllToTargetAriaLabel(): string;
    get moveToSourceAriaLabel(): string;
    get moveAllToSourceAriaLabel(): string;
    get idSource(): string;
    get idTarget(): string;
    _breakpoint: string;
    visibleOptionsSource: any[] | undefined | null;
    visibleOptionsTarget: any[] | undefined | null;
    selectedItemsSource: any[];
    selectedItemsTarget: any[];
    reorderedListElement: any;
    movedUp: Nullable<boolean>;
    movedDown: Nullable<boolean>;
    itemTouched: Nullable<boolean>;
    styleElement: any;
    id: string;
    filterValueSource: Nullable<string>;
    filterValueTarget: Nullable<string>;
    fromListType: Nullable<number>;
    sourceFilterOptions: Nullable<PickListFilterOptions>;
    targetFilterOptions: Nullable<PickListFilterOptions>;
    readonly SOURCE_LIST: number;
    readonly TARGET_LIST: number;
    window: Window;
    media: MediaQueryList | null | undefined;
    viewChanged: boolean | undefined;
    _componentStyle: PickListStyle;
    mediaChangeListener: VoidListener;
    filterService: FilterService;
    ngOnInit(): void;
    /**
     * Custom item template.
     * @group Templates
     */
    itemTemplate: TemplateRef<any>;
    /**
     * Custom source header template.
     * @group Templates
     */
    sourceHeaderTemplate: TemplateRef<any>;
    /**
     * Custom target header template.
     * @group Templates
     */
    targetHeaderTemplate: TemplateRef<any>;
    /**
     * Custom source filter template.
     * @group Templates
     */
    sourceFilterTemplate: TemplateRef<{
        options: PickListFilterOptions;
    }>;
    /**
     * Custom target filter template.
     * @group Templates
     */
    targetFilterTemplate: TemplateRef<{
        options: PickListFilterOptions;
    }>;
    /**
     * Custom empty message when source is empty template.
     * @group Templates
     */
    emptyMessageSourceTemplate: TemplateRef<any>;
    /**
     * Custom empty filter message when source is empty template.
     * @group Templates
     */
    emptyFilterMessageSourceTemplate: TemplateRef<any>;
    /**
     * Custom empty message when target is empty template.
     * @group Templates
     */
    emptyMessageTargetTemplate: TemplateRef<any>;
    /**
     * Custom empty filter message when target is empty template.
     * @group Templates
     */
    emptyFilterMessageTargetTemplate: TemplateRef<any>;
    /**
     * Custom move up icon template.
     * @group Templates
     */
    moveUpIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move top icon template.
     * @group Templates
     */
    moveTopIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move down icon template.
     * @group Templates
     */
    moveDownIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move bottom icon template.
     * @group Templates
     */
    moveBottomIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move to target icon template.
     * @group Templates
     */
    moveToTargetIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move all to target icon template.
     * @group Templates
     */
    moveAllToTargetIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move to source icon template.
     * @group Templates
     */
    moveToSourceIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom move all to source icon template.
     * @group Templates
     */
    moveAllToSourceIconTemplate: TemplateRef<{
        $implicit: boolean;
    }>;
    /**
     * Custom target filter icon template.
     * @group Templates
     */
    targetFilterIconTemplate: TemplateRef<{
        options: PickListFilterOptions;
    }>;
    /**
     * Custom source filter icon template.
     * @group Templates
     */
    sourceFilterIconTemplate: TemplateRef<{
        options: PickListFilterOptions;
    }>;
    templates: QueryList<PrimeTemplate>;
    _itemTemplate: TemplateRef<any> | undefined;
    _sourceHeaderTemplate: TemplateRef<any> | undefined;
    _targetHeaderTemplate: TemplateRef<any> | undefined;
    _sourceFilterTemplate: TemplateRef<any> | undefined;
    _targetFilterTemplate: TemplateRef<any> | undefined;
    _emptyMessageSourceTemplate: TemplateRef<any> | undefined;
    _emptyFilterMessageSourceTemplate: TemplateRef<any> | undefined;
    _emptyMessageTargetTemplate: TemplateRef<any> | undefined;
    _emptyFilterMessageTargetTemplate: TemplateRef<any> | undefined;
    _moveUpIconTemplate: TemplateRef<any> | undefined;
    _moveTopIconTemplate: TemplateRef<any> | undefined;
    _moveDownIconTemplate: TemplateRef<any> | undefined;
    _moveBottomIconTemplate: TemplateRef<any> | undefined;
    _moveToTargetIconTemplate: TemplateRef<any> | undefined;
    _moveAllToTargetIconTemplate: TemplateRef<any> | undefined;
    _moveToSourceIconTemplate: TemplateRef<any> | undefined;
    _moveAllToSourceIconTemplate: TemplateRef<any> | undefined;
    _targetFilterIconTemplate: TemplateRef<any> | undefined;
    _sourceFilterIconTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    onChangeSelection(e: ListboxChangeEvent, listType: number): void;
    onSourceItemDblClick(): void;
    onTargetItemDblClick(): void;
    onFilter(event: KeyboardEvent, listType: number): void;
    filterSource(value?: any): void;
    filterTarget(value?: any): void;
    filter(data: any[], listType: number): void;
    isItemVisible(item: any, listType: number): boolean | undefined;
    isEmpty(listType: number): boolean;
    isVisibleInList(data: any[], item: any, filterValue: string): boolean | undefined;
    onItemTouchEnd(): void;
    private sortByIndexInList;
    triggerChangeDetection(): void;
    moveUp(listElement: any, list: any[], selectedItems: any[], callback: EventEmitter<any>, listType: number): void;
    moveTop(listElement: any, list: any[], selectedItems: any[], callback: EventEmitter<any>, listType: number): void;
    moveDown(listElement: any, list: any[], selectedItems: any[], callback: EventEmitter<any>, listType: number): void;
    moveBottom(listElement: any, list: any[], selectedItems: any[], callback: EventEmitter<any>, listType: number): void;
    moveRight(): void;
    moveAllRight(): void;
    moveLeft(): void;
    moveAllLeft(): void;
    isSelected(item: any, selectedItems: any[]): boolean;
    findIndexInList(item: any, selectedItems: any[]): number;
    onDrop(event: CdkDragDrop<string[]>, listType: number): void;
    onListFocus(event: any, listType: any): void;
    onListBlur(event: any, listType: any): void;
    getListElement(listType: number): any;
    getListItems(listType: number): Element[];
    getLatestSelectedVisibleOptionIndex(visibleList: any[], selectedItems: any[]): number;
    getVisibleList(listType: number): any[];
    setSelectionList(listType: number, selectedItems: any[]): void;
    getDropIndexes(fromIndex: number, toIndex: number, droppedList: number, isTransfer: boolean, data: any[] | any): {
        previousIndex: any;
        currentIndex: any;
    };
    findFilteredCurrentIndex(visibleOptions: any[], index: number, options: any): number;
    resetSourceFilter(): void;
    resetTargetFilter(): void;
    resetFilter(): void;
    initMedia(): void;
    destroyMedia(): void;
    bindMediaChangeListener(): void;
    unbindMediaChangeListener(): void;
    createStyle(): void;
    sourceMoveDisabled(): boolean;
    targetMoveDisabled(): boolean;
    moveRightDisabled(): boolean;
    moveLeftDisabled(): boolean;
    moveAllRightDisabled(): boolean;
    moveAllLeftDisabled(): boolean;
    destroyStyle(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<PickList, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<PickList, "p-pickList, p-picklist, p-pick-list", never, { "source": { "alias": "source"; "required": false; }; "target": { "alias": "target"; "required": false; }; "sourceHeader": { "alias": "sourceHeader"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "rightButtonAriaLabel": { "alias": "rightButtonAriaLabel"; "required": false; }; "leftButtonAriaLabel": { "alias": "leftButtonAriaLabel"; "required": false; }; "allRightButtonAriaLabel": { "alias": "allRightButtonAriaLabel"; "required": false; }; "allLeftButtonAriaLabel": { "alias": "allLeftButtonAriaLabel"; "required": false; }; "upButtonAriaLabel": { "alias": "upButtonAriaLabel"; "required": false; }; "downButtonAriaLabel": { "alias": "downButtonAriaLabel"; "required": false; }; "topButtonAriaLabel": { "alias": "topButtonAriaLabel"; "required": false; }; "bottomButtonAriaLabel": { "alias": "bottomButtonAriaLabel"; "required": false; }; "targetHeader": { "alias": "targetHeader"; "required": false; }; "responsive": { "alias": "responsive"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "trackBy": { "alias": "trackBy"; "required": false; }; "sourceTrackBy": { "alias": "sourceTrackBy"; "required": false; }; "targetTrackBy": { "alias": "targetTrackBy"; "required": false; }; "showSourceFilter": { "alias": "showSourceFilter"; "required": false; }; "showTargetFilter": { "alias": "showTargetFilter"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "dragdrop": { "alias": "dragdrop"; "required": false; }; "style": { "alias": "style"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "sourceStyle": { "alias": "sourceStyle"; "required": false; }; "targetStyle": { "alias": "targetStyle"; "required": false; }; "showSourceControls": { "alias": "showSourceControls"; "required": false; }; "showTargetControls": { "alias": "showTargetControls"; "required": false; }; "sourceFilterPlaceholder": { "alias": "sourceFilterPlaceholder"; "required": false; }; "targetFilterPlaceholder": { "alias": "targetFilterPlaceholder"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "sourceOptionDisabled": { "alias": "sourceOptionDisabled"; "required": false; }; "targetOptionDisabled": { "alias": "targetOptionDisabled"; "required": false; }; "ariaSourceFilterLabel": { "alias": "ariaSourceFilterLabel"; "required": false; }; "ariaTargetFilterLabel": { "alias": "ariaTargetFilterLabel"; "required": false; }; "filterMatchMode": { "alias": "filterMatchMode"; "required": false; }; "stripedRows": { "alias": "stripedRows"; "required": false; }; "keepSelection": { "alias": "keepSelection"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "autoOptionFocus": { "alias": "autoOptionFocus"; "required": false; }; "buttonProps": { "alias": "buttonProps"; "required": false; }; "moveUpButtonProps": { "alias": "moveUpButtonProps"; "required": false; }; "moveTopButtonProps": { "alias": "moveTopButtonProps"; "required": false; }; "moveDownButtonProps": { "alias": "moveDownButtonProps"; "required": false; }; "moveBottomButtonProps": { "alias": "moveBottomButtonProps"; "required": false; }; "moveToTargetProps": { "alias": "moveToTargetProps"; "required": false; }; "moveAllToTargetProps": { "alias": "moveAllToTargetProps"; "required": false; }; "moveToSourceProps": { "alias": "moveToSourceProps"; "required": false; }; "moveAllToSourceProps": { "alias": "moveAllToSourceProps"; "required": false; }; "breakpoint": { "alias": "breakpoint"; "required": false; }; }, { "onMoveToSource": "onMoveToSource"; "onMoveAllToSource": "onMoveAllToSource"; "onMoveAllToTarget": "onMoveAllToTarget"; "onMoveToTarget": "onMoveToTarget"; "onSourceReorder": "onSourceReorder"; "onTargetReorder": "onTargetReorder"; "onSourceSelect": "onSourceSelect"; "onTargetSelect": "onTargetSelect"; "onSourceFilter": "onSourceFilter"; "onTargetFilter": "onTargetFilter"; "onFocus": "onFocus"; "onBlur": "onBlur"; }, ["itemTemplate", "sourceHeaderTemplate", "targetHeaderTemplate", "sourceFilterTemplate", "targetFilterTemplate", "emptyMessageSourceTemplate", "emptyFilterMessageSourceTemplate", "emptyMessageTargetTemplate", "emptyFilterMessageTargetTemplate", "moveUpIconTemplate", "moveTopIconTemplate", "moveDownIconTemplate", "moveBottomIconTemplate", "moveToTargetIconTemplate", "moveAllToTargetIconTemplate", "moveToSourceIconTemplate", "moveAllToSourceIconTemplate", "targetFilterIconTemplate", "sourceFilterIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_responsive: unknown;
    static ngAcceptInputType_showSourceFilter: unknown;
    static ngAcceptInputType_showTargetFilter: unknown;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_dragdrop: unknown;
    static ngAcceptInputType_showSourceControls: unknown;
    static ngAcceptInputType_showTargetControls: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_stripedRows: unknown;
    static ngAcceptInputType_keepSelection: unknown;
    static ngAcceptInputType_autoOptionFocus: unknown;
}
declare class PickListModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<PickListModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<PickListModule, never, [typeof PickList, typeof i1.SharedModule], [typeof PickList, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<PickListModule>;
}

export { PickList, PickListClasses, PickListModule, PickListStyle };
export type { PickListFilterOptions, PickListMoveAllToSourceEvent, PickListMoveAllToTargetEvent, PickListMoveToSourceEvent, PickListMoveToTargetEvent, PickListSourceFilterEvent, PickListSourceReorderEvent, PickListSourceSelectEvent, PickListTargetFilterEvent, PickListTargetReorderEvent, PickListTargetSelectEvent, PickListTemplates };
