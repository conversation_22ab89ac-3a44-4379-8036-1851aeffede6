import * as i0 from '@angular/core';
import { AfterContentInit, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { BlockableUI, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Toolbar is a grouping component for buttons and other content.
 *
 * [Live Demo](https://www.primeng.org/toolbar/)
 *
 * @module toolbarstyle
 *
 */
declare enum ToolbarClasses {
    /**
     * Class name of the root element
     */
    root = "p-toolbar",
    /**
     * Class name of the start element
     */
    start = "p-toolbar-start",
    /**
     * Class name of the center element
     */
    center = "p-toolbar-center",
    /**
     * Class name of the end element
     */
    end = "p-toolbar-end"
}
declare class ToolbarStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: () => string[];
        start: string;
        center: string;
        end: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ToolbarStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ToolbarStyle>;
}
interface ToolbarStyle extends BaseStyle {
}

/**
 * Toolbar is a grouping component for buttons and other content.
 * @group Components
 */
declare class Toolbar extends BaseComponent implements AfterContentInit, BlockableUI {
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Defines a string value that labels an interactive element.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    _componentStyle: ToolbarStyle;
    getBlockableElement(): HTMLElement;
    /**
     * Defines template option for start.
     * @group Templates
     */
    startTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for end.
     * @group Templates
     */
    endTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for center.
     * @group Templates
     */
    centerTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _startTemplate: TemplateRef<any> | undefined;
    _endTemplate: TemplateRef<any> | undefined;
    _centerTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Toolbar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Toolbar, "p-toolbar", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; }, {}, ["startTemplate", "endTemplate", "centerTemplate", "templates"], ["*"], true, never>;
}
declare class ToolbarModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ToolbarModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ToolbarModule, never, [typeof Toolbar, typeof i1.SharedModule], [typeof Toolbar, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ToolbarModule>;
}

/**
 * Defines valid templates in Toolbar.
 * @group Templates
 */
interface ToolbarTemplates {
    /**
     * Custom start content.
     */
    start(): TemplateRef<any>;
    /**
     * Custom end content.
     */
    end(): TemplateRef<any>;
    /**
     * Custom center content.
     */
    center(): TemplateRef<any>;
}

export { Toolbar, ToolbarClasses, ToolbarModule, ToolbarStyle };
export type { ToolbarTemplates };
