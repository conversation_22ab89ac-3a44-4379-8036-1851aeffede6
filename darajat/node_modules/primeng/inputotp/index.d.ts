import * as i0 from '@angular/core';
import { AfterContentInit, EventEmitter, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { Nullable } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * InputOtp is used to enter one time passwords.
 *
 * [Live Demo](https://www.primeng.org/inputotp/)
 *
 * @module inputotpstyle
 *
 */
declare enum InputOtpClasses {
    /**
     * Class name of the root element
     */
    root = "p-inputotp",
    /**
     * Class name of the input element
     */
    pcInputText = "p-inputotp-input"
}
declare class InputOtpStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: string;
        pcInputText: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<InputOtpStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<InputOtpStyle>;
}
interface InputOtpStyle extends BaseStyle {
}

declare const INPUT_OTP_VALUE_ACCESSOR: any;
/**
 * Input change event.
 * @property {Event} originalEvent - browser event.
 * @property {any}  value - updated value.
 * @group Interface
 */
interface InputOtpChangeEvent {
    originalEvent: Event;
    value: any;
}
/**
 * Context interface for the input template events.
 * @property {(event: Event, index: number) => void} input - input event.
 * @property {(event: Event)} keydown - keydown event.
 * @property {(event: Event)} focus - focus event.
 * @property {(event: Event)} blur - blur event.
 * @property {(event: Event)} paste - paste event.
 * @group Interface
 */
interface InputOtpTemplateEvents {
    input: (event: Event, index: number) => void;
    keydown: (event: Event) => void;
    focus: (event: Event) => void;
    blur: (event: Event) => void;
    paste: (event: Event) => void;
}
/**
 * Context of the input template.
 * @property {number | string} $implicit - token value.
 * @property {InputOtpTemplateEvents} events - Browser events of the template.
 * @property {number} index - index of the token.
 * @group Interface
 */
interface InputOtpInputTemplateContext {
    $implicit: number | string;
    events: InputOtpTemplateEvents;
    index: number;
}
/**
 * Input Otp is used to enter one time passwords.
 * @group Components
 */
declare class InputOtp extends BaseEditableHolder implements AfterContentInit {
    /**
     * When present, it specifies that an input field is read-only.
     * @group Props
     */
    readonly: boolean;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | null;
    /**
     * Number of characters to initiate.
     * @group Props
     */
    length: number;
    /**
     * Style class of the input element.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Mask pattern.
     * @group Props
     */
    mask: boolean;
    /**
     * When present, it specifies that an input field is integer-only.
     * @group Props
     */
    integerOnly: boolean;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Specifies the input variant of the component.
     * @defaultValue undefined
     * @group Props
     */
    variant: i0.InputSignal<"filled" | "outlined">;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: i0.InputSignal<"small" | "large">;
    /**
     * Callback to invoke on value change.
     * @group Emits
     */
    onChange: EventEmitter<InputOtpChangeEvent>;
    /**
     * Callback to invoke when the component receives focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the component loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Input template.
     * @param {InputOtpInputTemplateContext} context - Context of the template
     * @see {@link InputOtpInputTemplateContext}
     * @group Templates
     */
    inputTemplate: TemplateRef<any>;
    templates: Nullable<QueryList<PrimeTemplate>>;
    _inputTemplate: TemplateRef<any> | undefined;
    tokens: any;
    value: any;
    $variant: i0.Signal<"filled" | "outlined">;
    get inputMode(): string;
    get inputType(): string;
    _componentStyle: InputOtpStyle;
    ngAfterContentInit(): void;
    getToken(index: any): any;
    getTemplateEvents(index: any): {
        input: (event: any) => void;
        keydown: (event: any) => void;
        focus: (event: any) => void;
        blur: (event: any) => void;
        paste: (event: any) => void;
    };
    onInput(event: any, index: any): void;
    updateModel(event: any): void;
    updateTokens(): void;
    getModelValue(i: number): any;
    getAutofocus(i: number): boolean;
    moveToPrev(event: any): void;
    moveToNext(event: any): void;
    findNextInput(element: any): any;
    findPrevInput(element: any): any;
    onInputFocus(event: any): void;
    onInputBlur(event: any): void;
    onKeyDown(event: any): void;
    onPaste(event: any): void;
    handleOnPaste(paste: any, event: any): void;
    getRange(n: number): number[];
    trackByFn(index: number): number;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<InputOtp, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<InputOtp, "p-inputOtp, p-inputotp, p-input-otp", never, { "readonly": { "alias": "readonly"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "length": { "alias": "length"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "mask": { "alias": "mask"; "required": false; }; "integerOnly": { "alias": "integerOnly"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "variant": { "alias": "variant"; "required": false; "isSignal": true; }; "size": { "alias": "size"; "required": false; "isSignal": true; }; }, { "onChange": "onChange"; "onFocus": "onFocus"; "onBlur": "onBlur"; }, ["inputTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_autofocus: unknown;
}
declare class InputOtpModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<InputOtpModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<InputOtpModule, never, [typeof InputOtp, typeof i1.SharedModule], [typeof InputOtp, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<InputOtpModule>;
}

export { INPUT_OTP_VALUE_ACCESSOR, InputOtp, InputOtpClasses, InputOtpModule, InputOtpStyle };
export type { InputOtpChangeEvent, InputOtpInputTemplateContext, InputOtpTemplateEvents };
