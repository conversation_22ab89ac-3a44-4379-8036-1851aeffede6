import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, OnInit, OnDestroy, EventEmitter, ElementRef, QueryList } from '@angular/core';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import * as i1 from 'primeng/api';
import { FilterService, ScrollerOptions, PrimeTemplate } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { ScrollerLazyLoadEvent, Scroller } from 'primeng/scroller';
import { Nullable } from 'primeng/ts-helpers';
import { Subscription } from 'rxjs';
import { BaseStyle } from 'primeng/base';

/**
 * Filter options of listbox.
 * @group Interface
 */
interface ListboxFilterOptions {
    /**
     * Callback to filter options.
     * @param {any} value - Filter value.
     */
    filter?: (value?: any) => void;
    /**
     * Callback to reset filter.
     */
    reset?: () => void;
}
/**
 * Custom change event.
 * @see {@link Listbox.onChange}
 * @group Events
 */
interface ListboxChangeEvent {
    /**
     * Original event
     */
    originalEvent: Event;
    /**
     * Selected option value
     */
    value: any;
}
/**
 * Custom change event.
 * @see {@link Listbox.onSelectAllChange}
 * @group Events
 */
interface ListboxSelectAllChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Boolean value indicates whether all data is selected.
     */
    checked: boolean;
}
/**
 * Custom filter event.
 * @see {@link Listbox.onFilter}
 * @group Events
 */
interface ListboxFilterEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Filter value.
     */
    filter: any;
}
/**
 * Custom change event.
 * @see {@link Listbox.onClick}
 * @group Events
 */
interface ListboxClickEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Value of the component.
     */
    value: any;
    /**
     * Selected option
     */
    option?: any;
}
/**
 * Custom change event.
 * @see {@link Listbox.onDblClick}
 * @group Events
 */
interface ListboxDoubleClickEvent extends ListboxClickEvent {
}
/**
 * Defines valid templates in Panel.
 * @group Templates
 */
interface ListboxTemplates {
    /**
     * Custom item template.
     * @param {Object} context - item data.
     */
    item(context: {
        /**
         * Data of the option.
         */
        $implicit: any;
        /**
         * Index of the option.
         */
        index: number;
    }): TemplateRef<{
        $implicit: any;
        index: number;
    }>;
    /**
     * Custom group template.
     * @param {Object} context - group data.
     */
    group(context: {
        /**
         * Group option.
         */
        $implicit: any;
    }): TemplateRef<{
        $implicit: any;
    }>;
    /**
     * Custom header template.
     */
    header(): TemplateRef<any>;
    /**
     * Custom filter template.
     * @param {Object} context - filter options.
     */
    filter(context: {
        /**
         * Filter options.
         */
        options: ListboxFilterOptions;
    }): TemplateRef<any>;
    /**
     * Custom footer template.
     */
    footer(): TemplateRef<any>;
    /**
     * Custom empty template.
     */
    empty(): TemplateRef<any>;
    /**
     * Custom empty filter template.
     */
    emptyfilter(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filtericon(): TemplateRef<any>;
    /**
     * Custom check icon template.
     */
    checkicon(): TemplateRef<any>;
    /**
     * Custom checkmark template.
     */
    checkmark(context: {
        /**
         * Selection status.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
}

declare class ListBoxStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-listbox-striped': any;
            'p-disabled': any;
            'p-invalid': any;
            'p-listbox-fluid': any;
        })[];
        header: string;
        pcFilter: string;
        listContainer: string;
        list: string;
        optionGroup: string;
        option: ({ instance, option, i, scrollerOptions }: {
            instance: any;
            option: any;
            i: any;
            scrollerOptions: any;
        }) => (string | {
            'p-listbox-option-selected': any;
            'p-focus': boolean;
            'p-disabled': any;
        })[];
        optionCheckIcon: string;
        optionBlankIcon: string;
        emptyMessage: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ListBoxStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ListBoxStyle>;
}
/**
 *
 * ListBox is used to select one or more values from a list of items.
 *
 * [Live Demo](https://www.primeng.org/listbox/)
 *
 * @module listboxstyle
 *
 */
declare enum ListboxClasses {
    /**
     * Class name of the root element
     */
    root = "p-listbox",
    /**
     * Class name of the header element
     */
    header = "p-listbox-header",
    /**
     * Class name of the filter element
     */
    pcFilter = "p-listbox-filter",
    /**
     * Class name of the list container element
     */
    listContainer = "p-listbox-list-container",
    /**
     * Class name of the list element
     */
    list = "p-listbox-list",
    /**
     * Class name of the option group element
     */
    optionGroup = "p-listbox-option-group",
    /**
     * Class name of the option element
     */
    option = "p-listbox-option",
    /**
     * Class name of the option check icon element
     */
    optionCheckIcon = "p-listbox-option-check-icon",
    /**
     * Class name of the option blank icon element
     */
    optionBlankIcon = "p-listbox-option-blank-icon",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-listbox-empty-message"
}
interface ListboxStyle extends BaseStyle {
}

declare const LISTBOX_VALUE_ACCESSOR: any;
/**
 * ListBox is used to select one or more values from a list of items.
 * @group Components
 */
declare class Listbox extends BaseEditableHolder implements AfterContentInit, OnInit, OnDestroy {
    filterService: FilterService;
    /**
     * Unique identifier of the component.
     * @group Props
     */
    id: string | undefined;
    /**
     * Text to display when the search is active. Defaults to global value in i18n translation configuration.
     * @group Props
     * @defaultValue '{0} results are available'
     */
    searchMessage: string | undefined;
    /**
     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.
     * @group Props
     * @defaultValue 'No selected item'
     */
    emptySelectionMessage: string | undefined;
    /**
     * Text to be displayed in hidden accessible field when options are selected. Defaults to global value in i18n translation configuration.
     * @group Props
     * @defaultValue '{0} items selected'
     */
    selectionMessage: string | undefined;
    /**
     * Whether to focus on the first visible or selected element when the overlay panel is shown.
     * @group Props
     */
    autoOptionFocus: boolean | undefined;
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * When enabled, the focused option is selected.
     * @group Props
     */
    selectOnFocus: boolean | undefined;
    /**
     * Locale to use in searching. The default locale is the host environment's current locale.
     * @group Props
     */
    searchLocale: boolean | undefined;
    /**
     * When enabled, the hovered option will be focused.
     * @group Props
     */
    focusOnHover: boolean | undefined;
    /**
     * Text to display when filtering.
     * @group Props
     */
    filterMessage: string | undefined;
    /**
     * Fields used when filtering the options, defaults to optionLabel.
     * @group Props
     */
    filterFields: any[] | undefined;
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    lazy: boolean;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Height of an item in the list for VirtualScrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * When specified, allows selecting multiple values.
     * @group Props
     */
    multiple: boolean | undefined;
    /**
     * Style class of the container.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the list element.
     * @group Props
     */
    listStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the list element.
     * @group Props
     */
    listStyleClass: string | undefined;
    /**
     * When present, it specifies that the element value cannot be changed.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * When specified, allows selecting items with checkboxes.
     * @group Props
     */
    checkbox: boolean;
    /**
     * When specified, displays a filter input at header.
     * @group Props
     */
    filter: boolean;
    /**
     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.
     * @group Props
     */
    filterBy: string | undefined;
    /**
     * Defines how the items are filtered.
     * @group Props
     */
    filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte' | string;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean;
    /**
     * A property to uniquely identify a value in options.
     * @group Props
     */
    dataKey: string | undefined;
    /**
     * Whether header checkbox is shown in multiple mode.
     * @group Props
     */
    showToggleAll: boolean;
    /**
     * Name of the label field of an option.
     * @group Props
     */
    optionLabel: string | undefined;
    /**
     * Name of the value field of an option.
     * @group Props
     */
    optionValue: string | undefined;
    /**
     * Name of the options field of an option group.
     * @group Props
     */
    optionGroupChildren: string | undefined;
    /**
     * Name of the label field of an option group.
     * @group Props
     */
    optionGroupLabel: string | undefined;
    /**
     * Name of the disabled field of an option or function to determine disabled state.
     * @group Props
     */
    optionDisabled: string | ((item: any) => boolean) | undefined;
    /**
     * Defines a string that labels the filter input.
     * @group Props
     */
    ariaFilterLabel: string | undefined;
    /**
     * Defines placeholder of the filter input.
     * @group Props
     */
    filterPlaceHolder: string | undefined;
    /**
     * Text to display when filtering does not return any results.
     * @group Props
     */
    emptyFilterMessage: string | undefined;
    /**
     * Text to display when there is no data. Defaults to global value in i18n translation configuration.
     * @group Props
     */
    emptyMessage: string | undefined;
    /**
     * Whether to display options as grouped when nested options are provided.
     * @group Props
     */
    group: boolean | undefined;
    /**
     * An array of selectitems to display as the available options.
     * @group Props
     */
    get options(): any[];
    set options(val: any[]);
    /**
     * When specified, filter displays with this value.
     * @group Props
     */
    get filterValue(): string;
    set filterValue(val: string);
    /**
     * Whether all data is selected.
     * @group Props
     */
    get selectAll(): boolean | undefined | null;
    set selectAll(value: boolean | undefined | null);
    /**
     * Whether to displays rows with alternating colors.
     * @group Props
     * @defaultValue false
     */
    striped: boolean | undefined;
    /**
     * Whether the selected option will be add highlight class.
     * @group Props
     * @defaultValue true
     */
    highlightOnSelect: boolean;
    /**
     * Whether the selected option will be shown with a check mark.
     * @group Props
     * @defaultValue false
     */
    checkmark: boolean;
    /**
     * Whether to enable dragdrop based reordering.
     * @group Props
     */
    dragdrop: boolean;
    /**
     * Spans 100% width of the container when enabled.
     * @defaultValue undefined
     * @group Props
     */
    fluid: i0.InputSignalWithTransform<boolean, unknown>;
    /**
     * Callback to invoke on value change.
     * @param {ListboxChangeEvent} event - Custom change event.
     * @group Emits
     */
    onChange: EventEmitter<ListboxChangeEvent>;
    /**
     * Callback to invoke when option is clicked.
     * @param {ListboxClickEvent} event - Custom click event.
     * @group Emits
     */
    onClick: EventEmitter<ListboxClickEvent>;
    /**
     * Callback to invoke when option is double clicked.
     * @param {ListboxDoubleClickEvent} event - Custom double click event.
     * @group Emits
     */
    onDblClick: EventEmitter<ListboxDoubleClickEvent>;
    /**
     * Callback to invoke when data is filtered.
     * @param {ListboxFilterEvent} event - Custom filter event.
     * @group Emits
     */
    onFilter: EventEmitter<ListboxFilterEvent>;
    /**
     * Callback to invoke when component receives focus.
     * @param {FocusEvent} event - Focus event.
     * @group Emits
     */
    onFocus: EventEmitter<FocusEvent>;
    /**
     * Callback to invoke when component loses focus.
     * @param {FocusEvent} event - Blur event.
     * @group Emits
     */
    onBlur: EventEmitter<FocusEvent>;
    /**
     * Callback to invoke when all data is selected.
     * @param {ListboxSelectAllChangeEvent} event - Custom select event.
     * @group Emits
     */
    onSelectAllChange: EventEmitter<ListboxSelectAllChangeEvent>;
    /**
     * Emits on lazy load.
     * @param {ScrollerLazyLoadEvent} event - Scroller lazy load event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<ScrollerLazyLoadEvent>;
    /**
     * Emits on item is dropped.
     * @param {CdkDragDrop<string[]>} event - Scroller lazy load event.
     * @group Emits
     */
    onDrop: EventEmitter<CdkDragDrop<string[]>>;
    headerCheckboxViewChild: Nullable<ElementRef>;
    filterViewChild: Nullable<ElementRef>;
    lastHiddenFocusableElement: Nullable<ElementRef>;
    firstHiddenFocusableElement: Nullable<ElementRef>;
    scroller: Nullable<Scroller>;
    listViewChild: Nullable<ElementRef>;
    containerViewChild: Nullable<ElementRef>;
    headerFacet: Nullable<TemplateRef<any>>;
    footerFacet: Nullable<TemplateRef<any>>;
    /**
     * Custom item template.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    /**
     * Custom group template.
     * @group Templates
     */
    groupTemplate: TemplateRef<any> | undefined;
    /**
     * Custom header template.
     * @group Templates
     */
    headerTemplate: TemplateRef<any> | undefined;
    /**
     * Custom filter template.
     * @group Templates
     */
    filterTemplate: TemplateRef<any> | undefined;
    /**
     * Custom footer template.
     * @group Templates
     */
    footerTemplate: TemplateRef<any> | undefined;
    /**
     * Custom empty filter message template.
     * @group Templates
     */
    emptyFilterTemplate: TemplateRef<any> | undefined;
    /**
     * Custom empty message template.
     * @group Templates
     */
    emptyTemplate: TemplateRef<any> | undefined;
    /**
     * Custom filter icon template.
     * @group Templates
     */
    filterIconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom check icon template.
     * @group Templates
     */
    checkIconTemplate: TemplateRef<any> | undefined;
    /**
     * Custom checkmark icon template.
     * @group Templates
     */
    checkmarkTemplate: TemplateRef<any> | undefined;
    /**
     * Custom loader template.
     * @group Templates
     */
    loaderTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate>;
    _itemTemplate: TemplateRef<any> | undefined;
    _groupTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _filterTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _emptyFilterTemplate: TemplateRef<any> | undefined;
    _emptyTemplate: TemplateRef<any> | undefined;
    _filterIconTemplate: TemplateRef<any> | undefined;
    _checkIconTemplate: TemplateRef<any> | undefined;
    _checkmarkTemplate: TemplateRef<any> | undefined;
    _loaderTemplate: TemplateRef<any> | undefined;
    _filterValue: i0.WritableSignal<string>;
    _filteredOptions: any[] | undefined | null;
    filterOptions: ListboxFilterOptions | undefined;
    filtered: boolean | undefined | null;
    value: any | undefined | null;
    optionTouched: boolean | undefined | null;
    focus: boolean | undefined | null;
    headerCheckboxFocus: boolean | undefined | null;
    translationSubscription: Nullable<Subscription>;
    focused: boolean | undefined;
    scrollerTabIndex: string;
    _componentStyle: ListBoxStyle;
    get focusedOptionId(): string;
    get filterResultMessageText(): string;
    get filterMessageText(): string;
    get searchMessageText(): string;
    get emptyFilterMessageText(): string;
    get selectionMessageText(): string;
    get emptySelectionMessageText(): string;
    get selectedMessageText(): string;
    get ariaSetSize(): any;
    get virtualScrollerDisabled(): boolean;
    get searchFields(): any[];
    get toggleAllAriaLabel(): string;
    searchValue: string | undefined;
    searchTimeout: any;
    _selectAll: boolean | undefined | null;
    _options: i0.WritableSignal<any>;
    startRangeIndex: i0.WritableSignal<number>;
    focusedOptionIndex: i0.WritableSignal<number>;
    onHostFocusOut(event: FocusEvent): void;
    visibleOptions: i0.Signal<any>;
    constructor(filterService: FilterService);
    ngOnInit(): void;
    ngAfterContentInit(): void;
    flatOptions(options: any): any;
    autoUpdateModel(): void;
    /**
     * Updates the model value.
     * @group Method
     */
    updateModel(value: any, event?: any): void;
    removeOption(option: any): any;
    onOptionSelect(event: any, option: any, index?: number): void;
    onOptionSelectMultiple(event: any, option: any): void;
    onOptionSelectSingle(event: any, option: any): void;
    onOptionSelectRange(event: any, start?: number, end?: number): void;
    onToggleAll(event: any): void;
    allSelected(): any;
    onOptionTouchEnd(): void;
    onOptionMouseDown(event: MouseEvent, index: number): void;
    onOptionMouseEnter(event: MouseEvent, index: number): void;
    onOptionDoubleClick(event: MouseEvent, option: any): void;
    onFirstHiddenFocus(event: FocusEvent): void;
    onLastHiddenFocus(event: FocusEvent): void;
    onFocusout(event: FocusEvent): void;
    onListFocus(event: FocusEvent): void;
    onListBlur(event: FocusEvent): void;
    onHeaderCheckboxFocus(event: any): void;
    onHeaderCheckboxBlur(): void;
    onHeaderCheckboxKeyDown(event: any): void;
    onHeaderCheckboxTabKeyDown(event: any): void;
    onFilterChange(event: Event): void;
    onFilterBlur(event: FocusEvent): void;
    onListKeyDown(event: KeyboardEvent): void;
    onFilterKeyDown(event: KeyboardEvent): void;
    onArrowDownKey(event: KeyboardEvent): void;
    onArrowUpKey(event: KeyboardEvent): void;
    onArrowLeftKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onHomeKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onEndKey(event: KeyboardEvent, pressedInInputText?: boolean): void;
    onPageDownKey(event: KeyboardEvent): void;
    onPageUpKey(event: KeyboardEvent): void;
    onEnterKey(event: any): void;
    onSpaceKey(event: KeyboardEvent): void;
    onShiftKey(): void;
    getOptionGroupChildren(optionGroup: any): any;
    getOptionGroupLabel(optionGroup: any): any;
    getOptionLabel(option: any): any;
    getOptionIndex(index: any, scrollerOptions: any): any;
    getOptionValue(option: any): any;
    getAriaPosInset(index: number): number;
    hasSelectedOption(): boolean;
    isOptionGroup(option: any): any;
    changeFocusedOptionIndex(event: any, index: any): void;
    searchOptions(event: any, char: any): boolean;
    isOptionMatched(option: any): any;
    scrollInView(index?: number): void;
    findFirstOptionIndex(): any;
    findLastOptionIndex(): number;
    findFirstFocusedOptionIndex(): any;
    findLastFocusedOptionIndex(): number;
    findLastSelectedOptionIndex(): number;
    findNextOptionIndex(index: any): any;
    findNextSelectedOptionIndex(index: any): any;
    findPrevSelectedOptionIndex(index: any): number;
    findFirstSelectedOptionIndex(): any;
    findPrevOptionIndex(index: any): any;
    findSelectedOptionIndex(): any;
    findNearestSelectedOptionIndex(index: any, firstCheckUp?: boolean): any;
    equalityKey(): string;
    isValidSelectedOption(option: any): any;
    isOptionDisabled(option: any): any;
    isEquals(value1: any, value2: any): boolean;
    isSelected(option: any): any;
    isValidOption(option: any): boolean;
    isEmpty(): boolean;
    hasFilter(): boolean;
    resetFilter(): void;
    drop(event: CdkDragDrop<string[]>): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Listbox, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Listbox, "p-listbox, p-listBox, p-list-box", never, { "id": { "alias": "id"; "required": false; }; "searchMessage": { "alias": "searchMessage"; "required": false; }; "emptySelectionMessage": { "alias": "emptySelectionMessage"; "required": false; }; "selectionMessage": { "alias": "selectionMessage"; "required": false; }; "autoOptionFocus": { "alias": "autoOptionFocus"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "selectOnFocus": { "alias": "selectOnFocus"; "required": false; }; "searchLocale": { "alias": "searchLocale"; "required": false; }; "focusOnHover": { "alias": "focusOnHover"; "required": false; }; "filterMessage": { "alias": "filterMessage"; "required": false; }; "filterFields": { "alias": "filterFields"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "multiple": { "alias": "multiple"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "listStyle": { "alias": "listStyle"; "required": false; }; "listStyleClass": { "alias": "listStyleClass"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "checkbox": { "alias": "checkbox"; "required": false; }; "filter": { "alias": "filter"; "required": false; }; "filterBy": { "alias": "filterBy"; "required": false; }; "filterMatchMode": { "alias": "filterMatchMode"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "dataKey": { "alias": "dataKey"; "required": false; }; "showToggleAll": { "alias": "showToggleAll"; "required": false; }; "optionLabel": { "alias": "optionLabel"; "required": false; }; "optionValue": { "alias": "optionValue"; "required": false; }; "optionGroupChildren": { "alias": "optionGroupChildren"; "required": false; }; "optionGroupLabel": { "alias": "optionGroupLabel"; "required": false; }; "optionDisabled": { "alias": "optionDisabled"; "required": false; }; "ariaFilterLabel": { "alias": "ariaFilterLabel"; "required": false; }; "filterPlaceHolder": { "alias": "filterPlaceHolder"; "required": false; }; "emptyFilterMessage": { "alias": "emptyFilterMessage"; "required": false; }; "emptyMessage": { "alias": "emptyMessage"; "required": false; }; "group": { "alias": "group"; "required": false; }; "options": { "alias": "options"; "required": false; }; "filterValue": { "alias": "filterValue"; "required": false; }; "selectAll": { "alias": "selectAll"; "required": false; }; "striped": { "alias": "striped"; "required": false; }; "highlightOnSelect": { "alias": "highlightOnSelect"; "required": false; }; "checkmark": { "alias": "checkmark"; "required": false; }; "dragdrop": { "alias": "dragdrop"; "required": false; }; "fluid": { "alias": "fluid"; "required": false; "isSignal": true; }; }, { "onChange": "onChange"; "onClick": "onClick"; "onDblClick": "onDblClick"; "onFilter": "onFilter"; "onFocus": "onFocus"; "onBlur": "onBlur"; "onSelectAllChange": "onSelectAllChange"; "onLazyLoad": "onLazyLoad"; "onDrop": "onDrop"; }, ["headerFacet", "footerFacet", "itemTemplate", "groupTemplate", "headerTemplate", "filterTemplate", "footerTemplate", "emptyFilterTemplate", "emptyTemplate", "filterIconTemplate", "checkIconTemplate", "checkmarkTemplate", "loaderTemplate", "templates"], ["p-header", "p-footer"], true, never>;
    static ngAcceptInputType_autoOptionFocus: unknown;
    static ngAcceptInputType_selectOnFocus: unknown;
    static ngAcceptInputType_searchLocale: unknown;
    static ngAcceptInputType_focusOnHover: unknown;
    static ngAcceptInputType_lazy: unknown;
    static ngAcceptInputType_virtualScroll: unknown;
    static ngAcceptInputType_virtualScrollItemSize: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_multiple: unknown;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_checkbox: unknown;
    static ngAcceptInputType_filter: unknown;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_showToggleAll: unknown;
    static ngAcceptInputType_group: unknown;
    static ngAcceptInputType_striped: unknown;
    static ngAcceptInputType_highlightOnSelect: unknown;
    static ngAcceptInputType_checkmark: unknown;
    static ngAcceptInputType_dragdrop: unknown;
}
declare class ListboxModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ListboxModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ListboxModule, never, [typeof Listbox, typeof i1.SharedModule], [typeof Listbox, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ListboxModule>;
}

export { LISTBOX_VALUE_ACCESSOR, ListBoxStyle, Listbox, ListboxClasses, ListboxModule };
export type { ListboxChangeEvent, ListboxClickEvent, ListboxDoubleClickEvent, ListboxFilterEvent, ListboxFilterOptions, ListboxSelectAllChangeEvent, ListboxStyle, ListboxTemplates };
