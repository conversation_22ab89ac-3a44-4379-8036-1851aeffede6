import * as i0 from '@angular/core';
import { PipeTransform, EventEmitter, AfterContentInit, OnDestroy, ElementRef, TemplateRef, QueryList } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import * as i1 from 'primeng/api';
import { MenuItem, OverlayService, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { ConnectedOverlayScrollHandler } from 'primeng/dom';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Menu is a navigation / command component that supports dynamic and static positioning.
 *
 * [Live Demo](https://www.primeng.org/menu/)
 *
 * @module menustyle
 *
 */
declare enum MenuClasses {
    /**
     * Class name of the root element
     */
    root = "p-menu",
    /**
     * Class name of the start element
     */
    start = "p-menu-start",
    /**
     * Class name of the list element
     */
    list = "p-menu-list",
    /**
     * Class name of the submenu item element
     */
    submenuItem = "p-menu-submenu-item",
    /**
     * Class name of the separator element
     */
    separator = "p-menu-separator",
    /**
     * Class name of the end element
     */
    end = "p-menu-end",
    /**
     * Class name of the item element
     */
    item = "p-menu-item",
    /**
     * Class name of the item content element
     */
    itemContent = "p-menu-item-content",
    /**
     * Class name of the item link element
     */
    itemLink = "p-menu-item-link",
    /**
     * Class name of the item icon element
     */
    itemIcon = "p-menu-item-icon",
    /**
     * Class name of the item label element
     */
    itemLabel = "p-menu-item-label"
}
declare class MenuStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-menu-overlay': any;
        })[];
        start: string;
        list: string;
        submenuLabel: string;
        separator: string;
        end: string;
        item: ({ instance, item, id }: {
            instance: any;
            item: any;
            id: any;
        }) => any[];
        itemContent: string;
        itemLink: string;
        itemIcon: ({ item }: {
            item: any;
        }) => any[];
        itemLabel: string;
    };
    inlineStyles: {
        root: ({ instance }: {
            instance: any;
        }) => {
            position: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<MenuStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<MenuStyle>;
}
interface MenuStyle extends BaseStyle {
}

declare class SafeHtmlPipe implements PipeTransform {
    private readonly platformId;
    private readonly sanitizer;
    constructor(platformId: any, sanitizer: DomSanitizer);
    transform(value: string): SafeHtml;
    static ɵfac: i0.ɵɵFactoryDeclaration<SafeHtmlPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<SafeHtmlPipe, "safeHtml", true>;
}
declare function sanitizeHtml(value: string): SafeHtml | string;
declare class MenuItemContent extends BaseComponent {
    item: MenuItem | undefined;
    itemTemplate: any | undefined;
    onMenuItemClick: EventEmitter<any>;
    menu: Menu;
    _componentStyle: MenuStyle;
    constructor(menu: Menu);
    onItemClick(event: any, item: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<MenuItemContent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<MenuItemContent, "[pMenuItemContent]", never, { "item": { "alias": "pMenuItemContent"; "required": false; }; "itemTemplate": { "alias": "itemTemplate"; "required": false; }; }, { "onMenuItemClick": "onMenuItemClick"; }, never, never, true, never>;
}
/**
 * Menu is a navigation / command component that supports dynamic and static positioning.
 * @group Components
 */
declare class Menu extends BaseComponent implements AfterContentInit, OnDestroy {
    overlayService: OverlayService;
    /**
     * An array of menuitems.
     * @group Props
     */
    model: MenuItem[] | undefined;
    /**
     * Defines if menu would displayed as a popup.
     * @group Props
     */
    popup: boolean | undefined;
    /**
     * Inline style of the component.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the component.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Whether to automatically manage layering.
     * @group Props
     */
    autoZIndex: boolean;
    /**
     * Base zIndex value to use in layering.
     * @group Props
     */
    baseZIndex: number;
    /**
     * Transition options of the show animation.
     * @group Props
     */
    showTransitionOptions: string;
    /**
     * Transition options of the hide animation.
     * @group Props
     */
    hideTransitionOptions: string;
    /**
     * Defines a string value that labels an interactive element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Identifier of the underlying input element.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Current id state as a string.
     * @group Props
     */
    id: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number;
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    /**
     * Callback to invoke when overlay menu is shown.
     * @group Emits
     */
    onShow: EventEmitter<any>;
    /**
     * Callback to invoke when overlay menu is hidden.
     * @group Emits
     */
    onHide: EventEmitter<any>;
    /**
     * Callback to invoke when the list loses focus.
     * @param {Event} event - blur event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Callback to invoke when the list receives focus.
     * @param {Event} event - focus event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    listViewChild: Nullable<ElementRef>;
    containerViewChild: Nullable<ElementRef>;
    $appendTo: i0.Signal<any>;
    container: HTMLDivElement | undefined;
    scrollHandler: ConnectedOverlayScrollHandler | null | undefined;
    documentClickListener: VoidListener;
    documentResizeListener: VoidListener;
    preventDocumentDefault: boolean | undefined;
    target: any;
    visible: boolean | undefined;
    focusedOptionId: i0.Signal<any>;
    focusedOptionIndex: any;
    selectedOptionIndex: any;
    focused: boolean | undefined;
    overlayVisible: boolean | undefined;
    relativeAlign: boolean | undefined;
    _componentStyle: MenuStyle;
    constructor(overlayService: OverlayService);
    /**
     * Toggles the visibility of the popup menu.
     * @param {Event} event - Browser event.
     * @group Method
     */
    toggle(event: Event): void;
    /**
     * Displays the popup menu.
     * @param {Event} event - Browser event.
     * @group Method
     */
    show(event: any): void;
    ngOnInit(): void;
    /**
     * Defines template option for start.
     * @group Templates
     */
    startTemplate: TemplateRef<any> | undefined;
    _startTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for end.
     * @group Templates
     */
    endTemplate: TemplateRef<any> | undefined;
    _endTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for header.
     * @group Templates
     */
    headerTemplate: TemplateRef<any> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for item.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for item.
     * @group Templates
     */
    submenuHeaderTemplate: TemplateRef<any> | undefined;
    _submenuHeaderTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate>;
    ngAfterContentInit(): void;
    getTabIndexValue(): string | null;
    onOverlayAnimationStart(event: AnimationEvent): void;
    onOverlayAnimationEnd(event: AnimationEvent): void;
    alignOverlay(): void;
    appendOverlay(): void;
    restoreOverlayAppend(): void;
    moveOnTop(): void;
    /**
     * Hides the popup menu.
     * @group Method
     */
    hide(): void;
    onWindowResize(): void;
    menuitemId(item: MenuItem, id: string | any, index?: string | number, childIndex?: string | number): string;
    isItemFocused(id: any): boolean;
    label(label: any): any;
    disabled(disabled: any): any;
    activedescendant(): any;
    onListFocus(event: Event): void;
    onListBlur(event: FocusEvent | MouseEvent): void;
    onListKeyDown(event: any): void;
    onArrowDownKey(event: any): void;
    onArrowUpKey(event: any): void;
    onHomeKey(event: any): void;
    onEndKey(event: any): void;
    onEnterKey(event: any): void;
    onSpaceKey(event: any): void;
    findNextOptionIndex(index: any): number;
    findPrevOptionIndex(index: any): number;
    changeFocusedOptionIndex(index: any): void;
    itemClick(event: any, id: string): void;
    onOverlayClick(event: Event): void;
    bindDocumentClickListener(): void;
    unbindDocumentClickListener(): void;
    bindDocumentResizeListener(): void;
    unbindDocumentResizeListener(): void;
    bindScrollListener(): void;
    unbindScrollListener(): void;
    onOverlayHide(): void;
    ngOnDestroy(): void;
    hasSubMenu(): boolean;
    isItemHidden(item: any): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<Menu, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Menu, "p-menu", never, { "model": { "alias": "model"; "required": false; }; "popup": { "alias": "popup"; "required": false; }; "style": { "alias": "style"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "autoZIndex": { "alias": "autoZIndex"; "required": false; }; "baseZIndex": { "alias": "baseZIndex"; "required": false; }; "showTransitionOptions": { "alias": "showTransitionOptions"; "required": false; }; "hideTransitionOptions": { "alias": "hideTransitionOptions"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "id": { "alias": "id"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, { "onShow": "onShow"; "onHide": "onHide"; "onBlur": "onBlur"; "onFocus": "onFocus"; }, ["startTemplate", "endTemplate", "headerTemplate", "itemTemplate", "submenuHeaderTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_popup: unknown;
    static ngAcceptInputType_autoZIndex: unknown;
    static ngAcceptInputType_baseZIndex: unknown;
    static ngAcceptInputType_tabindex: unknown;
}
declare class MenuModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MenuModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MenuModule, never, [typeof Menu, typeof i1.SharedModule, typeof SafeHtmlPipe], [typeof Menu, typeof i1.SharedModule, typeof SafeHtmlPipe]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MenuModule>;
}

export { Menu, MenuClasses, MenuItemContent, MenuModule, MenuStyle, SafeHtmlPipe, sanitizeHtml };
