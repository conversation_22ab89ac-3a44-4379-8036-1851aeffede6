import * as i0 from '@angular/core';
import { TemplateRef, OnInit, AfterContentInit, OnChanges, ElementRef, EventEmitter, QueryList, SimpleChanges } from '@angular/core';
import * as i1 from 'primeng/api';
import { SelectItem, Aria } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { SelectChangeEvent } from 'primeng/select';
import { Nullable } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 * Paginator state.
 * @group Interface
 */
interface PaginatorState {
    page?: number;
    first?: number;
    rows?: number;
    pageCount?: number;
}
/**
 * Defines valid templates in PaginatorTemplates.
 * @group Templates
 */
interface PaginatorTemplates {
    /**
     * Custom dropdown trigger icon template.
     */
    dropdownicon(): TemplateRef<any>;
    /**
     * Custom first page link icon template.
     */
    firstpagelinkicon(): TemplateRef<any>;
    /**
     * Custom previous page link icon template.
     */
    previouspagelinkicon(): TemplateRef<any>;
    /**
     * Custom last page link icon template.
     */
    lastpagelinkicon(): TemplateRef<any>;
    /**
     * Custom next page link icon template.
     */
    nextpagelinkicon(): TemplateRef<any>;
}

/**
 *
 * Paginator is a generic component to display content in paged format.
 *
 * [Live Demo](https://www.primeng.org/paginator)
 *
 * @module paginatorstyle
 *
 */
declare enum PaginatorClasses {
    /**
     * Class name of the paginator element
     */
    paginator = "p-paginator",
    /**
     * Class name of the content start element
     */
    contentStart = "p-paginator-content-start",
    /**
     * Class name of the content end element
     */
    contentEnd = "p-paginator-content-end",
    /**
     * Class name of the first element
     */
    first = "p-paginator-first",
    /**
     * Class name of the first icon element
     */
    firstIcon = "p-paginator-first-icon",
    /**
     * Class name of the prev element
     */
    prev = "p-paginator-prev",
    /**
     * Class name of the prev icon element
     */
    prevIcon = "p-paginator-prev-icon",
    /**
     * Class name of the next element
     */
    next = "p-paginator-next",
    /**
     * Class name of the next icon element
     */
    nextIcon = "p-paginator-next-icon",
    /**
     * Class name of the last element
     */
    last = "p-paginator-last",
    /**
     * Class name of the last icon element
     */
    lastIcon = "p-paginator-last-icon",
    /**
     * Class name of the pages element
     */
    pages = "p-paginator-pages",
    /**
     * Class name of the page element
     */
    page = "p-paginator-page",
    /**
     * Class name of the current element
     */
    current = "p-paginator-current",
    /**
     * Class name of the row per page dropdown element
     */
    pcRowPerPageDropdown = "p-paginator-rpp-dropdown",
    /**
     * Class name of the jump to page dropdown element
     */
    pcJumpToPageDropdown = "p-paginator-jtp-dropdown",
    /**
     * Class name of the jump to page input element
     */
    pcJumpToPageInput = "p-paginator-jtp-input"
}
declare class PaginatorStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        paginator: ({ instance }: {
            instance: any;
        }) => string[];
        content: string;
        contentStart: string;
        contentEnd: string;
        first: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        firstIcon: string;
        prev: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        prevIcon: string;
        next: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        nextIcon: string;
        last: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        lastIcon: string;
        pages: string;
        page: ({ instance, pageLink }: {
            instance: any;
            pageLink: any;
        }) => (string | {
            'p-paginator-page-selected': boolean;
        })[];
        current: string;
        pcRowPerPageDropdown: string;
        pcJumpToPageDropdown: string;
        pcJumpToPageInput: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<PaginatorStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<PaginatorStyle>;
}
interface PaginatorStyle extends BaseStyle {
}

/**
 * Paginator is a generic component to display content in paged format.
 * @group Components
 */
declare class Paginator extends BaseComponent implements OnInit, AfterContentInit, OnChanges {
    /**
     * Number of page links to display.
     * @group Props
     */
    pageLinkSize: number;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Whether to show it even there is only one page.
     * @group Props
     */
    alwaysShow: boolean;
    /**
     * Target element to attach the dropdown overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @deprecated since v20.0.0. Use `appendTo` instead.
     * @group Props
     */
    dropdownAppendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;
    /**
     * Template instance to inject into the left side of the paginator.
     * @param {PaginatorState} context - Paginator state.
     * @group Props
     */
    templateLeft: TemplateRef<PaginatorState> | undefined;
    /**
     * Template instance to inject into the right side of the paginator.
     * @param {PaginatorState} context - Paginator state.
     * @group Props
     */
    templateRight: TemplateRef<PaginatorState> | undefined;
    /**
     * Dropdown height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    dropdownScrollHeight: string;
    /**
     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}
     * @group Props
     */
    currentPageReportTemplate: string;
    /**
     * Whether to display current page report.
     * @group Props
     */
    showCurrentPageReport: boolean | undefined;
    /**
     * When enabled, icons are displayed on paginator to go first and last page.
     * @group Props
     */
    showFirstLastIcon: boolean;
    /**
     * Number of total records.
     * @group Props
     */
    totalRecords: number;
    /**
     * Data count to display per page.
     * @group Props
     */
    rows: number;
    /**
     * Array of integer/object values to display inside rows per page dropdown. A object that have 'showAll' key can be added to it to show all data. Exp; [10,20,30,{showAll:'All'}]
     * @group Props
     */
    rowsPerPageOptions: any[] | undefined;
    /**
     * Whether to display a dropdown to navigate to any page.
     * @group Props
     */
    showJumpToPageDropdown: boolean | undefined;
    /**
     * Whether to display a input to navigate to any page.
     * @group Props
     */
    showJumpToPageInput: boolean | undefined;
    /**
     * Template instance to inject into the jump to page dropdown item inside in the paginator.
     * @param {Object} context - item instance.
     * @group Props
     */
    jumpToPageItemTemplate: TemplateRef<{
        $implicit: any;
    }> | undefined;
    /**
     * Whether to show page links.
     * @group Props
     */
    showPageLinks: boolean;
    /**
     * Locale to be used in formatting.
     * @group Props
     */
    locale: string | undefined;
    /**
     * Template instance to inject into the rows per page dropdown item inside in the paginator.
     * @param {Object} context - item instance.
     * @group Props
     */
    dropdownItemTemplate: TemplateRef<{
        $implicit: any;
    }> | undefined;
    /**
     * Zero-relative number of the first row to be displayed.
     * @group Props
     */
    get first(): number;
    set first(val: number);
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    /**
     * Callback to invoke when page changes, the event object contains information about the new state.
     * @param {PaginatorState} event - Paginator state.
     * @group Emits
     */
    onPageChange: EventEmitter<PaginatorState>;
    /**
     * Template for the dropdown icon.
     * @group Templates
     */
    dropdownIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Template for the first page link icon.
     * @group Templates
     */
    firstPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Template for the previous page link icon.
     * @group Templates
     */
    previousPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Template for the last page link icon.
     * @group Templates
     */
    lastPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Template for the next page link icon.
     * @group Templates
     */
    nextPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    templates: Nullable<QueryList<any>>;
    _dropdownIconTemplate: TemplateRef<any> | undefined;
    _firstPageLinkIconTemplate: TemplateRef<any> | undefined;
    _previousPageLinkIconTemplate: TemplateRef<any> | undefined;
    _lastPageLinkIconTemplate: TemplateRef<any> | undefined;
    _nextPageLinkIconTemplate: TemplateRef<any> | undefined;
    pageLinks: number[] | undefined;
    pageItems: SelectItem[] | undefined;
    rowsPerPageItems: SelectItem[] | undefined;
    paginatorState: any;
    _first: number;
    _page: number;
    _componentStyle: PaginatorStyle;
    $appendTo: i0.Signal<any>;
    get display(): string;
    constructor();
    ngOnInit(): void;
    ngAfterContentInit(): void;
    getAriaLabel(labelType: keyof Aria): string | undefined;
    getPageAriaLabel(value: number): string | undefined;
    getLocalization(digit: number): string;
    ngOnChanges(simpleChange: SimpleChanges): void;
    updateRowsPerPageOptions(): void;
    isFirstPage(): boolean;
    isLastPage(): boolean;
    getPageCount(): number;
    calculatePageLinkBoundaries(): [number, number];
    updatePageLinks(): void;
    changePage(p: number): void;
    updateFirst(): void;
    getPage(): number;
    changePageToFirst(event: Event): void;
    changePageToPrev(event: Event): void;
    changePageToNext(event: Event): void;
    changePageToLast(event: Event): void;
    onPageLinkClick(event: Event, page: number): void;
    onRppChange(event: Event): void;
    onPageDropdownChange(event: SelectChangeEvent): void;
    updatePaginatorState(): void;
    empty(): boolean;
    currentPage(): number;
    get currentPageReport(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<Paginator, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Paginator, "p-paginator", never, { "pageLinkSize": { "alias": "pageLinkSize"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "alwaysShow": { "alias": "alwaysShow"; "required": false; }; "dropdownAppendTo": { "alias": "dropdownAppendTo"; "required": false; }; "templateLeft": { "alias": "templateLeft"; "required": false; }; "templateRight": { "alias": "templateRight"; "required": false; }; "dropdownScrollHeight": { "alias": "dropdownScrollHeight"; "required": false; }; "currentPageReportTemplate": { "alias": "currentPageReportTemplate"; "required": false; }; "showCurrentPageReport": { "alias": "showCurrentPageReport"; "required": false; }; "showFirstLastIcon": { "alias": "showFirstLastIcon"; "required": false; }; "totalRecords": { "alias": "totalRecords"; "required": false; }; "rows": { "alias": "rows"; "required": false; }; "rowsPerPageOptions": { "alias": "rowsPerPageOptions"; "required": false; }; "showJumpToPageDropdown": { "alias": "showJumpToPageDropdown"; "required": false; }; "showJumpToPageInput": { "alias": "showJumpToPageInput"; "required": false; }; "jumpToPageItemTemplate": { "alias": "jumpToPageItemTemplate"; "required": false; }; "showPageLinks": { "alias": "showPageLinks"; "required": false; }; "locale": { "alias": "locale"; "required": false; }; "dropdownItemTemplate": { "alias": "dropdownItemTemplate"; "required": false; }; "first": { "alias": "first"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; }, { "onPageChange": "onPageChange"; }, ["dropdownIconTemplate", "firstPageLinkIconTemplate", "previousPageLinkIconTemplate", "lastPageLinkIconTemplate", "nextPageLinkIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_pageLinkSize: unknown;
    static ngAcceptInputType_alwaysShow: unknown;
    static ngAcceptInputType_showCurrentPageReport: unknown;
    static ngAcceptInputType_showFirstLastIcon: unknown;
    static ngAcceptInputType_totalRecords: unknown;
    static ngAcceptInputType_rows: unknown;
    static ngAcceptInputType_showJumpToPageDropdown: unknown;
    static ngAcceptInputType_showJumpToPageInput: unknown;
    static ngAcceptInputType_showPageLinks: unknown;
}
declare class PaginatorModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<PaginatorModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<PaginatorModule, never, [typeof Paginator, typeof i1.SharedModule], [typeof Paginator, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<PaginatorModule>;
}

export { Paginator, PaginatorClasses, PaginatorModule, PaginatorStyle };
export type { PaginatorState, PaginatorTemplates };
