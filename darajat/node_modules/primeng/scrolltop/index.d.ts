import { AnimationEvent } from '@angular/animations';
import * as i0 from '@angular/core';
import { OnInit, AfterContentInit, OnDestroy, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { ButtonProps } from 'primeng/button';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * ScrollTop gets displayed after a certain scroll position and used to navigates to the top of the page quickly.
 *
 * [Live Demo](https://www.primeng.org/scrolltop/)
 *
 * @module scrolltopstyle
 *
 */
declare enum ScrollTopClasses {
    /**
     * Class name of the root element
     */
    root = "p-scrolltop",
    /**
     * Class name of the icon element
     */
    icon = "p-scrolltop-icon"
}
declare class ScrollTopStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-scrolltop-sticky': boolean;
        })[];
        icon: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollTopStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ScrollTopStyle>;
}
interface ScrollTopStyle extends BaseStyle {
}

/**
 * ScrollTop gets displayed after a certain scroll position and used to navigates to the top of the page quickly.
 * @group Components
 */
declare class ScrollTop extends BaseComponent implements OnInit, AfterContentInit, OnDestroy {
    /**
     * Class of the element.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the element.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Target of the ScrollTop.
     * @group Props
     */
    target: 'window' | 'parent' | undefined;
    /**
     * Defines the threshold value of the vertical scroll position of the target to toggle the visibility.
     * @group Props
     */
    threshold: number;
    /**
     * Name of the icon or JSX.Element for icon.
     * @group Props
     */
    get icon(): string | undefined;
    /**
     * Defines the scrolling behavior, "smooth" adds an animation and "auto" scrolls with a jump.
     * @group Props
     */
    behavior: 'auto' | 'smooth' | undefined;
    /**
     * A string value used to determine the display transition options.
     * @group Props
     */
    showTransitionOptions: string;
    /**
     * A string value used to determine the hiding transition options.
     * @group Props
     */
    hideTransitionOptions: string;
    /**
     * Establishes a string value that labels the scroll-top button.
     * @group Props
     */
    buttonAriaLabel: string | undefined;
    /**
     * Used to pass all properties of the ButtonProps to the Button component.
     * @group Props
     */
    buttonProps: ButtonProps;
    /**
     * Template of the icon.
     * @group Templates
     */
    iconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    _iconTemplate: TemplateRef<any> | undefined;
    _icon: string | undefined;
    set icon(value: string | undefined);
    documentScrollListener: VoidFunction | null | undefined;
    parentScrollListener: VoidFunction | null | undefined;
    visible: boolean;
    overlay: any;
    _componentStyle: ScrollTopStyle;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    onClick(): void;
    onEnter(event: AnimationEvent): void;
    onLeave(event: AnimationEvent): void;
    checkVisibility(scrollY: number): void;
    bindParentScrollListener(): void;
    bindDocumentScrollListener(): void;
    unbindParentScrollListener(): void;
    unbindDocumentScrollListener(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollTop, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ScrollTop, "p-scrollTop, p-scrolltop, p-scroll-top", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "style": { "alias": "style"; "required": false; }; "target": { "alias": "target"; "required": false; }; "threshold": { "alias": "threshold"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "behavior": { "alias": "behavior"; "required": false; }; "showTransitionOptions": { "alias": "showTransitionOptions"; "required": false; }; "hideTransitionOptions": { "alias": "hideTransitionOptions"; "required": false; }; "buttonAriaLabel": { "alias": "buttonAriaLabel"; "required": false; }; "buttonProps": { "alias": "buttonProps"; "required": false; }; }, {}, ["iconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_threshold: unknown;
}
declare class ScrollTopModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ScrollTopModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ScrollTopModule, never, [typeof ScrollTop, typeof i1.SharedModule], [typeof ScrollTop, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ScrollTopModule>;
}

/**
 * Defines valid templates in ScrollTop.
 * @group Templates
 */
interface ScrollTopTemplates {
    /**
     * Icon of the component.
     */
    icon(context: {
        /**
         * Style class of the icon.
         */
        styleClass: 'p-scrolltop-icon';
    }): TemplateRef<{
        styleClass: string;
    }>;
}

export { ScrollTop, ScrollTopClasses, ScrollTopModule, ScrollTopStyle };
export type { ScrollTopTemplates };
