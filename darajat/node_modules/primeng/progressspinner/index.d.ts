import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';
import * as i0 from '@angular/core';
import * as i1 from 'primeng/api';

/**
 *
 * ProgressSpinner is a process status indicator.
 *
 * [Live Demo](https://www.primeng.org/progressspinner)
 *
 * @module progressspinnerstyle
 *
 */
declare enum ProgressSpinnerClasses {
    /**
     * Class name of the root element
     */
    root = "p-progressspinner",
    /**
     * Class name of the spin element
     */
    spin = "p-progressspinner-spin",
    /**
     * Class name of the circle element
     */
    circle = "p-progressspinner-circle"
}
declare class ProgressSpinnerStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: () => string[];
        spin: string;
        circle: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ProgressSpinnerStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ProgressSpinnerStyle>;
}
interface ProgressSpinnerStyle extends BaseStyle {
}

/**
 * ProgressSpinner is a process status indicator.
 * @group Components
 */
declare class ProgressSpinner extends BaseComponent {
    /**
     * Class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Width of the circle stroke.
     * @group Props
     */
    strokeWidth: string;
    /**
     * Color for the background of the circle.
     * @group Props
     */
    fill: string;
    /**
     * Duration of the rotate animation.
     * @group Props
     */
    animationDuration: string;
    /**
     * Used to define a aria label attribute the current element.
     * @group Props
     */
    ariaLabel: string | undefined;
    _componentStyle: ProgressSpinnerStyle;
    static ɵfac: i0.ɵɵFactoryDeclaration<ProgressSpinner, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ProgressSpinner, "p-progressSpinner, p-progress-spinner, p-progressspinner", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "strokeWidth": { "alias": "strokeWidth"; "required": false; }; "fill": { "alias": "fill"; "required": false; }; "animationDuration": { "alias": "animationDuration"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; }, {}, never, never, true, never>;
}
declare class ProgressSpinnerModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ProgressSpinnerModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ProgressSpinnerModule, never, [typeof ProgressSpinner, typeof i1.SharedModule], [typeof ProgressSpinner, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ProgressSpinnerModule>;
}

export { ProgressSpinner, ProgressSpinnerClasses, ProgressSpinnerModule, ProgressSpinnerStyle };
