import * as i0 from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, EventEmitter, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import * as i1 from 'primeng/api';
import { MenuItem } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Nullable } from 'primeng/ts-helpers';
import { Subscription } from 'rxjs';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Steps components is an indicator for the steps in a wizard workflow. Example below uses nested routes with Steps.
 *
 * [Live Demo](https://www.primeng.org/steps/)
 *
 * @module stepsstyle
 *
 */
declare enum StepsClasses {
    /**
     * Class name of the root element
     */
    root = "p-steps",
    /**
     * Class name of the list element
     */
    list = "p-steps-list",
    /**
     * Class name of the item element
     */
    item = "p-steps-item",
    /**
     * Class name of the item link element
     */
    itemLink = "p-steps-item-link",
    /**
     * Class name of the item number element
     */
    itemNumber = "p-steps-item-number",
    /**
     * Class name of the item label element
     */
    itemLabel = "p-steps-item-label"
}
declare class StepsStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-readonly': any;
        })[];
        list: string;
        item: ({ instance, item, index }: {
            instance: any;
            item: any;
            index: any;
        }) => (string | {
            'p-steps-item-active': any;
            'p-disabled': any;
        })[];
        itemLink: string;
        itemNumber: string;
        itemLabel: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<StepsStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<StepsStyle>;
}
interface StepsStyle extends BaseStyle {
}

/**
 * Steps components is an indicator for the steps in a wizard workflow.
 * @group Components
 */
declare class Steps extends BaseComponent implements OnInit, OnDestroy {
    /**
     * Index of the active item.
     * @group Props
     */
    activeIndex: number;
    /**
     * An array of menu items.
     * @group Props
     */
    model: MenuItem[] | undefined;
    /**
     * Whether the items are clickable or not.
     * @group Props
     */
    readonly: boolean;
    /**
     * Inline style of the component.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the component.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Whether to apply 'router-link-active-exact' class if route exactly matches the item path.
     * @group Props
     */
    exact: boolean;
    /**
     * Callback to invoke when the new step is selected.
     * @param {number} number - current index.
     * @group Emits
     */
    activeIndexChange: EventEmitter<number>;
    listViewChild: Nullable<ElementRef>;
    router: Router;
    route: ActivatedRoute;
    _componentStyle: StepsStyle;
    subscription: Subscription | undefined;
    ngOnInit(): void;
    onItemClick(event: Event, item: MenuItem, i: number): void;
    onItemKeydown(event: KeyboardEvent, item: MenuItem, i: number): void;
    navigateToNextItem(target: any): void;
    navigateToPrevItem(target: any): void;
    navigateToFirstItem(target: any): void;
    navigateToLastItem(target: any): void;
    findNextItem(item: any): any;
    findPrevItem(item: any): any;
    findFirstItem(): Element;
    findLastItem(): Element;
    setFocusToMenuitem(target: any, focusableItem: any): void;
    isClickableRouterLink(item: MenuItem): boolean;
    isItemDisabled(item: MenuItem, index: number): boolean;
    isActive(item: MenuItem, index: number): boolean;
    getItemTabIndex(item: MenuItem, index: number): string;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Steps, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Steps, "p-steps", never, { "activeIndex": { "alias": "activeIndex"; "required": false; }; "model": { "alias": "model"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "style": { "alias": "style"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "exact": { "alias": "exact"; "required": false; }; }, { "activeIndexChange": "activeIndexChange"; }, never, never, true, never>;
    static ngAcceptInputType_activeIndex: unknown;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_exact: unknown;
}
declare class StepsModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<StepsModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<StepsModule, never, [typeof Steps, typeof i1.SharedModule], [typeof Steps, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<StepsModule>;
}

export { Steps, StepsClasses, StepsModule, StepsStyle };
