import * as i0 from '@angular/core';
import { AfterContentInit, EventEmitter, TemplateRef, ElementRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { BlockableUI, PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

declare class PanelStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-panel-toggleable': any;
            'p-panel-expanded': any;
            'p-panel-collapsed': any;
        })[];
        icons: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-panel-icons-start': boolean;
            'p-panel-icons-end': boolean;
            'p-panel-icons-center': boolean;
        })[];
        header: string;
        title: string;
        headerActions: string;
        pcToggleButton: string;
        contentContainer: string;
        content: string;
        footer: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<PanelStyle>;
}
/**
 *
 * Panel is a container with the optional content toggle feature.
 *
 * [Live Demo](https://www.primeng.org/panel/)
 *
 * @module panelstyle
 *
 */
declare enum PanelClasses {
    /**
     * Class name of the root element
     */
    root = "p-panel",
    /**
     * Class name of the header element
     */
    header = "p-panel-header",
    /**
     * Class name of the title element
     */
    title = "p-panel-title",
    /**
     * Class name of the header actions element
     */
    headerActions = "p-panel-header-actions",
    /**
     * Class name of the toggle button element
     */
    pcToggleButton = "p-panel-toggle-button",
    /**
     * Class name of the content container element
     */
    contentContainer = "p-panel-content-container",
    /**
     * Class name of the content element
     */
    content = "p-panel-content",
    /**
     * Class name of the footer element
     */
    footer = "p-panel-footer"
}

/**
 * Custom panel toggle event, emits before panel toggle.
 * @see {@link onBeforeToggle}
 * @group Interface
 */
interface PanelBeforeToggleEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Collapsed state of the panel.
     */
    collapsed: boolean | undefined;
}
/**
 * Custom panel toggle event, emits after panel toggle.
 * @see {@link onAfterToggle}
 * @extends {PanelBeforeToggleEvent}
 * @group Interface
 */
interface PanelAfterToggleEvent extends PanelBeforeToggleEvent {
}
/**
 * Toggle icon template context.
 * @param {boolean} $implicit - Collapsed state as a boolean, implicit value.
 * @group Interface
 */
interface PanelHeaderIconsTemplateContext {
    /**
     * Collapsed state as a boolean, implicit value.
     */
    $implicit: boolean;
}
/**
 * Panel is a container with the optional content toggle feature.
 * @group Components
 */
declare class Panel extends BaseComponent implements AfterContentInit, BlockableUI {
    /**
     * Defines if content of panel can be expanded and collapsed.
     * @group Props
     */
    toggleable: boolean | undefined;
    /**
     * Header text of the panel.
     * @group Props
     */
    _header: string | undefined;
    /**
     * Defines the initial state of panel content, supports one or two-way binding as well.
     * @group Props
     */
    collapsed: boolean | undefined;
    /**
     * Id of the component.
     */
    id: string | undefined;
    /**
     * Style class of the component.
     * @group Props
     * @deprecated since v20.0.0, use `class` instead.
     */
    styleClass: string | undefined;
    /**
     * Position of the icons.
     * @group Props
     */
    iconPos: 'start' | 'end' | 'center';
    /**
     * Specifies if header of panel cannot be displayed.
     * @group Props
     */
    showHeader: boolean;
    /**
     * Specifies the toggler element to toggle the panel content.
     * @group Props
     */
    toggler: 'icon' | 'header';
    /**
     * Transition options of the animation.
     * @group Props
     */
    transitionOptions: string;
    /**
     * Used to pass all properties of the ButtonProps to the Button component.
     * @group Props
     */
    toggleButtonProps: any;
    /**
     * Emitted when the collapsed changes.
     * @param {boolean} value - New Value.
     * @group Emits
     */
    collapsedChange: EventEmitter<boolean>;
    /**
     * Callback to invoke before panel toggle.
     * @param {PanelBeforeToggleEvent} event - Custom panel toggle event
     * @group Emits
     */
    onBeforeToggle: EventEmitter<PanelBeforeToggleEvent>;
    /**
     * Callback to invoke after panel toggle.
     * @param {PanelAfterToggleEvent} event - Custom panel toggle event
     * @group Emits
     */
    onAfterToggle: EventEmitter<PanelAfterToggleEvent>;
    footerFacet: Nullable<TemplateRef<any>>;
    animating: Nullable<boolean>;
    /**
     * Defines template option for header.
     * @group Templates
     */
    headerTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for icon.
     * @example
     * ```html
     * <ng-template #icon> </ng-template>
     * ```
     * @group Templates
     */
    iconTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for content.
     * @example
     * ```html
     * <ng-template #content> </ng-template>
     * ```
     * @group Templates
     */
    contentTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for footer.
     * @example
     * ```html
     * <ng-template #footer> </ng-template>
     * ```
     * @group Templates
     */
    footerTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for headerIcon.
     * @type {TemplateRef<PanelHeaderIconsTemplateContext>} context - context of the template.
     * @example
     * ```html
     * <ng-template #headericons let-collapsed> </ng-template>
     * ```
     * @see {@link PanelHeaderIconsTemplateContext}
     * @group Templates
     */
    headerIconsTemplate: TemplateRef<PanelHeaderIconsTemplateContext> | undefined;
    _headerTemplate: TemplateRef<any> | undefined;
    _iconTemplate: TemplateRef<any> | undefined;
    _contentTemplate: TemplateRef<any> | undefined;
    _footerTemplate: TemplateRef<any> | undefined;
    _headerIconsTemplate: TemplateRef<any> | undefined;
    contentWrapperViewChild: ElementRef;
    get buttonAriaLabel(): string;
    _componentStyle: PanelStyle;
    onHeaderClick(event: MouseEvent): void;
    onIconClick(event: MouseEvent): void;
    toggle(event: MouseEvent): boolean;
    expand(): void;
    collapse(): void;
    getBlockableElement(): HTMLElement;
    updateTabIndex(): void;
    onKeyDown(event: any): void;
    onToggleDone(event: any): void;
    templates: QueryList<PrimeTemplate> | undefined;
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Panel, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Panel, "p-panel", never, { "toggleable": { "alias": "toggleable"; "required": false; }; "_header": { "alias": "header"; "required": false; }; "collapsed": { "alias": "collapsed"; "required": false; }; "id": { "alias": "id"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "iconPos": { "alias": "iconPos"; "required": false; }; "showHeader": { "alias": "showHeader"; "required": false; }; "toggler": { "alias": "toggler"; "required": false; }; "transitionOptions": { "alias": "transitionOptions"; "required": false; }; "toggleButtonProps": { "alias": "toggleButtonProps"; "required": false; }; }, { "collapsedChange": "collapsedChange"; "onBeforeToggle": "onBeforeToggle"; "onAfterToggle": "onAfterToggle"; }, ["footerFacet", "headerTemplate", "iconTemplate", "contentTemplate", "footerTemplate", "headerIconsTemplate", "templates"], ["p-header", "*", "p-footer"], true, never>;
    static ngAcceptInputType_toggleable: unknown;
    static ngAcceptInputType_collapsed: unknown;
    static ngAcceptInputType_showHeader: unknown;
}
declare class PanelModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<PanelModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<PanelModule, never, [typeof Panel, typeof i1.SharedModule], [typeof Panel, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<PanelModule>;
}

export { Panel, PanelClasses, PanelModule, PanelStyle };
export type { PanelAfterToggleEvent, PanelBeforeToggleEvent, PanelHeaderIconsTemplateContext };
