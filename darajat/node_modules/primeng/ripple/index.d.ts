import * as i0 from '@angular/core';
import { AfterViewInit, OnDestroy, NgZone } from '@angular/core';
import { BaseComponent } from 'primeng/basecomponent';
import { VoidListener } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Ripple directive adds ripple effect to the host element.
 *
 * [Live Demo](https://www.primeng.org/ripple)
 *
 * @module ripplestyle
 *
 */
declare enum RippleClasses {
    /**
     * Class name of the root element
     */
    root = "p-ink"
}
declare class RippleStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<RippleStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<RippleStyle>;
}
interface RippleStyle extends BaseStyle {
}

/**
 * Ripple directive adds ripple effect to the host element.
 * @group Components
 */
declare class Ripple extends BaseComponent implements AfterViewInit, OnDestroy {
    zone: NgZone;
    _componentStyle: RippleStyle;
    animationListener: VoidListener;
    mouseDownListener: VoidListener;
    timeout: any;
    constructor();
    ngAfterViewInit(): void;
    onMouseDown(event: MouseEvent): void;
    getInk(): any;
    resetInk(): void;
    onAnimationEnd(event: Event): void;
    create(): void;
    remove(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Ripple, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<Ripple, "[pRipple]", never, {}, {}, never, never, true, never>;
}
declare class RippleModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<RippleModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<RippleModule, never, [typeof Ripple], [typeof Ripple]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<RippleModule>;
}

export { Ripple, RippleClasses, RippleModule, RippleStyle };
