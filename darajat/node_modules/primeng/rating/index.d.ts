import * as i0 from '@angular/core';
import { TemplateRef, OnInit, EventEmitter, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 * Custom change event.
 * @see {@link Rating.onRate}
 * @group Events
 */
interface RatingRateEvent {
    /**
     * Browser event
     */
    originalEvent: Event;
    /**
     * Selected option value
     */
    value: number;
}
/**
 * Defines valid templates in Rating.
 * @group Templates
 */
interface RatingTemplates {
    /**
     * Custom on icon template.
     */
    onicon(): TemplateRef<any>;
    /**
     * Custom off icon template.
     */
    officon(): TemplateRef<any>;
    /**
     * Custom cancel icon template.
     */
    cancelicon(): TemplateRef<any>;
}

/**
 *
 * Rating component is a star based selection input.
 *
 * [Live Demo](https://www.primeng.org/rating/)
 *
 * @module ratingstyle
 *
 */
declare enum RatingClasses {
    /**
     * Class name of the root element
     */
    root = "p-rating",
    /**
     * Class name of the option element
     */
    option = "p-rating-option",
    /**
     * Class name of the on icon element
     */
    onIcon = "p-rating-on-icon",
    /**
     * Class name of the off icon element
     */
    offIcon = "p-rating-off-icon"
}
declare class RatingStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-readonly': any;
            'p-disabled': any;
        })[];
        option: ({ instance, star, value }: {
            instance: any;
            star: any;
            value: any;
        }) => (string | {
            'p-rating-option-active': boolean;
            'p-focus-visible': any;
        })[];
        onIcon: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-invalid': any;
        })[];
        offIcon: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-invalid': any;
        })[];
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<RatingStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<RatingStyle>;
}
interface RatingStyle extends BaseStyle {
}

declare const RATING_VALUE_ACCESSOR: any;
/**
 * Rating is an extension to standard radio button element with theming.
 * @group Components
 */
declare class Rating extends BaseEditableHolder implements OnInit {
    /**
     * When present, changing the value is not possible.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * Number of stars.
     * @group Props
     */
    stars: number;
    /**
     * Style class of the on icon.
     * @group Props
     */
    iconOnClass: string | undefined;
    /**
     * Inline style of the on icon.
     * @group Props
     */
    iconOnStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the off icon.
     * @group Props
     */
    iconOffClass: string | undefined;
    /**
     * Inline style of the off icon.
     * @group Props
     */
    iconOffStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Emitted on value change.
     * @param {RatingRateEvent} value - Custom rate event.
     * @group Emits
     */
    onRate: EventEmitter<RatingRateEvent>;
    /**
     * Emitted when the rating receives focus.
     * @param {Event} value - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<FocusEvent>;
    /**
     * Emitted when the rating loses focus.
     * @param {Event} value - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<FocusEvent>;
    /**
     * Custom on icon template.
     * @group Templates
     */
    onIconTemplate: Nullable<TemplateRef<any>>;
    /**
     * Custom off icon template.
     * @group Templates
     */
    offIconTemplate: Nullable<TemplateRef<any>>;
    templates: QueryList<PrimeTemplate>;
    value: Nullable<number>;
    starsArray: Nullable<number[]>;
    isFocusVisibleItem: boolean;
    focusedOptionIndex: i0.WritableSignal<number>;
    nameattr: string | undefined;
    _componentStyle: RatingStyle;
    _onIconTemplate: TemplateRef<any> | undefined;
    _offIconTemplate: TemplateRef<any> | undefined;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    onOptionClick(event: any, value: any): void;
    onOptionSelect(event: any, value: any): void;
    onChange(event: any, value: any): void;
    onInputBlur(event: any): void;
    onInputFocus(event: any, value: any): void;
    updateModel(event: any, value: any): void;
    starAriaLabel(value: any): string;
    getIconTemplate(i: number): Nullable<TemplateRef<any>>;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    get isCustomIcon(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<Rating, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Rating, "p-rating", never, { "readonly": { "alias": "readonly"; "required": false; }; "stars": { "alias": "stars"; "required": false; }; "iconOnClass": { "alias": "iconOnClass"; "required": false; }; "iconOnStyle": { "alias": "iconOnStyle"; "required": false; }; "iconOffClass": { "alias": "iconOffClass"; "required": false; }; "iconOffStyle": { "alias": "iconOffStyle"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; }, { "onRate": "onRate"; "onFocus": "onFocus"; "onBlur": "onBlur"; }, ["onIconTemplate", "offIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_stars: unknown;
    static ngAcceptInputType_autofocus: unknown;
}
declare class RatingModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<RatingModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<RatingModule, never, [typeof Rating, typeof i1.SharedModule], [typeof Rating, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<RatingModule>;
}

export { RATING_VALUE_ACCESSOR, Rating, RatingClasses, RatingModule, RatingStyle };
export type { RatingRateEvent, RatingTemplates };
