import * as i0 from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, EventEmitter, ElementRef, Injector } from '@angular/core';
import { NgControl } from '@angular/forms';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';
import * as i1 from 'primeng/api';

/**
 * Custom click event.
 * @see {@link RadioButton.onClick}
 * @group Events
 */
interface RadioButtonClickEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Browser event.
     */
    value: any;
}

/**
 *
 * RadioButton is an extension to standard radio button element with theming.
 *
 * [Live Demo](https://www.primeng.org/radiobutton/)
 *
 * @module radiobuttonstyle
 *
 */
declare enum RadioButtonClasses {
    /**
     * Class name of the root element
     */
    root = "p-radiobutton",
    /**
     * Class name of the box element
     */
    box = "p-radiobutton-box",
    /**
     * Class name of the input element
     */
    input = "p-radiobutton-input",
    /**
     * Class name of the icon element
     */
    icon = "p-radiobutton-icon"
}
declare class RadioButtonStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-radiobutton-checked': any;
            'p-disabled': any;
            'p-invalid': any;
            'p-variant-filled': boolean;
            'p-radiobutton-sm p-inputfield-sm': boolean;
            'p-radiobutton-lg p-inputfield-lg': boolean;
        })[];
        box: string;
        input: string;
        icon: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<RadioButtonStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<RadioButtonStyle>;
}
interface RadioButtonStyle extends BaseStyle {
}

declare const RADIO_VALUE_ACCESSOR: any;
declare class RadioControlRegistry {
    private accessors;
    add(control: NgControl, accessor: RadioButton): void;
    remove(accessor: RadioButton): void;
    select(accessor: RadioButton): void;
    private isSameGroup;
    static ɵfac: i0.ɵɵFactoryDeclaration<RadioControlRegistry, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<RadioControlRegistry>;
}
/**
 * RadioButton is an extension to standard radio button element with theming.
 * @group Components
 */
declare class RadioButton extends BaseEditableHolder implements OnInit, OnDestroy {
    /**
     * Value of the radiobutton.
     * @group Props
     */
    value: any;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * Identifier of the focus input to match a label defined for the component.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Used to define a string that labels the input element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Allows to select a boolean value.
     * @group Props
     */
    binary: boolean | undefined;
    /**
     * Specifies the input variant of the component.
     * @defaultValue undefined
     * @group Props
     */
    variant: i0.InputSignal<"filled" | "outlined">;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: i0.InputSignal<"small" | "large">;
    /**
     * Callback to invoke on radio button click.
     * @param {RadioButtonClickEvent} event - Custom click event.
     * @group Emits
     */
    onClick: EventEmitter<RadioButtonClickEvent>;
    /**
     * Callback to invoke when the receives focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    inputViewChild: ElementRef;
    $variant: i0.Signal<"filled" | "outlined">;
    checked: Nullable<boolean>;
    focused: Nullable<boolean>;
    control: Nullable<NgControl>;
    _componentStyle: RadioButtonStyle;
    injector: Injector;
    registry: RadioControlRegistry;
    ngOnInit(): void;
    onChange(event: any): void;
    select(event: Event): void;
    onInputFocus(event: Event): void;
    onInputBlur(event: Event): void;
    /**
     * Applies focus to input field.
     * @group Method
     */
    focus(): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<RadioButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<RadioButton, "p-radioButton, p-radiobutton, p-radio-button", never, { "value": { "alias": "value"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "binary": { "alias": "binary"; "required": false; }; "variant": { "alias": "variant"; "required": false; "isSignal": true; }; "size": { "alias": "size"; "required": false; "isSignal": true; }; }, { "onClick": "onClick"; "onFocus": "onFocus"; "onBlur": "onBlur"; }, never, never, true, never>;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_binary: unknown;
}
declare class RadioButtonModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<RadioButtonModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<RadioButtonModule, never, [typeof RadioButton, typeof i1.SharedModule], [typeof RadioButton, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<RadioButtonModule>;
}

export { RADIO_VALUE_ACCESSOR, RadioButton, RadioButtonClasses, RadioButtonModule, RadioButtonStyle, RadioControlRegistry };
export type { RadioButtonClickEvent };
