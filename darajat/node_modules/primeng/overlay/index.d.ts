import * as i1 from 'primeng/api';
import { OverlayService, OverlayModeType, ResponsiveOverlayOptions, OverlayOptions, OverlayOnBeforeShowEvent, OverlayOnShowEvent, OverlayOnBeforeHideEvent, OverlayOnHideEvent } from 'primeng/api';
import * as i0 from '@angular/core';
import { AfterContentInit, OnDestroy, EventEmitter, ElementRef, TemplateRef, QueryList, NgZone } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import { BaseComponent } from 'primeng/basecomponent';
import { BaseStyle } from 'primeng/base';

declare class OverlayStyle extends BaseStyle {
    name: string;
    theme: string;
    static ɵfac: i0.ɵɵFactoryDeclaration<OverlayStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<OverlayStyle>;
}

/**
 * This API allows overlay components to be controlled from the PrimeNG. In this way, all overlay components in the application can have the same behavior.
 * @group Components
 */
declare class Overlay extends BaseComponent implements <PERSON>C<PERSON>ntIni<PERSON>, On<PERSON><PERSON>roy {
    overlayService: OverlayService;
    private zone;
    /**
     * The visible property is an input that determines the visibility of the component.
     * @defaultValue false
     * @group Props
     */
    get visible(): boolean;
    set visible(value: boolean);
    /**
     * The mode property is an input that determines the overlay mode type or string.
     * @defaultValue null
     * @group Props
     */
    get mode(): OverlayModeType | string;
    set mode(value: OverlayModeType | string);
    /**
     * The style property is an input that determines the style object for the component.
     * @defaultValue null
     * @group Props
     */
    get style(): {
        [klass: string]: any;
    } | null | undefined;
    set style(value: {
        [klass: string]: any;
    } | null | undefined);
    /**
     * The styleClass property is an input that determines the CSS class(es) for the component.
     * @defaultValue null
     * @group Props
     */
    get styleClass(): string;
    set styleClass(value: string);
    /**
     * The contentStyle property is an input that determines the style object for the content of the component.
     * @defaultValue null
     * @group Props
     */
    get contentStyle(): {
        [klass: string]: any;
    } | null | undefined;
    set contentStyle(value: {
        [klass: string]: any;
    } | null | undefined);
    /**
     * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.
     * @defaultValue null
     * @group Props
     */
    get contentStyleClass(): string;
    set contentStyleClass(value: string);
    /**
     * The target property is an input that specifies the target element or selector for the component.
     * @defaultValue null
     * @group Props
     */
    get target(): string | null | undefined;
    set target(value: string | null | undefined);
    /**
     * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.
     * @defaultValue false
     * @group Props
     */
    get autoZIndex(): boolean;
    set autoZIndex(value: boolean);
    /**
     * The baseZIndex is base zIndex value to use in layering.
     * @defaultValue null
     * @group Props
     */
    get baseZIndex(): number;
    set baseZIndex(value: number);
    /**
     * Transition options of the show or hide animation.
     * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)
     * @group Props
     */
    get showTransitionOptions(): string;
    set showTransitionOptions(value: string);
    /**
     * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.
     * @defaultValue .1s linear
     * @group Props
     */
    get hideTransitionOptions(): string;
    set hideTransitionOptions(value: string);
    /**
     * The listener property is an input that specifies the listener object for the component.
     * @defaultValue null
     * @group Props
     */
    get listener(): any;
    set listener(value: any);
    /**
     * It is the option used to determine in which mode it should appear according to the given media or breakpoint.
     * @defaultValue null
     * @group Props
     */
    get responsive(): ResponsiveOverlayOptions | undefined;
    set responsive(val: ResponsiveOverlayOptions | undefined);
    /**
     * The options property is an input that specifies the overlay options for the component.
     * @defaultValue null
     * @group Props
     */
    get options(): OverlayOptions | undefined;
    set options(val: OverlayOptions | undefined);
    /**
     * Target element to attach the overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @defaultValue 'self'
     * @group Props
     */
    appendTo: i0.InputSignal<any>;
    /**
     * This EventEmitter is used to notify changes in the visibility state of a component.
     * @param {Boolean} boolean - Value of visibility as boolean.
     * @group Emits
     */
    visibleChange: EventEmitter<boolean>;
    /**
     * Callback to invoke before the overlay is shown.
     * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.
     * @group Emits
     */
    onBeforeShow: EventEmitter<OverlayOnBeforeShowEvent>;
    /**
     * Callback to invoke when the overlay is shown.
     * @param {OverlayOnShowEvent} event - Custom overlay show event.
     * @group Emits
     */
    onShow: EventEmitter<OverlayOnShowEvent>;
    /**
     * Callback to invoke before the overlay is hidden.
     * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.
     * @group Emits
     */
    onBeforeHide: EventEmitter<OverlayOnBeforeHideEvent>;
    /**
     * Callback to invoke when the overlay is hidden
     * @param {OverlayOnHideEvent} event - Custom hide event.
     * @group Emits
     */
    onHide: EventEmitter<OverlayOnHideEvent>;
    /**
     * Callback to invoke when the animation is started.
     * @param {AnimationEvent} event - Animation event.
     * @group Emits
     */
    onAnimationStart: EventEmitter<AnimationEvent>;
    /**
     * Callback to invoke when the animation is done.
     * @param {AnimationEvent} event - Animation event.
     * @group Emits
     */
    onAnimationDone: EventEmitter<AnimationEvent>;
    overlayViewChild: ElementRef | undefined;
    contentViewChild: ElementRef | undefined;
    /**
     * Content template of the component.
     * @group Templates
     */
    contentTemplate: TemplateRef<any> | undefined;
    templates: QueryList<any> | undefined;
    hostAttrSelector: i0.InputSignal<string>;
    $appendTo: i0.Signal<any>;
    _contentTemplate: TemplateRef<any> | undefined;
    _visible: boolean;
    _mode: OverlayModeType | string;
    _style: {
        [klass: string]: any;
    } | null | undefined;
    _styleClass: string | undefined;
    _contentStyle: {
        [klass: string]: any;
    } | null | undefined;
    _contentStyleClass: string | undefined;
    _target: any;
    _autoZIndex: boolean | undefined;
    _baseZIndex: number | undefined;
    _showTransitionOptions: string | undefined;
    _hideTransitionOptions: string | undefined;
    _listener: any;
    _responsive: ResponsiveOverlayOptions | undefined;
    _options: OverlayOptions | undefined;
    modalVisible: boolean;
    isOverlayClicked: boolean;
    isOverlayContentClicked: boolean;
    scrollHandler: any;
    documentClickListener: any;
    documentResizeListener: any;
    _componentStyle: OverlayStyle;
    private documentKeyboardListener;
    private window;
    protected transformOptions: any;
    get modal(): boolean;
    get overlayMode(): string;
    get overlayOptions(): OverlayOptions;
    get overlayResponsiveOptions(): ResponsiveOverlayOptions;
    get overlayResponsiveDirection(): i1.ResponsiveOverlayDirectionType;
    get overlayEl(): any;
    get contentEl(): any;
    get targetEl(): any;
    constructor(overlayService: OverlayService, zone: NgZone);
    ngAfterContentInit(): void;
    show(overlay?: HTMLElement, isFocus?: boolean): void;
    hide(overlay?: HTMLElement, isFocus?: boolean): void;
    alignOverlay(): void;
    onVisibleChange(visible: boolean): void;
    onOverlayClick(): void;
    onOverlayContentClick(event: MouseEvent): void;
    onOverlayContentAnimationStart(event: AnimationEvent): void;
    onOverlayContentAnimationDone(event: AnimationEvent): void;
    handleEvents(name: string, params: any): void;
    bindListeners(): void;
    unbindListeners(): void;
    bindScrollListener(): void;
    unbindScrollListener(): void;
    bindDocumentClickListener(): void;
    unbindDocumentClickListener(): void;
    bindDocumentResizeListener(): void;
    unbindDocumentResizeListener(): void;
    bindDocumentKeyboardListener(): void;
    unbindDocumentKeyboardListener(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Overlay, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Overlay, "p-overlay", never, { "visible": { "alias": "visible"; "required": false; }; "mode": { "alias": "mode"; "required": false; }; "style": { "alias": "style"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "contentStyle": { "alias": "contentStyle"; "required": false; }; "contentStyleClass": { "alias": "contentStyleClass"; "required": false; }; "target": { "alias": "target"; "required": false; }; "autoZIndex": { "alias": "autoZIndex"; "required": false; }; "baseZIndex": { "alias": "baseZIndex"; "required": false; }; "showTransitionOptions": { "alias": "showTransitionOptions"; "required": false; }; "hideTransitionOptions": { "alias": "hideTransitionOptions"; "required": false; }; "listener": { "alias": "listener"; "required": false; }; "responsive": { "alias": "responsive"; "required": false; }; "options": { "alias": "options"; "required": false; }; "appendTo": { "alias": "appendTo"; "required": false; "isSignal": true; }; "hostAttrSelector": { "alias": "hostAttrSelector"; "required": false; "isSignal": true; }; }, { "visibleChange": "visibleChange"; "onBeforeShow": "onBeforeShow"; "onShow": "onShow"; "onBeforeHide": "onBeforeHide"; "onHide": "onHide"; "onAnimationStart": "onAnimationStart"; "onAnimationDone": "onAnimationDone"; }, ["contentTemplate", "templates"], ["*"], true, never>;
}
declare class OverlayModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<OverlayModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<OverlayModule, never, [typeof Overlay, typeof i1.SharedModule], [typeof Overlay, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<OverlayModule>;
}

export { Overlay, OverlayModule, OverlayStyle };
