import * as i0 from '@angular/core';
import { TemplateRef, AfterContentInit, EventEmitter, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { BaseStyle } from 'primeng/base';

/**
 * Custom click event.
 * @see {@link SelectButton.onOptionClick}
 * @group Events
 */
interface SelectButtonOptionClickEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
    /**
     * Selected option.
     */
    option?: any;
    /**
     * Index of the selected option.
     */
    index?: number;
}
/**
 * Custom change event.
 * @see {@link SelectButton.onChange}
 * @group Events
 */
interface SelectButtonChangeEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
    /**
     * Selected option.
     */
    value?: any;
}
/**
 * Defines valid templates in SelectButton.
 * @group Templates
 */
interface SelectButtonTemplates {
    /**
     * Custom item template.
     */
    item(): TemplateRef<any>;
}

/**
 *
 * SelectButton is used to choose single or multiple items from a list using buttons.
 *
 * [Live Demo](https://www.primeng.org/selectbutton/)
 *
 * @module selectbuttonstyle
 *
 */
declare enum SelectButtonClasses {
    /**
     * Class name of the root element
     */
    root = "p-selectbutton"
}
declare class SelectButtonStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-invalid': any;
            'p-selectbutton-fluid': any;
        })[];
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectButtonStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SelectButtonStyle>;
}
interface SelectButtonStyle extends BaseStyle {
}

declare const SELECTBUTTON_VALUE_ACCESSOR: any;
/**
 * SelectButton is used to choose single or multiple items from a list using buttons.
 * @group Components
 */
declare class SelectButton extends BaseEditableHolder implements AfterContentInit {
    /**
     * An array of selectitems to display as the available options.
     * @group Props
     */
    options: any[] | undefined;
    /**
     * Name of the label field of an option.
     * @group Props
     */
    optionLabel: string | undefined;
    /**
     * Name of the value field of an option.
     * @group Props
     */
    optionValue: string | undefined;
    /**
     * Name of the disabled field of an option.
     * @group Props
     */
    optionDisabled: string | undefined;
    /**
     * Whether selection can be cleared.
     * @group Props
     */
    get unselectable(): boolean;
    private _unselectable;
    set unselectable(value: boolean);
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number;
    /**
     * When specified, allows selecting multiple values.
     * @group Props
     */
    multiple: boolean | undefined;
    /**
     * Whether selection can not be cleared.
     * @group Props
     */
    allowEmpty: boolean;
    /**
     * Style class of the component.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * A property to uniquely identify a value in options.
     * @group Props
     */
    dataKey: string | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: i0.InputSignal<"small" | "large">;
    /**
     * Spans 100% width of the container when enabled.
     * @defaultValue undefined
     * @group Props
     */
    fluid: i0.InputSignalWithTransform<boolean, unknown>;
    /**
     * Callback to invoke on input click.
     * @param {SelectButtonOptionClickEvent} event - Custom click event.
     * @group Emits
     */
    onOptionClick: EventEmitter<SelectButtonOptionClickEvent>;
    /**
     * Callback to invoke on selection change.
     * @param {SelectButtonChangeEvent} event - Custom change event.
     * @group Emits
     */
    onChange: EventEmitter<SelectButtonChangeEvent>;
    /**
     * Template of an item in the list.
     * @group Templates
     */
    itemTemplate: TemplateRef<any>;
    _itemTemplate: TemplateRef<any> | undefined;
    get equalityKey(): string;
    value: any;
    focusedIndex: number;
    _componentStyle: SelectButtonStyle;
    getAllowEmpty(): boolean;
    getOptionLabel(option: any): any;
    getOptionValue(option: any): any;
    isOptionDisabled(option: any): any;
    onOptionSelect(event: any, option: any, index: any): void;
    changeTabIndexes(event: any, direction: any): void;
    onFocus(event: Event, index: number): void;
    onBlur(): void;
    removeOption(option: any): void;
    isSelected(option: any): boolean;
    templates: QueryList<PrimeTemplate> | undefined;
    ngAfterContentInit(): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SelectButton, "p-selectButton, p-selectbutton, p-select-button", never, { "options": { "alias": "options"; "required": false; }; "optionLabel": { "alias": "optionLabel"; "required": false; }; "optionValue": { "alias": "optionValue"; "required": false; }; "optionDisabled": { "alias": "optionDisabled"; "required": false; }; "unselectable": { "alias": "unselectable"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "multiple": { "alias": "multiple"; "required": false; }; "allowEmpty": { "alias": "allowEmpty"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "dataKey": { "alias": "dataKey"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "size": { "alias": "size"; "required": false; "isSignal": true; }; "fluid": { "alias": "fluid"; "required": false; "isSignal": true; }; }, { "onOptionClick": "onOptionClick"; "onChange": "onChange"; }, ["itemTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_unselectable: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_multiple: unknown;
    static ngAcceptInputType_allowEmpty: unknown;
    static ngAcceptInputType_autofocus: unknown;
}
declare class SelectButtonModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectButtonModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SelectButtonModule, never, [typeof SelectButton, typeof i1.SharedModule], [typeof SelectButton, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SelectButtonModule>;
}

export { SELECTBUTTON_VALUE_ACCESSOR, SelectButton, SelectButtonClasses, SelectButtonModule, SelectButtonStyle };
export type { SelectButtonChangeEvent, SelectButtonOptionClickEvent, SelectButtonTemplates };
