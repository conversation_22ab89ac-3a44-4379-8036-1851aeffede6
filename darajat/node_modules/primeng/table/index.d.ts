import * as i0 from '@angular/core';
import { TemplateRef, OnInit, AfterViewInit, AfterContentInit, OnChanges, ElementRef, EventEmitter, QueryList, NgZone, SimpleChanges, OnDestroy, ChangeDetectorRef, Renderer2 } from '@angular/core';
import * as rxjs from 'rxjs';
import { Subscription } from 'rxjs';
import { AnimationEvent } from '@angular/animations';
import * as i25 from 'primeng/api';
import { FilterMetadata, LazyLoadMeta, SortMeta, BlockableUI, ScrollerOptions, TableState, PrimeTemplate, OverlayService, FilterService, SelectItem } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as i11 from 'primeng/checkbox';
import { CheckboxChangeEvent } from 'primeng/checkbox';
import { ConnectedOverlayScrollHandler } from 'primeng/dom';
import * as i24 from 'primeng/radiobutton';
import { RadioButton, RadioButtonClickEvent } from 'primeng/radiobutton';
import * as i12 from 'primeng/scroller';
import { Scroller } from 'primeng/scroller';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';
import * as i6 from 'primeng/button';
import { ButtonProps } from 'primeng/button';
import * as i1 from '@angular/common';
import * as i2 from 'primeng/paginator';
import * as i3 from 'primeng/inputtext';
import * as i4 from 'primeng/select';
import * as i5 from '@angular/forms';
import * as i7 from 'primeng/selectbutton';
import * as i8 from 'primeng/datepicker';
import * as i9 from 'primeng/inputnumber';
import * as i10 from 'primeng/badge';
import * as i13 from 'primeng/icons/arrowdown';
import * as i14 from 'primeng/icons/arrowup';
import * as i15 from 'primeng/icons/spinner';
import * as i16 from 'primeng/icons/sortalt';
import * as i17 from 'primeng/icons/sortamountupalt';
import * as i18 from 'primeng/icons/sortamountdown';
import * as i19 from 'primeng/icons/filter';
import * as i20 from 'primeng/icons/filterfill';
import * as i21 from 'primeng/icons/filterslash';
import * as i22 from 'primeng/icons/plus';
import * as i23 from 'primeng/icons/trash';

declare class TableStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-datatable-hoverable': any;
            'p-datatable-resizable': any;
            'p-datatable-resizable-fit': boolean;
            'p-datatable-scrollable': any;
            'p-datatable-flex-scrollable': boolean;
            'p-datatable-striped': any;
            'p-datatable-gridlines': any;
            'p-datatable-sm': boolean;
            'p-datatable-lg': boolean;
        })[];
        mask: string;
        loadingIcon: string;
        header: string;
        pcPaginator: ({ instance }: {
            instance: any;
        }) => string;
        tableContainer: string;
        table: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-datatable-scrollable-table': any;
            'p-datatable-resizable-table': any;
            'p-datatable-resizable-table-fit': boolean;
        })[];
        thead: string;
        columnResizer: string;
        columnHeaderContent: string;
        columnTitle: string;
        columnFooter: string;
        sortIcon: string;
        pcSortBadge: string;
        filter: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-filter': boolean;
            'p-datatable-inline-filter': boolean;
            'p-datatable-popover-filter': boolean;
        };
        filterElementContainer: string;
        pcColumnFilterButton: string;
        pcColumnFilterClearButton: string;
        filterOverlay: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-filter-overlay p-component': boolean;
            'p-datatable-filter-overlay-popover': boolean;
        };
        filterConstraintList: string;
        filterConstraint: string;
        filterConstraintSeparator: string;
        filterOperator: string;
        pcFilterOperatorDropdown: string;
        filterRuleList: string;
        filterRule: string;
        pcFilterConstraintDropdown: string;
        pcFilterRemoveRuleButton: string;
        pcFilterAddRuleButton: string;
        filterButtonbar: string;
        pcFilterClearButton: string;
        pcFilterApplyButton: string;
        tbody: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-tbody': boolean;
            'p-datatable-frozen-tbody': any;
            'p-virtualscroller-content': any;
        };
        rowGroupHeader: string;
        rowToggleButton: string;
        rowToggleIcon: string;
        rowExpansion: string;
        rowGroupFooter: string;
        emptyMessage: string;
        bodyCell: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-frozen-column': any;
        };
        reorderableRowHandle: string;
        pcRowEditorInit: string;
        pcRowEditorSave: string;
        pcRowEditorCancel: string;
        tfoot: string;
        footerCell: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-frozen-column': any;
        };
        virtualScrollerSpacer: string;
        footer: string;
        columnResizeIndicator: string;
        rowReorderIndicatorUp: string;
        rowReorderIndicatorDown: string;
        sortableColumn: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-sortable-column': any;
            ' p-datatable-column-sorted': any;
        };
        sortableColumnIcon: string;
        sortableColumnBadge: string;
        selectableRow: ({ instance }: {
            instance: any;
        }) => {
            'p-datatable-selectable-row': any;
            'p-datatable-row-selected': any;
        };
        resizableColumn: string;
        reorderableColumn: string;
        rowEditorCancel: string;
    };
    inlineStyles: {
        tableContainer: ({ instance }: {
            instance: any;
        }) => {
            'max-height': any;
            overflow: string;
        };
        thead: {
            position: string;
        };
        tfoot: {
            position: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TableStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TableStyle>;
}
/**
 *
 * DataTable displays data in tabular format.
 *
 * [Live Demo](https://www.primeng.org/table/)
 *
 * @module tablestyle
 *
 */
declare enum TableClasses {
    /**
     * Class name of the root element
     */
    root = "p-datatable",
    /**
     * Class name of the mask element
     */
    mask = "p-datatable-mask",
    /**
     * Class name of the loading icon element
     */
    loadingIcon = "p-datatable-loading-icon",
    /**
     * Class name of the header element
     */
    header = "p-datatable-header",
    /**
     * Class name of the paginator element
     */
    pcPaginator = "p-datatable-paginator-[position]",
    /**
     * Class name of the table container element
     */
    tableContainer = "p-datatable-table-container",
    /**
     * Class name of the table element
     */
    table = "p-datatable-table",
    /**
     * Class name of the thead element
     */
    thead = "p-datatable-thead",
    /**
     * Class name of the column resizer element
     */
    columnResizer = "p-datatable-column-resizer",
    /**
     * Class name of the column header content element
     */
    columnHeaderContent = "p-datatable-column-header-content",
    /**
     * Class name of the column title element
     */
    columnTitle = "p-datatable-column-title",
    /**
     * Class name of the sort icon element
     */
    sortIcon = "p-datatable-sort-icon",
    /**
     * Class name of the sort badge element
     */
    pcSortBadge = "p-datatable-sort-badge",
    /**
     * Class name of the filter element
     */
    filter = "p-datatable-filter",
    /**
     * Class name of the filter element container element
     */
    filterElementContainer = "p-datatable-filter-element-container",
    /**
     * Class name of the column filter button element
     */
    pcColumnFilterButton = "p-datatable-column-filter-button",
    /**
     * Class name of the column filter clear button element
     */
    pcColumnFilterClearButton = "p-datatable-column-filter-clear-button",
    /**
     * Class name of the filter overlay element
     */
    filterOverlay = "p-datatable-filter-overlay",
    /**
     * Class name of the filter constraint list element
     */
    filterConstraintList = "p-datatable-filter-constraint-list",
    /**
     * Class name of the filter constraint element
     */
    filterConstraint = "p-datatable-filter-constraint",
    /**
     * Class name of the filter constraint separator element
     */
    filterConstraintSeparator = "p-datatable-filter-constraint-separator",
    /**
     * Class name of the filter operator element
     */
    filterOperator = "p-datatable-filter-operator",
    /**
     * Class name of the filter operator dropdown element
     */
    pcFilterOperatorDropdown = "p-datatable-filter-operator-dropdown",
    /**
     * Class name of the filter rule list element
     */
    filterRuleList = "p-datatable-filter-rule-list",
    /**
     * Class name of the filter rule element
     */
    filterRule = "p-datatable-filter-rule",
    /**
     * Class name of the filter constraint dropdown element
     */
    pcFilterConstraintDropdown = "p-datatable-filter-constraint-dropdown",
    /**
     * Class name of the filter remove rule button element
     */
    pcFilterRemoveRuleButton = "p-datatable-filter-remove-rule-button",
    /**
     * Class name of the filter add rule button element
     */
    pcFilterAddRuleButton = "p-datatable-filter-add-rule-button",
    /**
     * Class name of the filter buttonbar element
     */
    filterButtonbar = "p-datatable-filter-buttonbar",
    /**
     * Class name of the filter clear button element
     */
    pcFilterClearButton = "p-datatable-filter-clear-button",
    /**
     * Class name of the filter apply button element
     */
    pcFilterApplyButton = "p-datatable-filter-apply-button",
    /**
     * Class name of the tbody element
     */
    tbody = "p-datatable-tbody",
    /**
     * Class name of the row group header element
     */
    rowGroupHeader = "p-datatable-row-group-header",
    /**
     * Class name of the row toggle button element
     */
    rowToggleButton = "p-datatable-row-toggle-button",
    /**
     * Class name of the row toggle icon element
     */
    rowToggleIcon = "p-datatable-row-toggle-icon",
    /**
     * Class name of the row expansion element
     */
    rowExpansion = "p-datatable-row-expansion",
    /**
     * Class name of the row group footer element
     */
    rowGroupFooter = "p-datatable-row-group-footer",
    /**
     * Class name of the empty message element
     */
    emptyMessage = "p-datatable-empty-message",
    /**
     * Class name of the reorderable row handle element
     */
    reorderableRowHandle = "p-datatable-reorderable-row-handle",
    /**
     * Class name of the row editor init element
     */
    pcRowEditorInit = "p-datatable-row-editor-init",
    /**
     * Class name of the row editor save element
     */
    pcRowEditorSave = "p-datatable-row-editor-save",
    /**
     * Class name of the row editor cancel element
     */
    pcRowEditorCancel = "p-datatable-row-editor-cancel",
    /**
     * Class name of the tfoot element
     */
    tfoot = "p-datatable-tfoot",
    /**
     * Class name of the virtual scroller spacer element
     */
    virtualScrollerSpacer = "p-datatable-virtualscroller-spacer",
    /**
     * Class name of the footer element
     */
    footer = "p-datatable-footer",
    /**
     * Class name of the column resize indicator element
     */
    columnResizeIndicator = "p-datatable-column-resize-indicator",
    /**
     * Class name of the row reorder indicator up element
     */
    rowReorderIndicatorUp = "p-datatable-row-reorder-indicator-up",
    /**
     * Class name of the row reorder indicator down element
     */
    rowReorderIndicatorDown = "p-datatable-row-reorder-indicator-down",
    /**
     * Class name of the sortable column element
     */
    sortableColumn = "p-datatable-sortable-column",
    /**
     * Class name of the sortable column icon element
     */
    sortableColumnIcon = "p-sortable-column-icon",
    /**
     * Class name of the sortable column badge element
     */
    sortableColumnBadge = "p-sortable-column-badge",
    /**
     * Class name of the selectable row element
     */
    selectableRow = "p-datatable-selectable-row",
    /**
     * Class name of the resizable column element
     */
    resizableColumn = "p-datatable-resizable-column",
    /**
     * Class name of the row editor cancel element
     */
    rowEditorCancel = "p-datatable-row-editor-cancel"
}

/**
 * Custom select event.
 * @see {@link Table.onRowSelect}
 * @extends {LazyLoadMeta}
 * @group Events
 */
interface TableRowSelectEvent<RowData = any> {
    /**
     * Browser event.
     */
    originalEvent?: Event;
    /**
     * Row data.
     */
    data?: RowData | RowData[];
    /**
     * Selection type.
     */
    type?: string;
    /**
     * Index of the selected row.
     */
    index?: number;
}
/**
 * Custom unselect event.
 * @see {@link Table.onRowUnselect}
 * @extends {TableRowSelectEvent}
 * @group Events
 */
interface TableRowUnSelectEvent<RowData> extends TableRowSelectEvent<RowData> {
}
/**
 * Custom page event.
 * @see {@link Table.onPage}
 */
interface TablePageEvent {
    /**
     * Index of the first element.
     */
    first: number;
    /**
     * Row count.
     */
    rows: number;
}
/**
 * Custom filter event.
 * @see {@link Table.onFilter}
 * @group Events
 */
interface TableFilterEvent {
    /**
     * Filter meta.
     */
    filters?: {
        [s: string]: FilterMetadata | undefined;
    };
    /**
     * Value after filter.
     */
    filteredValue?: any[] | any;
}
/**
 * Custom lazy load event.
 * @see {@link Table.onLazyLoad}
 * @extends {LazyLoadMeta}
 * @group Events
 */
interface TableLazyLoadEvent extends LazyLoadMeta {
    /**
     * First element in viewport.
     */
    first?: number;
    /**
     * Last element in viewport.
     */
    last?: number;
}
/**
 * Custom row expand event.
 * @see {@link Table.onRowExpand}
 * @group Events
 */
interface TableRowExpandEvent<RowData = any> {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Row data.
     */
    data: RowData;
}
/**
 * Custom row collapse event.
 * @see {@link Table.onRowCollapse}
 * @extends {TableRowExpandEvent}
 * @group Events
 */
interface TableRowCollapseEvent extends TableRowExpandEvent {
}
/**
 * Custom context menu select event.
 * @see {@link Table.onContextMenuSelect}
 * @group Events
 */
interface TableContextMenuSelectEvent<RowData = any> {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Row data.
     */
    data: RowData;
    /**
     * Row index.
     */
    index: number;
}
/**
 * Custom context menu selection change event.
 * @see {@link Table.contextMenuSelectionChange}
 * @group Events
 */
interface TableContextMenuSelectionChangeEvent<RowData = any> {
    /**
     * Row data.
     */
    data: RowData;
}
/**
 * Custom column resize event.
 * @see {@link Table.onColResize}
 * @group Events
 */
interface TableColResizeEvent {
    /**
     * Instance of resized column.
     */
    element: HTMLElement;
    /**
     * Position.
     */
    delta: number;
}
/**
 * Custom column reorder event.
 * @see {@link Table.onColReorder}
 * @group Events
 */
interface TableColumnReorderEvent {
    /**
     * Index of the dragged item.
     */
    dragIndex?: number;
    /**
     * Index of the drop position.
     */
    dropIndex?: number;
    /**
     * Columns after reorder.
     */
    columns?: any[];
}
/**
 * Custom row reorder event.
 * @see {@link Table.onRowReorder}
 * @group Events
 */
interface TableRowReorderEvent {
    /**
     * Index of the dragged item.
     */
    dragIndex?: number;
    /**
     * Index of the drop position.
     */
    dropIndex?: number;
}
/**
 * Custom edit event.
 * @group Events
 */
interface TableEditEvent<RowData = any> {
    /**
     * Field to be edited.
     */
    field?: string;
    /**
     * New value.
     */
    data?: RowData;
}
/**
 * Custom edit init event.
 * @see {@link Table.onEditInit}
 * @group Events
 */
interface TableEditInitEvent extends TableEditEvent {
    /**
     * Edited element index.
     */
    index: number;
}
/**
 * Custom edit cancel event.
 * @see {@link Table.onEditCancel}
 * @group Events
 */
interface TableEditCancelEvent extends TableEditEvent {
    /**
     * Browser event.
     */
    originalEvent?: Event;
    /**
     * Edited element index.
     */
    index?: number;
}
/**
 * Custom edit complete event.
 * @see {@link Table.onEditComplete}
 * @group Events
 */
interface TableEditCompleteEvent extends TableEditCancelEvent {
}
/**
 * Custom checkbox toggle event.
 * @see {@link Table.onHeaderCheckboxToggle}
 * @group Events
 */
interface TableHeaderCheckboxToggleEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Checked state.
     */
    checked: boolean;
}
/**
 * Custom all selection change event.
 * @see {@link Table.selectAllChange}
 * @group Events
 */
interface TableSelectAllChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Checked state.
     */
    checked: boolean;
}
/**
 * Custom table filter popover button props options.
 */
interface TableFilterButtonPopoverPropsOptions {
    /**
     * Add rule button props
     */
    addRule: ButtonProps | undefined;
    /**
     * Remove rule button props
     */
    removeRule: ButtonProps | undefined;
    /**
     * Apply button props
     */
    apply: ButtonProps | undefined;
    /**
     * Apply button props
     */
    clear: ButtonProps | undefined;
}
/**
 * Custom table filter inline button props options.
 */
interface TableFilterButtonInlinePropsOptions {
    /**
     * Apply button props
     */
    clear: ButtonProps | undefined;
}
/**
 * Custom table filter buttons' props options.
 */
interface TableFilterButtonPropsOptions {
    /**
     * Filter button props
     */
    filter: ButtonProps | undefined;
    /**
     * Inline filter buttons' options
     */
    inline: TableFilterButtonInlinePropsOptions | undefined;
    /**
     * Popover filter buttons' options
     */
    popover: TableFilterButtonPopoverPropsOptions | undefined;
}
/**
 * Custom CSV export options.
 */
interface ExportCSVOptions {
    /**
     * Boolean value determining whether to export all selected values.
     */
    selectionOnly?: boolean;
    /**
     * Boolean value determining whether to export all values.
     */
    allValues?: boolean;
}
/**
 * Defines valid templates in Table.
 * @group Templates
 */
interface TableTemplates {
    /**
     * Custom caption template.
     */
    caption(): TemplateRef<any>;
    /**
     * Custom grouped header template.
     * @param {Object} context - row data.
     */
    headergrouped(context: {
        /**
         * Row data.
         */
        $implicit?: any;
        /**
         * Row index.
         */
        rowIndex?: number;
        /**
         * Columns.
         */
        columns?: any[];
        /**
         * Editing state.
         */
        editing?: boolean;
        /**
         * Frozen state.
         */
        frozen?: boolean;
    }): TemplateRef<any>;
    /**
     * Custom grouped header template.
     * @param {Object} context - header data.
     */
    header(context: {
        /**
         * Field.
         */
        $implicit: string;
    }): TemplateRef<{
        $implicit: string;
    }>;
    /**
     * Custom body template.
     * @param {Object} context - body data.
     */
    body(context: {
        /**
         * Columns.
         */
        $implicit: any;
        /**
         * Frozen state.
         */
        frozen: boolean;
    }): TemplateRef<{
        $implicit: any;
        frozen: boolean;
    }>;
    /**
     * Custom loading body template.
     * @param {Object} context - loading body data.
     */
    loadingbody(context: {
        /**
         * Row span.
         */
        rowspan?: number;
        /**
         * Row group.
         */
        rowgroup?: number;
    }): TemplateRef<any>;
    /**
     * Custom footer template.
     * @param {Object} context - footer data.
     */
    footer(context: {
        /**
         * Field.
         */
        $implicit: string;
    }): TemplateRef<{
        $implicit: string;
    }>;
    /**
     * Custom footer template.
     * @param {Object} context - footer data.
     */
    footergrouped(context: {
        /**
         * Columns.
         */
        $implicit: any[];
    }): TemplateRef<any[]>;
    /**
     * Custom column group template.
     * @param {Object} context - columns data.
     */
    colgroup(context: {
        /**
         * Columns.
         */
        $implicit: any[];
    }): TemplateRef<any[]>;
    /**
     * Custom summary template.
     */
    summary(): TemplateRef<any>;
    /**
     * Custom expanded row template.
     * @param {Object} context - expanded row data.
     */
    expandedrow(context: {
        /**
         * Row span.
         */
        rowspan?: number;
        /**
         * Row group.
         */
        rowgroup?: number;
        /**
         * Expanded state.
         */
        expanded: boolean;
    }): TemplateRef<any>;
    /**
     * Custom group header template.
     * @param {Object} context - row data.
     */
    groupheader(context: {
        /**
         * Row data.
         */
        $implicit?: any;
        /**
         * Row index.
         */
        rowIndex?: number;
        /**
         * Columns.
         */
        columns?: any[];
        /**
         * Editing state.
         */
        editing?: boolean;
        /**
         * Frozen state.
         */
        frozen?: boolean;
    }): TemplateRef<any>;
    /**
     * Custom group footer template.
     * @param {TableRowContext} context - row data.
     */
    groupfooter(context: {
        /**
         * Row data.
         */
        $implicit?: any;
        /**
         * Row index.
         */
        rowIndex?: number;
        /**
         * Columns.
         */
        columns?: any[];
        /**
         * Editing state.
         */
        editing?: boolean;
        /**
         * Frozen state.
         */
        frozen?: boolean;
    }): TemplateRef<any>;
    /**
     * Custom frozen header template.
     * @param {*} context - columns.
     */
    frozenheader(): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom frozen body template.
     * @param {Object} context - row data.
     */
    frozenbody(context: {
        /**
         * Row data.
         */
        $implicit?: any;
        /**
         * Row index.
         */
        rowIndex?: number;
        /**
         * Columns.
         */
        columns?: any[];
        /**
         * Editing state.
         */
        editing?: boolean;
        /**
         * Frozen state.
         */
        frozen?: boolean;
    }): TemplateRef<any>;
    /**
     * Custom frozen footer template.
     * @param {*} context - columns.
     */
    frozenfooter(): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom frozen column group template.
     * @param {*} context - columns.
     */
    frozencolgroup(): TemplateRef<{
        $implicit: any[];
    }>;
    /**
     * Custom frozen expanded row template.
     * @param {Object} context - row data.
     */
    frozenexpandedrow(context: {
        /**
         * Row span.
         */
        rowspan?: number;
        /**
         * Row group.
         */
        rowgroup?: number;
        /**
         * Expanded state.
         */
        expanded: boolean;
    }): TemplateRef<any>;
    /**
     * Custom empty message template.
     */
    emptymessage(context: {
        /**
         * Columns
         */
        $implicit: any[];
        /**
         * Frozen state.
         */
        frozen: boolean;
    }): TemplateRef<{
        $implicit: any[];
        frozen: boolean;
    }>;
    /**
     * Custom paginator left template.
     */
    paginatorleft(): TemplateRef<any>;
    /**
     * Custom paginator right template.
     */
    paginatorright(): TemplateRef<any>;
    /**
     * Custom paginator dropdown trigger icon template.
     */
    paginatordropdownicon(): TemplateRef<any>;
    /**
     * Custom paginator dropdown item template.
     */
    paginatordropdownitem(): TemplateRef<any>;
    /**
     * Custom paginator first page link icon template.
     */
    paginatorfirstpagelinkicon(): TemplateRef<any>;
    /**
     * Custom paginator last page link icon template.
     */
    paginatorlastpagelinkicon(): TemplateRef<any>;
    /**
     * Custom paginator previous page link icon template.
     */
    paginatorpreviouspagelinkicon(): TemplateRef<any>;
    /**
     * Custom paginator next page link icon template.
     */
    paginatornextpagelinkicon(): TemplateRef<any>;
    /**
     * Custom loading icon template.
     */
    loadingicon(): TemplateRef<any>;
    /**
     * Custom reorder indicator up icon template.
     */
    reorderindicatorupicon(): TemplateRef<any>;
    /**
     * Custom reorder indicator down icon template.
     */
    reorderindicatordownicon(): TemplateRef<any>;
    /**
     * Custom sort icon template.
     */
    sorticon(context: {
        /**
         * Sort order.
         */
        $implicit: number;
    }): TemplateRef<{
        $implicit: number;
    }>;
    /**
     * Custom checkbox icon template.
     * @param {Object} context - checkbox data.
     */
    checkboxicon(context: {
        /**
         * Checkbox state.
         */
        $implicit: boolean;
        /**
         * Partial selection state of row node.
         */
        partialSelected: boolean;
    }): TemplateRef<{
        $implicit: boolean;
        partialSelected: boolean;
    }>;
    /**
     * Custom header checkbox icon template.
     * @param {Object} context - checkbox data.
     */
    headercheckboxicon(context: {
        /**
         * Checked state.
         */
        $implicit: boolean;
    }): TemplateRef<{
        $implicit: boolean;
    }>;
}

declare class TableService {
    private sortSource;
    private selectionSource;
    private contextMenuSource;
    private valueSource;
    private columnsSource;
    sortSource$: rxjs.Observable<SortMeta | SortMeta[]>;
    selectionSource$: rxjs.Observable<unknown>;
    contextMenuSource$: rxjs.Observable<any>;
    valueSource$: rxjs.Observable<any>;
    columnsSource$: rxjs.Observable<unknown>;
    onSort(sortMeta: SortMeta | SortMeta[] | null): void;
    onSelectionChange(): void;
    onContextMenu(data: any): void;
    onValueChange(value: any): void;
    onColumnsChange(columns: any[]): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TableService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TableService>;
}
/**
 * Table displays data in tabular format.
 * @group Components
 */
declare class Table<RowData = any> extends BaseComponent implements OnInit, AfterViewInit, AfterContentInit, BlockableUI, OnChanges {
    /**
     * An array of objects to represent dynamic columns that are frozen.
     * @group Props
     */
    frozenColumns: any[] | undefined;
    /**
     * An array of objects to display as frozen.
     * @group Props
     */
    frozenValue: any[] | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the table.
     * @group Props
     */
    tableStyle: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Style class of the table.
     * @group Props
     */
    tableStyleClass: string | undefined;
    /**
     * When specified as true, enables the pagination.
     * @group Props
     */
    paginator: boolean | undefined;
    /**
     * Number of page links to display in paginator.
     * @group Props
     */
    pageLinks: number;
    /**
     * Array of integer/object values to display inside rows per page dropdown of paginator
     * @group Props
     */
    rowsPerPageOptions: any[] | undefined;
    /**
     * Whether to show it even there is only one page.
     * @group Props
     */
    alwaysShowPaginator: boolean;
    /**
     * Position of the paginator, options are "top", "bottom" or "both".
     * @group Props
     */
    paginatorPosition: 'top' | 'bottom' | 'both';
    /**
     * Custom style class for paginator
     * @group Props
     */
    paginatorStyleClass: string | undefined;
    /**
     * Target element to attach the paginator dropdown overlay, valid values are "body" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]="mydiv" for a div element having #mydiv as variable name).
     * @group Props
     */
    paginatorDropdownAppendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;
    /**
     * Paginator dropdown height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    paginatorDropdownScrollHeight: string;
    /**
     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}
     * @group Props
     */
    currentPageReportTemplate: string;
    /**
     * Whether to display current page report.
     * @group Props
     */
    showCurrentPageReport: boolean | undefined;
    /**
     * Whether to display a dropdown to navigate to any page.
     * @group Props
     */
    showJumpToPageDropdown: boolean | undefined;
    /**
     * Whether to display a input to navigate to any page.
     * @group Props
     */
    showJumpToPageInput: boolean | undefined;
    /**
     * When enabled, icons are displayed on paginator to go first and last page.
     * @group Props
     */
    showFirstLastIcon: boolean;
    /**
     * Whether to show page links.
     * @group Props
     */
    showPageLinks: boolean;
    /**
     * Sort order to use when an unsorted column gets sorted by user interaction.
     * @group Props
     */
    defaultSortOrder: number;
    /**
     * Defines whether sorting works on single column or on multiple columns.
     * @group Props
     */
    sortMode: 'single' | 'multiple';
    /**
     * When true, resets paginator to first page after sorting. Available only when sortMode is set to single.
     * @group Props
     */
    resetPageOnSort: boolean;
    /**
     * Specifies the selection mode, valid values are "single" and "multiple".
     * @group Props
     */
    selectionMode: 'single' | 'multiple' | undefined | null;
    /**
     * When enabled with paginator and checkbox selection mode, the select all checkbox in the header will select all rows on the current page.
     * @group Props
     */
    selectionPageOnly: boolean | undefined;
    /**
     * Selected row with a context menu.
     * @group Props
     */
    contextMenuSelection: any;
    /**
     * Callback to invoke on context menu selection change.
     * @param {*} object - row data.
     * @group Emits
     */
    contextMenuSelectionChange: EventEmitter<any>;
    /**
     *  Defines the behavior of context menu selection, in "separate" mode context menu updates contextMenuSelection property whereas in joint mode selection property is used instead so that when row selection is enabled, both row selection and context menu selection use the same property.
     * @group Props
     */
    contextMenuSelectionMode: string;
    /**
     * A property to uniquely identify a record in data.
     * @group Props
     */
    dataKey: string | undefined;
    /**
     * Defines whether metaKey should be considered for the selection. On touch enabled devices, metaKeySelection is turned off automatically.
     * @group Props
     */
    metaKeySelection: boolean | undefined;
    /**
     * Defines if the row is selectable.
     * @group Props
     */
    rowSelectable: (row: {
        data: any;
        index: number;
    }) => boolean | undefined;
    /**
     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.
     * @group Props
     */
    rowTrackBy: Function;
    /**
     * Defines if data is loaded and interacted with in lazy manner.
     * @group Props
     */
    lazy: boolean;
    /**
     * Whether to call lazy loading on initialization.
     * @group Props
     */
    lazyLoadOnInit: boolean;
    /**
     * Algorithm to define if a row is selected, valid values are "equals" that compares by reference and "deepEquals" that compares all fields.
     * @group Props
     */
    compareSelectionBy: 'equals' | 'deepEquals';
    /**
     * Character to use as the csv separator.
     * @group Props
     */
    csvSeparator: string;
    /**
     * Name of the exported file.
     * @group Props
     */
    exportFilename: string;
    /**
     * An array of FilterMetadata objects to provide external filters.
     * @group Props
     */
    filters: {
        [s: string]: FilterMetadata | FilterMetadata[];
    };
    /**
     * An array of fields as string to use in global filtering.
     * @group Props
     */
    globalFilterFields: string[] | undefined;
    /**
     * Delay in milliseconds before filtering the data.
     * @group Props
     */
    filterDelay: number;
    /**
     * Locale to use in filtering. The default locale is the host environment's current locale.
     * @group Props
     */
    filterLocale: string | undefined;
    /**
     * Map instance to keep the expanded rows where key of the map is the data key of the row.
     * @group Props
     */
    expandedRowKeys: {
        [s: string]: boolean;
    };
    /**
     * Map instance to keep the rows being edited where key of the map is the data key of the row.
     * @group Props
     */
    editingRowKeys: {
        [s: string]: boolean;
    };
    /**
     * Whether multiple rows can be expanded at any time. Valid values are "multiple" and "single".
     * @group Props
     */
    rowExpandMode: 'multiple' | 'single';
    /**
     * Enables scrollable tables.
     * @group Props
     */
    scrollable: boolean | undefined;
    /**
     * Type of the row grouping, valid values are "subheader" and "rowspan".
     * @group Props
     */
    rowGroupMode: 'subheader' | 'rowspan' | undefined;
    /**
     * Height of the scroll viewport in fixed pixels or the "flex" keyword for a dynamic size.
     * @group Props
     */
    scrollHeight: string | undefined;
    /**
     * Whether the data should be loaded on demand during scroll.
     * @group Props
     */
    virtualScroll: boolean | undefined;
    /**
     * Height of a row to use in calculations of virtual scrolling.
     * @group Props
     */
    virtualScrollItemSize: number | undefined;
    /**
     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.
     * @group Props
     */
    virtualScrollOptions: ScrollerOptions | undefined;
    /**
     * Threshold in milliseconds to delay lazy loading during scrolling.
     * @group Props
     */
    virtualScrollDelay: number;
    /**
     * Width of the frozen columns container.
     * @group Props
     */
    frozenWidth: string | undefined;
    /**
     * Local ng-template varilable of a ContextMenu.
     * @group Props
     */
    contextMenu: any;
    /**
     * When enabled, columns can be resized using drag and drop.
     * @group Props
     */
    resizableColumns: boolean | undefined;
    /**
     * Defines whether the overall table width should change on column resize, valid values are "fit" and "expand".
     * @group Props
     */
    columnResizeMode: string;
    /**
     * When enabled, columns can be reordered using drag and drop.
     * @group Props
     */
    reorderableColumns: boolean | undefined;
    /**
     * Displays a loader to indicate data load is in progress.
     * @group Props
     */
    loading: boolean | undefined;
    /**
     * The icon to show while indicating data load is in progress.
     * @group Props
     */
    loadingIcon: string | undefined;
    /**
     * Whether to show the loading mask when loading property is true.
     * @group Props
     */
    showLoader: boolean;
    /**
     * Adds hover effect to rows without the need for selectionMode. Note that tr elements that can be hovered need to have "p-selectable-row" class for rowHover to work.
     * @group Props
     */
    rowHover: boolean | undefined;
    /**
     * Whether to use the default sorting or a custom one using sortFunction.
     * @group Props
     */
    customSort: boolean | undefined;
    /**
     * Whether to use the initial sort badge or not.
     * @group Props
     */
    showInitialSortBadge: boolean;
    /**
     * Export function.
     * @group Props
     */
    exportFunction: Function | undefined;
    /**
     * Custom export header of the column to be exported as CSV.
     * @group Props
     */
    exportHeader: string | undefined;
    /**
     * Unique identifier of a stateful table to use in state storage.
     * @group Props
     */
    stateKey: string | undefined;
    /**
     * Defines where a stateful table keeps its state, valid values are "session" for sessionStorage and "local" for localStorage.
     * @group Props
     */
    stateStorage: 'session' | 'local';
    /**
     * Defines the editing mode, valid values are "cell" and "row".
     * @group Props
     */
    editMode: 'cell' | 'row';
    /**
     * Field name to use in row grouping.
     * @group Props
     */
    groupRowsBy: any;
    /**
     * Defines the size of the table.
     * @group Props
     */
    size: 'small' | 'large' | undefined;
    /**
     * Whether to show grid lines between cells.
     * @group Props
     */
    showGridlines: boolean | undefined;
    /**
     * Whether to display rows with alternating colors.
     * @group Props
     */
    stripedRows: boolean | undefined;
    /**
     * Order to sort when default row grouping is enabled.
     * @group Props
     */
    groupRowsByOrder: number;
    /**
     * Defines the responsive mode, valid options are "stack" and "scroll".
     * @deprecated since v20.0.0, always defaults to scroll, stack mode needs custom implementation
     * @group Props
     */
    responsiveLayout: string;
    /**
     * The breakpoint to define the maximum width boundary when using stack responsive layout.
     * @group Props
     */
    breakpoint: string;
    /**
     * Locale to be used in paginator formatting.
     * @group Props
     */
    paginatorLocale: string | undefined;
    /**
     * An array of objects to display.
     * @group Props
     */
    get value(): RowData[];
    set value(val: RowData[]);
    /**
     * An array of objects to represent dynamic columns.
     * @group Props
     */
    get columns(): any[] | undefined;
    set columns(cols: any[] | undefined);
    /**
     * Index of the first row to be displayed.
     * @group Props
     */
    get first(): number | null | undefined;
    set first(val: number | null | undefined);
    /**
     * Number of rows to display per page.
     * @group Props
     */
    get rows(): number | undefined;
    set rows(val: number | undefined);
    /**
     * Number of total records, defaults to length of value when not defined.
     * @group Props
     */
    totalRecords: number;
    /**
     * Name of the field to sort data by default.
     * @group Props
     */
    get sortField(): string | undefined | null;
    set sortField(val: string | undefined | null);
    /**
     * Order to sort when default sorting is enabled.
     * @group Props
     */
    get sortOrder(): number;
    set sortOrder(val: number);
    /**
     * An array of SortMeta objects to sort the data by default in multiple sort mode.
     * @group Props
     */
    get multiSortMeta(): SortMeta[] | undefined | null;
    set multiSortMeta(val: SortMeta[] | undefined | null);
    /**
     * Selected row in single mode or an array of values in multiple mode.
     * @group Props
     */
    get selection(): any;
    set selection(val: any);
    /**
     * Whether all data is selected.
     * @group Props
     */
    get selectAll(): boolean | null;
    set selectAll(val: boolean | null);
    /**
     * Emits when the all of the items selected or unselected.
     * @param {TableSelectAllChangeEvent} event - custom  all selection change event.
     * @group Emits
     */
    selectAllChange: EventEmitter<TableSelectAllChangeEvent>;
    /**
     * Callback to invoke on selection changed.
     * @param {any | null} value - selected data.
     * @group Emits
     */
    selectionChange: EventEmitter<any | null>;
    /**
     * Callback to invoke when a row is selected.
     * @param {TableRowSelectEvent} event - custom select event.
     * @group Emits
     */
    onRowSelect: EventEmitter<TableRowSelectEvent<RowData>>;
    /**
     * Callback to invoke when a row is unselected.
     * @param {TableRowUnSelectEvent} event - custom unselect event.
     * @group Emits
     */
    onRowUnselect: EventEmitter<TableRowUnSelectEvent<RowData>>;
    /**
     * Callback to invoke when pagination occurs.
     * @param {TablePageEvent} event - custom pagination event.
     * @group Emits
     */
    onPage: EventEmitter<TablePageEvent>;
    /**
     * Callback to invoke when a column gets sorted.
     * @param {Object} object - sort meta.
     * @group Emits
     */
    onSort: EventEmitter<{
        multisortmeta: SortMeta[];
    } | any>;
    /**
     * Callback to invoke when data is filtered.
     * @param {TableFilterEvent} event - custom filtering event.
     * @group Emits
     */
    onFilter: EventEmitter<TableFilterEvent>;
    /**
     * Callback to invoke when paging, sorting or filtering happens in lazy mode.
     * @param {TableLazyLoadEvent} event - custom lazy loading event.
     * @group Emits
     */
    onLazyLoad: EventEmitter<TableLazyLoadEvent>;
    /**
     * Callback to invoke when a row is expanded.
     * @param {TableRowExpandEvent} event - custom row expand event.
     * @group Emits
     */
    onRowExpand: EventEmitter<TableRowExpandEvent<RowData>>;
    /**
     * Callback to invoke when a row is collapsed.
     * @param {TableRowCollapseEvent} event - custom row collapse event.
     * @group Emits
     */
    onRowCollapse: EventEmitter<TableRowCollapseEvent>;
    /**
     * Callback to invoke when a row is selected with right click.
     * @param {TableContextMenuSelectEvent} event - custom context menu select event.
     * @group Emits
     */
    onContextMenuSelect: EventEmitter<TableContextMenuSelectEvent<RowData>>;
    /**
     * Callback to invoke when a column is resized.
     * @param {TableColResizeEvent} event - custom column resize event.
     * @group Emits
     */
    onColResize: EventEmitter<TableColResizeEvent>;
    /**
     * Callback to invoke when a column is reordered.
     * @param {TableColumnReorderEvent} event - custom column reorder event.
     * @group Emits
     */
    onColReorder: EventEmitter<TableColumnReorderEvent>;
    /**
     * Callback to invoke when a row is reordered.
     * @param {TableRowReorderEvent} event - custom row reorder event.
     * @group Emits
     */
    onRowReorder: EventEmitter<TableRowReorderEvent>;
    /**
     * Callback to invoke when a cell switches to edit mode.
     * @param {TableEditInitEvent} event - custom edit init event.
     * @group Emits
     */
    onEditInit: EventEmitter<TableEditInitEvent>;
    /**
     * Callback to invoke when cell edit is completed.
     * @param {TableEditCompleteEvent} event - custom edit complete event.
     * @group Emits
     */
    onEditComplete: EventEmitter<TableEditCompleteEvent>;
    /**
     * Callback to invoke when cell edit is cancelled with escape key.
     * @param {TableEditCancelEvent} event - custom edit cancel event.
     * @group Emits
     */
    onEditCancel: EventEmitter<TableEditCancelEvent>;
    /**
     * Callback to invoke when state of header checkbox changes.
     * @param {TableHeaderCheckboxToggleEvent} event - custom header checkbox event.
     * @group Emits
     */
    onHeaderCheckboxToggle: EventEmitter<TableHeaderCheckboxToggleEvent>;
    /**
     * A function to implement custom sorting, refer to sorting section for details.
     * @param {any} any - sort meta.
     * @group Emits
     */
    sortFunction: EventEmitter<any>;
    /**
     * Callback to invoke on pagination.
     * @param {number} number - first element.
     * @group Emits
     */
    firstChange: EventEmitter<number>;
    /**
     * Callback to invoke on rows change.
     * @param {number} number - Row count.
     * @group Emits
     */
    rowsChange: EventEmitter<number>;
    /**
     * Callback to invoke table state is saved.
     * @param {TableState} object - table state.
     * @group Emits
     */
    onStateSave: EventEmitter<TableState>;
    /**
     * Callback to invoke table state is restored.
     * @param {TableState} object - table state.
     * @group Emits
     */
    onStateRestore: EventEmitter<TableState>;
    resizeHelperViewChild: Nullable<ElementRef>;
    reorderIndicatorUpViewChild: Nullable<ElementRef>;
    reorderIndicatorDownViewChild: Nullable<ElementRef>;
    wrapperViewChild: Nullable<ElementRef>;
    tableViewChild: Nullable<ElementRef>;
    tableHeaderViewChild: Nullable<ElementRef>;
    tableFooterViewChild: Nullable<ElementRef>;
    scroller: Nullable<Scroller>;
    _templates: Nullable<QueryList<PrimeTemplate>>;
    _value: RowData[];
    _columns: any[] | undefined;
    _totalRecords: number;
    _first: number | null | undefined;
    _rows: number | undefined;
    filteredValue: any[] | undefined | null;
    _headerTemplate: TemplateRef<any>;
    headerTemplate: Nullable<TemplateRef<any>>;
    _headerGroupedTemplate: TemplateRef<any>;
    headerGroupedTemplate: Nullable<TemplateRef<any>>;
    _bodyTemplate: TemplateRef<any>;
    bodyTemplate: Nullable<TemplateRef<any>>;
    _loadingBodyTemplate: TemplateRef<any>;
    loadingBodyTemplate: Nullable<TemplateRef<any>>;
    _captionTemplate: TemplateRef<any>;
    captionTemplate: Nullable<TemplateRef<any>>;
    _footerTemplate: TemplateRef<any>;
    footerTemplate: Nullable<TemplateRef<any>>;
    _footerGroupedTemplate: TemplateRef<any>;
    footerGroupedTemplate: Nullable<TemplateRef<any>>;
    _summaryTemplate: TemplateRef<any>;
    summaryTemplate: Nullable<TemplateRef<any>>;
    _colGroupTemplate: TemplateRef<any>;
    colGroupTemplate: Nullable<TemplateRef<any>>;
    _expandedRowTemplate: TemplateRef<any>;
    expandedRowTemplate: Nullable<TemplateRef<any>>;
    _groupHeaderTemplate: TemplateRef<any>;
    groupHeaderTemplate: Nullable<TemplateRef<any>>;
    _groupFooterTemplate: TemplateRef<any>;
    groupFooterTemplate: Nullable<TemplateRef<any>>;
    _frozenExpandedRowTemplate: TemplateRef<any>;
    frozenExpandedRowTemplate: Nullable<TemplateRef<any>>;
    _frozenHeaderTemplate: TemplateRef<any>;
    frozenHeaderTemplate: Nullable<TemplateRef<any>>;
    _frozenBodyTemplate: TemplateRef<any>;
    frozenBodyTemplate: Nullable<TemplateRef<any>>;
    _frozenFooterTemplate: TemplateRef<any>;
    frozenFooterTemplate: Nullable<TemplateRef<any>>;
    _frozenColGroupTemplate: TemplateRef<any>;
    frozenColGroupTemplate: Nullable<TemplateRef<any>>;
    _emptyMessageTemplate: TemplateRef<any>;
    emptyMessageTemplate: Nullable<TemplateRef<any>>;
    _paginatorLeftTemplate: TemplateRef<any>;
    paginatorLeftTemplate: Nullable<TemplateRef<any>>;
    _paginatorRightTemplate: TemplateRef<any>;
    paginatorRightTemplate: Nullable<TemplateRef<any>>;
    _paginatorDropdownItemTemplate: TemplateRef<any>;
    paginatorDropdownItemTemplate: Nullable<TemplateRef<any>>;
    _loadingIconTemplate: TemplateRef<any>;
    loadingIconTemplate: Nullable<TemplateRef<any>>;
    _reorderIndicatorUpIconTemplate: TemplateRef<any>;
    reorderIndicatorUpIconTemplate: Nullable<TemplateRef<any>>;
    _reorderIndicatorDownIconTemplate: TemplateRef<any>;
    reorderIndicatorDownIconTemplate: Nullable<TemplateRef<any>>;
    _sortIconTemplate: TemplateRef<any>;
    sortIconTemplate: Nullable<TemplateRef<any>>;
    _checkboxIconTemplate: TemplateRef<any>;
    checkboxIconTemplate: Nullable<TemplateRef<any>>;
    _headerCheckboxIconTemplate: TemplateRef<any>;
    headerCheckboxIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorDropdownIconTemplate: TemplateRef<any>;
    paginatorDropdownIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorFirstPageLinkIconTemplate: TemplateRef<any>;
    paginatorFirstPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorLastPageLinkIconTemplate: TemplateRef<any>;
    paginatorLastPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorPreviousPageLinkIconTemplate: TemplateRef<any>;
    paginatorPreviousPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    _paginatorNextPageLinkIconTemplate: TemplateRef<any>;
    paginatorNextPageLinkIconTemplate: Nullable<TemplateRef<any>>;
    selectionKeys: any;
    lastResizerHelperX: number | undefined;
    reorderIconWidth: number | undefined;
    reorderIconHeight: number | undefined;
    draggedColumn: any;
    draggedRowIndex: number | undefined | null;
    droppedRowIndex: number | undefined | null;
    rowDragging: boolean | undefined | null;
    dropPosition: number | undefined | null;
    editingCell: Element | undefined | null;
    editingCellData: any;
    editingCellField: any;
    editingCellRowIndex: number | undefined | null;
    selfClick: boolean | undefined | null;
    documentEditListener: any;
    _multiSortMeta: SortMeta[] | undefined | null;
    _sortField: string | undefined | null;
    _sortOrder: number;
    preventSelectionSetterPropagation: boolean | undefined;
    _selection: any;
    _selectAll: boolean | null;
    anchorRowIndex: number | undefined | null;
    rangeRowIndex: number | undefined;
    filterTimeout: any;
    initialized: boolean | undefined | null;
    rowTouched: boolean | undefined;
    restoringSort: boolean | undefined;
    restoringFilter: boolean | undefined;
    stateRestored: boolean | undefined;
    columnOrderStateRestored: boolean | undefined;
    columnWidthsState: string | undefined;
    tableWidthState: string | undefined;
    overlaySubscription: Subscription | undefined;
    resizeColumnElement: HTMLElement;
    columnResizing: boolean;
    rowGroupHeaderStyleObject: any;
    id: string;
    styleElement: any;
    responsiveStyleElement: any;
    overlayService: OverlayService;
    filterService: FilterService;
    tableService: TableService;
    zone: NgZone;
    _componentStyle: TableStyle;
    ngOnInit(): void;
    ngAfterContentInit(): void;
    ngAfterViewInit(): void;
    ngOnChanges(simpleChange: SimpleChanges): void;
    get processedData(): any[];
    private _initialColWidths;
    dataToRender(data: any): any;
    updateSelectionKeys(): void;
    onPageChange(event: TablePageEvent): void;
    sort(event: any): void;
    sortSingle(): void;
    sortMultiple(): void;
    multisortField(data1: any, data2: any, multiSortMeta: SortMeta[], index: number): any;
    compareValuesOnSort(value1: any, value2: any, order: any): number;
    getSortMeta(field: string): SortMeta;
    isSorted(field: string): boolean;
    handleRowClick(event: any): void;
    handleRowTouchEnd(event: Event): void;
    handleRowRightClick(event: any): void;
    selectRange(event: MouseEvent | KeyboardEvent, rowIndex: number, isMetaKeySelection?: boolean | undefined): void;
    clearSelectionRange(event: MouseEvent | KeyboardEvent): void;
    isSelected(rowData: any): boolean;
    findIndexInSelection(rowData: any): number;
    isRowSelectable(data: any, index: number): boolean;
    toggleRowWithRadio(event: any, rowData: any): void;
    toggleRowWithCheckbox(event: {
        originalEvent: Event;
        rowIndex: number;
    }, rowData: any): void;
    toggleRowsWithCheckbox({ originalEvent }: CheckboxChangeEvent, check: boolean): void;
    equals(data1: any, data2: any): boolean;
    filter(value: any, field: string, matchMode: string): void;
    filterGlobal(value: any, matchMode: string): void;
    isFilterBlank(filter: any): boolean;
    _filter(): void;
    executeLocalFilter(field: string, rowData: any, filterMeta: FilterMetadata): boolean;
    hasFilter(): boolean;
    createLazyLoadMetadata(): any;
    clear(): void;
    clearFilterValues(): void;
    reset(): void;
    getExportHeader(column: any): any;
    /**
     * Data export method.
     * @param {ExportCSVOptions} object - Export options.
     * @group Method
     */
    exportCSV(options?: ExportCSVOptions): void;
    onLazyItemLoad(event: LazyLoadMeta): void;
    /**
     * Resets scroll to top.
     * @group Method
     */
    resetScrollTop(): void;
    /**
     * Scrolls to given index when using virtual scroll.
     * @param {number} index - index of the element.
     * @group Method
     */
    scrollToVirtualIndex(index: number): void;
    /**
     * Scrolls to given index.
     * @param {ScrollToOptions} options - scroll options.
     * @group Method
     */
    scrollTo(options: any): void;
    updateEditingCell(cell: any, data: any, field: string, index: number): void;
    isEditingCellValid(): boolean;
    bindDocumentEditListener(): void;
    unbindDocumentEditListener(): void;
    initRowEdit(rowData: any): void;
    saveRowEdit(rowData: any, rowElement: HTMLTableRowElement): void;
    cancelRowEdit(rowData: any): void;
    toggleRow(rowData: any, event?: Event): void;
    isRowExpanded(rowData: any): boolean;
    isRowEditing(rowData: any): boolean;
    isSingleSelectionMode(): boolean;
    isMultipleSelectionMode(): boolean;
    onColumnResizeBegin(event: any): void;
    onColumnResize(event: any): void;
    onColumnResizeEnd(): void;
    private _totalTableWidth;
    onColumnDragStart(event: any, columnElement: any): void;
    onColumnDragEnter(event: any, dropHeader: any): void;
    onColumnDragLeave(event: Event): void;
    onColumnDrop(event: Event, dropColumn: any): void;
    resizeTableCells(newColumnWidth: number, nextColumnWidth: number | null): void;
    updateStyleElement(width: number[], colIndex: number, newColumnWidth: number, nextColumnWidth: number | null): void;
    onRowDragStart(event: any, index: number): void;
    onRowDragOver(event: MouseEvent, index: number, rowElement: any): void;
    onRowDragLeave(event: Event, rowElement: any): void;
    onRowDragEnd(event: Event): void;
    onRowDrop(event: Event, rowElement: any): void;
    isEmpty(): boolean;
    getBlockableElement(): HTMLElement;
    getStorage(): Storage;
    isStateful(): boolean;
    saveState(): void;
    clearState(): void;
    restoreState(): void;
    saveColumnWidths(state: any): void;
    setResizeTableWidth(width: string): void;
    restoreColumnWidths(): void;
    saveColumnOrder(state: any): void;
    restoreColumnOrder(): void;
    findColumnByKey(key: any): any;
    createStyleElement(): void;
    getGroupRowsMeta(): {
        field: any;
        order: number;
    };
    createResponsiveStyle(): void;
    destroyResponsiveStyle(): void;
    destroyStyleElement(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Table<any>, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Table<any>, "p-table", never, { "frozenColumns": { "alias": "frozenColumns"; "required": false; }; "frozenValue": { "alias": "frozenValue"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "tableStyle": { "alias": "tableStyle"; "required": false; }; "tableStyleClass": { "alias": "tableStyleClass"; "required": false; }; "paginator": { "alias": "paginator"; "required": false; }; "pageLinks": { "alias": "pageLinks"; "required": false; }; "rowsPerPageOptions": { "alias": "rowsPerPageOptions"; "required": false; }; "alwaysShowPaginator": { "alias": "alwaysShowPaginator"; "required": false; }; "paginatorPosition": { "alias": "paginatorPosition"; "required": false; }; "paginatorStyleClass": { "alias": "paginatorStyleClass"; "required": false; }; "paginatorDropdownAppendTo": { "alias": "paginatorDropdownAppendTo"; "required": false; }; "paginatorDropdownScrollHeight": { "alias": "paginatorDropdownScrollHeight"; "required": false; }; "currentPageReportTemplate": { "alias": "currentPageReportTemplate"; "required": false; }; "showCurrentPageReport": { "alias": "showCurrentPageReport"; "required": false; }; "showJumpToPageDropdown": { "alias": "showJumpToPageDropdown"; "required": false; }; "showJumpToPageInput": { "alias": "showJumpToPageInput"; "required": false; }; "showFirstLastIcon": { "alias": "showFirstLastIcon"; "required": false; }; "showPageLinks": { "alias": "showPageLinks"; "required": false; }; "defaultSortOrder": { "alias": "defaultSortOrder"; "required": false; }; "sortMode": { "alias": "sortMode"; "required": false; }; "resetPageOnSort": { "alias": "resetPageOnSort"; "required": false; }; "selectionMode": { "alias": "selectionMode"; "required": false; }; "selectionPageOnly": { "alias": "selectionPageOnly"; "required": false; }; "contextMenuSelection": { "alias": "contextMenuSelection"; "required": false; }; "contextMenuSelectionMode": { "alias": "contextMenuSelectionMode"; "required": false; }; "dataKey": { "alias": "dataKey"; "required": false; }; "metaKeySelection": { "alias": "metaKeySelection"; "required": false; }; "rowSelectable": { "alias": "rowSelectable"; "required": false; }; "rowTrackBy": { "alias": "rowTrackBy"; "required": false; }; "lazy": { "alias": "lazy"; "required": false; }; "lazyLoadOnInit": { "alias": "lazyLoadOnInit"; "required": false; }; "compareSelectionBy": { "alias": "compareSelectionBy"; "required": false; }; "csvSeparator": { "alias": "csvSeparator"; "required": false; }; "exportFilename": { "alias": "exportFilename"; "required": false; }; "filters": { "alias": "filters"; "required": false; }; "globalFilterFields": { "alias": "globalFilterFields"; "required": false; }; "filterDelay": { "alias": "filterDelay"; "required": false; }; "filterLocale": { "alias": "filterLocale"; "required": false; }; "expandedRowKeys": { "alias": "expandedRowKeys"; "required": false; }; "editingRowKeys": { "alias": "editingRowKeys"; "required": false; }; "rowExpandMode": { "alias": "rowExpandMode"; "required": false; }; "scrollable": { "alias": "scrollable"; "required": false; }; "rowGroupMode": { "alias": "rowGroupMode"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "virtualScroll": { "alias": "virtualScroll"; "required": false; }; "virtualScrollItemSize": { "alias": "virtualScrollItemSize"; "required": false; }; "virtualScrollOptions": { "alias": "virtualScrollOptions"; "required": false; }; "virtualScrollDelay": { "alias": "virtualScrollDelay"; "required": false; }; "frozenWidth": { "alias": "frozenWidth"; "required": false; }; "contextMenu": { "alias": "contextMenu"; "required": false; }; "resizableColumns": { "alias": "resizableColumns"; "required": false; }; "columnResizeMode": { "alias": "columnResizeMode"; "required": false; }; "reorderableColumns": { "alias": "reorderableColumns"; "required": false; }; "loading": { "alias": "loading"; "required": false; }; "loadingIcon": { "alias": "loadingIcon"; "required": false; }; "showLoader": { "alias": "showLoader"; "required": false; }; "rowHover": { "alias": "rowHover"; "required": false; }; "customSort": { "alias": "customSort"; "required": false; }; "showInitialSortBadge": { "alias": "showInitialSortBadge"; "required": false; }; "exportFunction": { "alias": "exportFunction"; "required": false; }; "exportHeader": { "alias": "exportHeader"; "required": false; }; "stateKey": { "alias": "stateKey"; "required": false; }; "stateStorage": { "alias": "stateStorage"; "required": false; }; "editMode": { "alias": "editMode"; "required": false; }; "groupRowsBy": { "alias": "groupRowsBy"; "required": false; }; "size": { "alias": "size"; "required": false; }; "showGridlines": { "alias": "showGridlines"; "required": false; }; "stripedRows": { "alias": "stripedRows"; "required": false; }; "groupRowsByOrder": { "alias": "groupRowsByOrder"; "required": false; }; "responsiveLayout": { "alias": "responsiveLayout"; "required": false; }; "breakpoint": { "alias": "breakpoint"; "required": false; }; "paginatorLocale": { "alias": "paginatorLocale"; "required": false; }; "value": { "alias": "value"; "required": false; }; "columns": { "alias": "columns"; "required": false; }; "first": { "alias": "first"; "required": false; }; "rows": { "alias": "rows"; "required": false; }; "totalRecords": { "alias": "totalRecords"; "required": false; }; "sortField": { "alias": "sortField"; "required": false; }; "sortOrder": { "alias": "sortOrder"; "required": false; }; "multiSortMeta": { "alias": "multiSortMeta"; "required": false; }; "selection": { "alias": "selection"; "required": false; }; "selectAll": { "alias": "selectAll"; "required": false; }; }, { "contextMenuSelectionChange": "contextMenuSelectionChange"; "selectAllChange": "selectAllChange"; "selectionChange": "selectionChange"; "onRowSelect": "onRowSelect"; "onRowUnselect": "onRowUnselect"; "onPage": "onPage"; "onSort": "onSort"; "onFilter": "onFilter"; "onLazyLoad": "onLazyLoad"; "onRowExpand": "onRowExpand"; "onRowCollapse": "onRowCollapse"; "onContextMenuSelect": "onContextMenuSelect"; "onColResize": "onColResize"; "onColReorder": "onColReorder"; "onRowReorder": "onRowReorder"; "onEditInit": "onEditInit"; "onEditComplete": "onEditComplete"; "onEditCancel": "onEditCancel"; "onHeaderCheckboxToggle": "onHeaderCheckboxToggle"; "sortFunction": "sortFunction"; "firstChange": "firstChange"; "rowsChange": "rowsChange"; "onStateSave": "onStateSave"; "onStateRestore": "onStateRestore"; }, ["_headerTemplate", "_headerGroupedTemplate", "_bodyTemplate", "_loadingBodyTemplate", "_captionTemplate", "_footerTemplate", "_footerGroupedTemplate", "_summaryTemplate", "_colGroupTemplate", "_expandedRowTemplate", "_groupHeaderTemplate", "_groupFooterTemplate", "_frozenExpandedRowTemplate", "_frozenHeaderTemplate", "_frozenBodyTemplate", "_frozenFooterTemplate", "_frozenColGroupTemplate", "_emptyMessageTemplate", "_paginatorLeftTemplate", "_paginatorRightTemplate", "_paginatorDropdownItemTemplate", "_loadingIconTemplate", "_reorderIndicatorUpIconTemplate", "_reorderIndicatorDownIconTemplate", "_sortIconTemplate", "_checkboxIconTemplate", "_headerCheckboxIconTemplate", "_paginatorDropdownIconTemplate", "_paginatorFirstPageLinkIconTemplate", "_paginatorLastPageLinkIconTemplate", "_paginatorPreviousPageLinkIconTemplate", "_paginatorNextPageLinkIconTemplate", "_templates"], never, false, never>;
    static ngAcceptInputType_paginator: unknown;
    static ngAcceptInputType_pageLinks: unknown;
    static ngAcceptInputType_alwaysShowPaginator: unknown;
    static ngAcceptInputType_showCurrentPageReport: unknown;
    static ngAcceptInputType_showJumpToPageDropdown: unknown;
    static ngAcceptInputType_showJumpToPageInput: unknown;
    static ngAcceptInputType_showFirstLastIcon: unknown;
    static ngAcceptInputType_showPageLinks: unknown;
    static ngAcceptInputType_defaultSortOrder: unknown;
    static ngAcceptInputType_resetPageOnSort: unknown;
    static ngAcceptInputType_selectionPageOnly: unknown;
    static ngAcceptInputType_metaKeySelection: unknown;
    static ngAcceptInputType_lazy: unknown;
    static ngAcceptInputType_lazyLoadOnInit: unknown;
    static ngAcceptInputType_filterDelay: unknown;
    static ngAcceptInputType_scrollable: unknown;
    static ngAcceptInputType_virtualScroll: unknown;
    static ngAcceptInputType_virtualScrollItemSize: unknown;
    static ngAcceptInputType_virtualScrollDelay: unknown;
    static ngAcceptInputType_resizableColumns: unknown;
    static ngAcceptInputType_reorderableColumns: unknown;
    static ngAcceptInputType_loading: unknown;
    static ngAcceptInputType_showLoader: unknown;
    static ngAcceptInputType_rowHover: unknown;
    static ngAcceptInputType_customSort: unknown;
    static ngAcceptInputType_showInitialSortBadge: unknown;
    static ngAcceptInputType_showGridlines: unknown;
    static ngAcceptInputType_stripedRows: unknown;
    static ngAcceptInputType_groupRowsByOrder: unknown;
}
declare class TableBody implements AfterViewInit, OnDestroy {
    dt: Table;
    tableService: TableService;
    cd: ChangeDetectorRef;
    el: ElementRef;
    columns: any[] | undefined;
    template: Nullable<TemplateRef<any>>;
    get value(): any[] | undefined;
    set value(val: any[] | undefined);
    frozen: boolean | undefined;
    frozenRows: boolean | undefined;
    scrollerOptions: any;
    subscription: Subscription;
    _value: any[] | undefined;
    ngAfterViewInit(): void;
    constructor(dt: Table, tableService: TableService, cd: ChangeDetectorRef, el: ElementRef);
    shouldRenderRowGroupHeader(value: any, rowData: any, i: number): boolean;
    shouldRenderRowGroupFooter(value: any, rowData: any, i: number): boolean;
    shouldRenderRowspan(value: any, rowData: any, i: number): boolean;
    calculateRowGroupSize(value: any, rowData: any, index: number): number;
    ngOnDestroy(): void;
    updateFrozenRowStickyPosition(): void;
    updateFrozenRowGroupHeaderStickyPosition(): void;
    getScrollerOption(option: any, options?: any): any;
    getRowIndex(rowIndex: number): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<TableBody, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TableBody, "[pTableBody]", never, { "columns": { "alias": "pTableBody"; "required": false; }; "template": { "alias": "pTableBodyTemplate"; "required": false; }; "value": { "alias": "value"; "required": false; }; "frozen": { "alias": "frozen"; "required": false; }; "frozenRows": { "alias": "frozenRows"; "required": false; }; "scrollerOptions": { "alias": "scrollerOptions"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_frozen: unknown;
    static ngAcceptInputType_frozenRows: unknown;
}
declare class RowGroupHeader {
    dt: Table;
    constructor(dt: Table);
    get getFrozenRowGroupHeaderStickyPosition(): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<RowGroupHeader, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<RowGroupHeader, "[pRowGroupHeader]", never, {}, {}, never, never, false, never>;
}
declare class FrozenColumn implements AfterViewInit {
    private el;
    private zone;
    get frozen(): boolean;
    set frozen(val: boolean);
    alignFrozen: string;
    constructor(el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    recalculateColumns(): void;
    _frozen: boolean;
    updateStickyPosition(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<FrozenColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<FrozenColumn, "[pFrozenColumn]", never, { "frozen": { "alias": "frozen"; "required": false; }; "alignFrozen": { "alias": "alignFrozen"; "required": false; }; }, {}, never, never, false, never>;
}
declare class SortableColumn extends BaseComponent implements OnInit, OnDestroy {
    #private;
    dt: Table;
    field: string | undefined;
    pSortableColumnDisabled: boolean | undefined;
    role: string;
    sorted: boolean | undefined;
    sortOrder: string | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TableStyle;
    constructor(dt: Table);
    ngOnInit(): void;
    updateSortState(): void;
    onClick(event: MouseEvent): void;
    onEnterKey(event: MouseEvent): void;
    isEnabled(): boolean;
    isFilterElement(element: HTMLElement): boolean;
    private isFilterElementIconOrButton;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SortableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<SortableColumn, "[pSortableColumn]", never, { "field": { "alias": "pSortableColumn"; "required": false; }; "pSortableColumnDisabled": { "alias": "pSortableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pSortableColumnDisabled: unknown;
}
declare class SortIcon extends BaseComponent implements OnInit, OnDestroy {
    dt: Table;
    cd: ChangeDetectorRef;
    field: string | undefined;
    subscription: Subscription | undefined;
    sortOrder: number | undefined;
    _componentStyle: TableStyle;
    constructor(dt: Table, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick(event: Event): void;
    updateSortState(): void;
    getMultiSortMetaIndex(): number;
    getBadgeValue(): number;
    isMultiSorted(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SortIcon, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SortIcon, "p-sortIcon", never, { "field": { "alias": "field"; "required": false; }; }, {}, never, never, false, never>;
}
declare class SelectableRow extends BaseComponent implements OnInit, OnDestroy {
    dt: Table;
    tableService: TableService;
    data: any;
    index: number | undefined;
    pSelectableRowDisabled: boolean | undefined;
    selected: boolean | undefined;
    subscription: Subscription | undefined;
    _componentStyle: TableStyle;
    constructor(dt: Table, tableService: TableService);
    setRowTabIndex(): 0 | -1;
    ngOnInit(): void;
    onClick(event: Event): void;
    onTouchEnd(event: Event): void;
    onKeyDown(event: KeyboardEvent): void;
    onArrowDownKey(event: KeyboardEvent): void;
    onArrowUpKey(event: KeyboardEvent): void;
    onEnterKey(event: KeyboardEvent): void;
    onEndKey(event: KeyboardEvent): void;
    onHomeKey(event: KeyboardEvent): void;
    onSpaceKey(event: any): void;
    focusRowChange(firstFocusableRow: any, currentFocusedRow: any): void;
    findLastSelectableRow(): any;
    findFirstSelectableRow(): any;
    findNextSelectableRow(row: HTMLTableRowElement): HTMLTableRowElement | null;
    findPrevSelectableRow(row: HTMLTableRowElement): HTMLTableRowElement | null;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<SelectableRow, "[pSelectableRow]", never, { "data": { "alias": "pSelectableRow"; "required": false; }; "index": { "alias": "pSelectableRowIndex"; "required": false; }; "pSelectableRowDisabled": { "alias": "pSelectableRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pSelectableRowDisabled: unknown;
}
declare class SelectableRowDblClick implements OnInit, OnDestroy {
    dt: Table;
    tableService: TableService;
    data: any;
    index: number | undefined;
    pSelectableRowDisabled: boolean | undefined;
    selected: boolean | undefined;
    subscription: Subscription | undefined;
    constructor(dt: Table, tableService: TableService);
    ngOnInit(): void;
    onClick(event: Event): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SelectableRowDblClick, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<SelectableRowDblClick, "[pSelectableRowDblClick]", never, { "data": { "alias": "pSelectableRowDblClick"; "required": false; }; "index": { "alias": "pSelectableRowIndex"; "required": false; }; "pSelectableRowDisabled": { "alias": "pSelectableRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pSelectableRowDisabled: unknown;
}
declare class ContextMenuRow {
    dt: Table;
    tableService: TableService;
    private el;
    data: any;
    index: number | undefined;
    pContextMenuRowDisabled: boolean | undefined;
    selected: boolean | undefined;
    subscription: Subscription | undefined;
    constructor(dt: Table, tableService: TableService, el: ElementRef);
    onContextMenu(event: Event): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ContextMenuRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<ContextMenuRow, "[pContextMenuRow]", never, { "data": { "alias": "pContextMenuRow"; "required": false; }; "index": { "alias": "pContextMenuRowIndex"; "required": false; }; "pContextMenuRowDisabled": { "alias": "pContextMenuRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pContextMenuRowDisabled: unknown;
}
declare class RowToggler {
    dt: Table;
    data: any;
    pRowTogglerDisabled: boolean | undefined;
    constructor(dt: Table);
    onClick(event: Event): void;
    isEnabled(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<RowToggler, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<RowToggler, "[pRowToggler]", never, { "data": { "alias": "pRowToggler"; "required": false; }; "pRowTogglerDisabled": { "alias": "pRowTogglerDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pRowTogglerDisabled: unknown;
}
declare class ResizableColumn extends BaseComponent implements AfterViewInit, OnDestroy {
    dt: Table;
    el: ElementRef;
    zone: NgZone;
    pResizableColumnDisabled: boolean | undefined;
    resizer: HTMLSpanElement | undefined;
    resizerMouseDownListener: VoidListener;
    resizerTouchStartListener: VoidListener;
    resizerTouchMoveListener: VoidListener;
    resizerTouchEndListener: VoidListener;
    documentMouseMoveListener: VoidListener;
    documentMouseUpListener: VoidListener;
    _componentStyle: TableStyle;
    constructor(dt: Table, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    bindDocumentEvents(): void;
    unbindDocumentEvents(): void;
    onMouseDown(event: MouseEvent): void;
    onTouchStart(event: TouchEvent): void;
    onTouchMove(event: TouchEvent): void;
    onDocumentMouseMove(event: MouseEvent): void;
    onDocumentMouseUp(event: MouseEvent): void;
    onTouchEnd(event: TouchEvent): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ResizableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<ResizableColumn, "[pResizableColumn]", never, { "pResizableColumnDisabled": { "alias": "pResizableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pResizableColumnDisabled: unknown;
}
declare class ReorderableColumn extends BaseComponent implements AfterViewInit, OnDestroy {
    dt: Table;
    el: ElementRef;
    zone: NgZone;
    pReorderableColumnDisabled: boolean | undefined;
    dragStartListener: VoidListener;
    dragOverListener: VoidListener;
    dragEnterListener: VoidListener;
    dragLeaveListener: VoidListener;
    mouseDownListener: VoidListener;
    _componentStyle: TableStyle;
    constructor(dt: Table, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    bindEvents(): void;
    unbindEvents(): void;
    onMouseDown(event: any): void;
    onDragStart(event: any): void;
    onDragOver(event: any): void;
    onDragEnter(event: any): void;
    onDragLeave(event: any): void;
    onDrop(event: any): void;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ReorderableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<ReorderableColumn, "[pReorderableColumn]", never, { "pReorderableColumnDisabled": { "alias": "pReorderableColumnDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pReorderableColumnDisabled: unknown;
}
declare class EditableColumn implements OnChanges, AfterViewInit, OnDestroy {
    dt: Table;
    el: ElementRef;
    zone: NgZone;
    data: any;
    field: any;
    rowIndex: number | undefined;
    pEditableColumnDisabled: boolean | undefined;
    pFocusCellSelector: string | undefined;
    overlayEventListener: any;
    constructor(dt: Table, el: ElementRef, zone: NgZone);
    ngOnChanges(changes: SimpleChanges): void;
    ngAfterViewInit(): void;
    onClick(event: MouseEvent): void;
    openCell(): void;
    closeEditingCell(completed: any, event: Event): void;
    onEnterKeyDown(event: KeyboardEvent): void;
    onTabKeyDown(event: KeyboardEvent): void;
    onEscapeKeyDown(event: KeyboardEvent): void;
    onShiftKeyDown(event: KeyboardEvent): void;
    onArrowDown(event: KeyboardEvent): void;
    onArrowUp(event: KeyboardEvent): void;
    onArrowLeft(event: KeyboardEvent): void;
    onArrowRight(event: KeyboardEvent): void;
    findCell(element: any): any;
    moveToPreviousCell(event: KeyboardEvent): void;
    moveToNextCell(event: KeyboardEvent): void;
    findPreviousEditableColumn(cell: any): HTMLTableCellElement | null;
    findNextEditableColumn(cell: any): HTMLTableCellElement | null;
    findNextEditableColumnByIndex(cell: Element, index: number): Element;
    findPrevEditableColumnByIndex(cell: Element, index: number): Element;
    isEnabled(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<EditableColumn, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<EditableColumn, "[pEditableColumn]", never, { "data": { "alias": "pEditableColumn"; "required": false; }; "field": { "alias": "pEditableColumnField"; "required": false; }; "rowIndex": { "alias": "pEditableColumnRowIndex"; "required": false; }; "pEditableColumnDisabled": { "alias": "pEditableColumnDisabled"; "required": false; }; "pFocusCellSelector": { "alias": "pFocusCellSelector"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pEditableColumnDisabled: unknown;
}
declare class EditableRow {
    el: ElementRef;
    data: any;
    pEditableRowDisabled: boolean | undefined;
    constructor(el: ElementRef);
    isEnabled(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<EditableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<EditableRow, "[pEditableRow]", never, { "data": { "alias": "pEditableRow"; "required": false; }; "pEditableRowDisabled": { "alias": "pEditableRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pEditableRowDisabled: unknown;
}
declare class InitEditableRow {
    dt: Table;
    editableRow: EditableRow;
    constructor(dt: Table, editableRow: EditableRow);
    onClick(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<InitEditableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<InitEditableRow, "[pInitEditableRow]", never, {}, {}, never, never, false, never>;
}
declare class SaveEditableRow {
    dt: Table;
    editableRow: EditableRow;
    constructor(dt: Table, editableRow: EditableRow);
    onClick(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SaveEditableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<SaveEditableRow, "[pSaveEditableRow]", never, {}, {}, never, never, false, never>;
}
declare class CancelEditableRow extends BaseComponent {
    dt: Table;
    editableRow: EditableRow;
    constructor(dt: Table, editableRow: EditableRow);
    _componentStyle: TableStyle;
    onClick(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<CancelEditableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<CancelEditableRow, "[pCancelEditableRow]", never, {}, {}, never, never, false, never>;
}
declare class CellEditor implements AfterContentInit {
    dt: Table;
    editableColumn: EditableColumn;
    editableRow: EditableRow;
    _templates: Nullable<QueryList<PrimeTemplate>>;
    _inputTemplate: TemplateRef<any>;
    _outputTemplate: TemplateRef<any>;
    inputTemplate: Nullable<TemplateRef<any>>;
    outputTemplate: Nullable<TemplateRef<any>>;
    constructor(dt: Table, editableColumn: EditableColumn, editableRow: EditableRow);
    ngAfterContentInit(): void;
    get editing(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<CellEditor, [null, { optional: true; }, { optional: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<CellEditor, "p-cellEditor", never, {}, {}, ["_inputTemplate", "_outputTemplate", "_templates"], never, false, never>;
}
declare class TableRadioButton implements OnInit, OnDestroy {
    dt: Table;
    cd: ChangeDetectorRef;
    value: any;
    readonly disabled: i0.InputSignalWithTransform<boolean, unknown>;
    readonly index: i0.InputSignalWithTransform<number, unknown>;
    readonly inputId: i0.InputSignal<string>;
    readonly name: i0.InputSignal<string>;
    ariaLabel: string | undefined;
    inputViewChild: Nullable<RadioButton>;
    checked: boolean | undefined;
    subscription: Subscription;
    constructor(dt: Table, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick(event: RadioButtonClickEvent): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TableRadioButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TableRadioButton, "p-tableRadioButton", never, { "value": { "alias": "value"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; "isSignal": true; }; "index": { "alias": "index"; "required": false; "isSignal": true; }; "inputId": { "alias": "inputId"; "required": false; "isSignal": true; }; "name": { "alias": "name"; "required": false; "isSignal": true; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; }, {}, never, never, false, never>;
}
declare class TableCheckbox implements OnInit, OnDestroy {
    dt: Table;
    tableService: TableService;
    cd: ChangeDetectorRef;
    value: any;
    readonly disabled: i0.InputSignalWithTransform<boolean, unknown>;
    readonly required: i0.InputSignalWithTransform<boolean, unknown>;
    readonly index: i0.InputSignalWithTransform<number, unknown>;
    readonly inputId: i0.InputSignal<string>;
    readonly name: i0.InputSignal<string>;
    ariaLabel: string | undefined;
    checked: boolean | undefined;
    subscription: Subscription;
    constructor(dt: Table, tableService: TableService, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick({ originalEvent }: CheckboxChangeEvent): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TableCheckbox, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TableCheckbox, "p-tableCheckbox", never, { "value": { "alias": "value"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; "isSignal": true; }; "required": { "alias": "required"; "required": false; "isSignal": true; }; "index": { "alias": "index"; "required": false; "isSignal": true; }; "inputId": { "alias": "inputId"; "required": false; "isSignal": true; }; "name": { "alias": "name"; "required": false; "isSignal": true; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; }, {}, never, never, false, never>;
}
declare class TableHeaderCheckbox implements OnInit, OnDestroy {
    dt: Table;
    tableService: TableService;
    cd: ChangeDetectorRef;
    readonly disabled: i0.InputSignalWithTransform<boolean, unknown>;
    readonly inputId: i0.InputSignal<string>;
    readonly name: i0.InputSignal<string>;
    ariaLabel: string | undefined;
    checked: boolean | undefined;
    selectionChangeSubscription: Subscription;
    valueChangeSubscription: Subscription;
    constructor(dt: Table, tableService: TableService, cd: ChangeDetectorRef);
    ngOnInit(): void;
    onClick(event: CheckboxChangeEvent): void;
    isDisabled(): boolean;
    ngOnDestroy(): void;
    updateCheckedState(): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<TableHeaderCheckbox, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TableHeaderCheckbox, "p-tableHeaderCheckbox", never, { "disabled": { "alias": "disabled"; "required": false; "isSignal": true; }; "inputId": { "alias": "inputId"; "required": false; "isSignal": true; }; "name": { "alias": "name"; "required": false; "isSignal": true; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; }, {}, never, never, false, never>;
}
declare class ReorderableRowHandle extends BaseComponent implements AfterViewInit {
    el: ElementRef;
    _componentStyle: TableStyle;
    constructor(el: ElementRef);
    ngAfterViewInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ReorderableRowHandle, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<ReorderableRowHandle, "[pReorderableRowHandle]", never, {}, {}, never, never, false, never>;
}
declare class ReorderableRow implements AfterViewInit {
    private renderer;
    dt: Table;
    el: ElementRef;
    zone: NgZone;
    index: number | undefined;
    pReorderableRowDisabled: boolean | undefined;
    mouseDownListener: VoidListener;
    dragStartListener: VoidListener;
    dragEndListener: VoidListener;
    dragOverListener: VoidListener;
    dragLeaveListener: VoidListener;
    dropListener: VoidListener;
    constructor(renderer: Renderer2, dt: Table, el: ElementRef, zone: NgZone);
    ngAfterViewInit(): void;
    bindEvents(): void;
    unbindEvents(): void;
    onMouseDown(event: Event): void;
    isHandleElement(element: HTMLElement): boolean;
    onDragStart(event: DragEvent): void;
    onDragEnd(event: DragEvent): void;
    onDragOver(event: DragEvent): void;
    onDragLeave(event: DragEvent): void;
    isEnabled(): boolean;
    onDrop(event: DragEvent): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ReorderableRow, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<ReorderableRow, "[pReorderableRow]", never, { "index": { "alias": "pReorderableRow"; "required": false; }; "pReorderableRowDisabled": { "alias": "pReorderableRowDisabled"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_pReorderableRowDisabled: unknown;
}
declare class ColumnFilter extends BaseComponent implements AfterContentInit {
    /**
     * Property represented by the column.
     * @group Props
     */
    field: string | undefined;
    /**
     * Type of the input.
     * @group Props
     */
    type: string;
    /**
     * Filter display.
     * @group Props
     */
    display: string;
    /**
     * Decides whether to display filter menu popup.
     * @group Props
     */
    showMenu: boolean;
    /**
     * Filter match mode.
     * @group Props
     */
    matchMode: string | undefined;
    /**
     * Filter operator.
     * @defaultValue 'AND'
     * @group Props
     */
    operator: string;
    /**
     * Decides whether to display filter operator.
     * @group Props
     */
    showOperator: boolean;
    /**
     * Decides whether to display clear filter button.
     * @group Props
     */
    showClearButton: boolean;
    /**
     * Decides whether to display apply filter button.
     * @group Props
     */
    showApplyButton: boolean;
    /**
     * Decides whether to display filter match modes.
     * @group Props
     */
    showMatchModes: boolean;
    /**
     * Decides whether to display add filter button.
     * @group Props
     */
    showAddButton: boolean;
    /**
     * Decides whether to close popup on clear button click.
     * @group Props
     */
    hideOnClear: boolean;
    /**
     * Filter placeholder.
     * @group Props
     */
    placeholder: string | undefined;
    /**
     * Filter match mode options.
     * @group Props
     */
    matchModeOptions: SelectItem[] | undefined;
    /**
     * Defines maximum amount of constraints.
     * @group Props
     */
    maxConstraints: number;
    /**
     * Defines minimum fraction of digits.
     * @group Props
     */
    minFractionDigits: number | undefined;
    /**
     * Defines maximum fraction of digits.
     * @group Props
     */
    maxFractionDigits: number | undefined;
    /**
     * Defines prefix of the filter.
     * @group Props
     */
    prefix: string | undefined;
    /**
     * Defines suffix of the filter.
     * @group Props
     */
    suffix: string | undefined;
    /**
     * Defines filter locale.
     * @group Props
     */
    locale: string | undefined;
    /**
     * Defines filter locale matcher.
     * @group Props
     */
    localeMatcher: string | undefined;
    /**
     * Enables currency input.
     * @group Props
     */
    currency: string | undefined;
    /**
     * Defines the display of the currency input.
     * @group Props
     */
    currencyDisplay: string | undefined;
    /**
     * Default trigger to run filtering on built-in text and numeric filters, valid values are 'enter' and 'input'.
     * @defaultValue enter
     * @group Props
     */
    filterOn: string | undefined;
    /**
     * Defines if filter grouping will be enabled.
     * @group Props
     */
    useGrouping: boolean;
    /**
     * Defines the visibility of buttons.
     * @group Props
     */
    showButtons: boolean;
    /**
     * Defines the aria-label of the form element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Used to pass all filter button property object
     * @defaultValue {
     filter: { severity: 'secondary', text: true, rounded: true },
     inline: {
     clear: { severity: 'secondary', text: true, rounded: true }
     },
     popover: {
     addRule: { severity: 'info', text: true, size: 'small' },
     removeRule: { severity: 'danger', text: true, size: 'small' },
     apply: { size: 'small' },
     clear: { outlined: true, size: 'small' }
     }
     }
     */
    filterButtonProps: TableFilterButtonPropsOptions;
    /**
     * Callback to invoke on overlay is shown.
     * @param {AnimationEvent} originalEvent - animation event.
     * @group Emits
     */
    onShow: EventEmitter<{
        originalEvent: AnimationEvent;
    }>;
    /**
     * Callback to invoke on overlay is hidden.
     * @param {AnimationEvent} originalEvent - animation event.
     * @group Emits
     */
    onHide: EventEmitter<{
        originalEvent: AnimationEvent;
    }>;
    icon: ElementRef | undefined;
    clearButtonViewChild: Nullable<ElementRef>;
    _templates: Nullable<QueryList<any>>;
    overlaySubscription: Subscription | undefined;
    _headerTemplate: TemplateRef<any>;
    headerTemplate: Nullable<TemplateRef<any>>;
    _filterTemplate: TemplateRef<any>;
    filterTemplate: Nullable<TemplateRef<any>>;
    _footerTemplate: TemplateRef<any>;
    footerTemplate: Nullable<TemplateRef<any>>;
    _filterIconTemplate: TemplateRef<any>;
    filterIconTemplate: Nullable<TemplateRef<any>>;
    _removeRuleIconTemplate: TemplateRef<any>;
    removeRuleIconTemplate: Nullable<TemplateRef<any>>;
    _addRuleIconTemplate: TemplateRef<any>;
    addRuleIconTemplate: Nullable<TemplateRef<any>>;
    _clearFilterIconTemplate: TemplateRef<any>;
    clearFilterIconTemplate: Nullable<TemplateRef<any>>;
    operatorOptions: any[] | undefined;
    overlayVisible: boolean | undefined;
    overlay: HTMLElement | undefined | null;
    scrollHandler: ConnectedOverlayScrollHandler | null | undefined;
    documentClickListener: VoidListener;
    documentResizeListener: VoidListener;
    matchModes: SelectItem[] | undefined;
    translationSubscription: Subscription | undefined;
    resetSubscription: Subscription | undefined;
    selfClick: boolean | undefined;
    overlayEventListener: any;
    overlayId: any;
    get fieldConstraints(): FilterMetadata[] | undefined | null;
    get showRemoveIcon(): boolean;
    get showMenuButton(): boolean;
    get isShowOperator(): boolean;
    get isShowAddConstraint(): boolean | undefined | null;
    get showMenuButtonLabel(): any;
    get applyButtonLabel(): string;
    get clearButtonLabel(): string;
    get addRuleButtonLabel(): string;
    get removeRuleButtonLabel(): string;
    get noFilterLabel(): string;
    get filterMenuButtonAriaLabel(): string;
    get removeRuleButtonAriaLabel(): string;
    get filterOperatorAriaLabel(): string;
    get filterConstraintAriaLabel(): string;
    dt: Table<any>;
    overlayService: OverlayService;
    hostName: string;
    parentInstance: any;
    ngOnInit(): void;
    generateMatchModeOptions(): void;
    generateOperatorOptions(): void;
    ngAfterContentInit(): void;
    initFieldFilterConstraint(): void;
    onMenuMatchModeChange(value: any, filterMeta: FilterMetadata): void;
    onRowMatchModeChange(matchMode: string): void;
    onRowMatchModeKeyDown(event: KeyboardEvent): void;
    onRowClearItemClick(): void;
    isRowMatchModeSelected(matchMode: string): boolean;
    addConstraint(): void;
    removeConstraint(filterMeta: FilterMetadata): void;
    onOperatorChange(value: any): void;
    toggleMenu(event: Event): void;
    onToggleButtonKeyDown(event: KeyboardEvent): void;
    onEscape(): void;
    findNextItem(item: HTMLLIElement): any;
    findPrevItem(item: HTMLLIElement): any;
    onContentClick(): void;
    onOverlayAnimationStart(event: AnimationEvent): void;
    onOverlayAnimationEnd(event: AnimationEvent): void;
    focusOnFirstElement(): void;
    getDefaultMatchMode(): string;
    getDefaultOperator(): string | undefined;
    hasRowFilter(): boolean;
    get hasFilter(): boolean;
    isOutsideClicked(event: any): boolean;
    bindDocumentClickListener(): void;
    unbindDocumentClickListener(): void;
    bindDocumentResizeListener(): void;
    unbindDocumentResizeListener(): void;
    bindScrollListener(): void;
    unbindScrollListener(): void;
    hide(): void;
    onOverlayHide(): void;
    clearFilter(): void;
    applyFilter(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ColumnFilter, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ColumnFilter, "p-columnFilter", never, { "field": { "alias": "field"; "required": false; }; "type": { "alias": "type"; "required": false; }; "display": { "alias": "display"; "required": false; }; "showMenu": { "alias": "showMenu"; "required": false; }; "matchMode": { "alias": "matchMode"; "required": false; }; "operator": { "alias": "operator"; "required": false; }; "showOperator": { "alias": "showOperator"; "required": false; }; "showClearButton": { "alias": "showClearButton"; "required": false; }; "showApplyButton": { "alias": "showApplyButton"; "required": false; }; "showMatchModes": { "alias": "showMatchModes"; "required": false; }; "showAddButton": { "alias": "showAddButton"; "required": false; }; "hideOnClear": { "alias": "hideOnClear"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "matchModeOptions": { "alias": "matchModeOptions"; "required": false; }; "maxConstraints": { "alias": "maxConstraints"; "required": false; }; "minFractionDigits": { "alias": "minFractionDigits"; "required": false; }; "maxFractionDigits": { "alias": "maxFractionDigits"; "required": false; }; "prefix": { "alias": "prefix"; "required": false; }; "suffix": { "alias": "suffix"; "required": false; }; "locale": { "alias": "locale"; "required": false; }; "localeMatcher": { "alias": "localeMatcher"; "required": false; }; "currency": { "alias": "currency"; "required": false; }; "currencyDisplay": { "alias": "currencyDisplay"; "required": false; }; "filterOn": { "alias": "filterOn"; "required": false; }; "useGrouping": { "alias": "useGrouping"; "required": false; }; "showButtons": { "alias": "showButtons"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "filterButtonProps": { "alias": "filterButtonProps"; "required": false; }; }, { "onShow": "onShow"; "onHide": "onHide"; }, ["_headerTemplate", "_filterTemplate", "_footerTemplate", "_filterIconTemplate", "_removeRuleIconTemplate", "_addRuleIconTemplate", "_clearFilterIconTemplate", "_templates"], never, false, never>;
    static ngAcceptInputType_showMenu: unknown;
    static ngAcceptInputType_showOperator: unknown;
    static ngAcceptInputType_showClearButton: unknown;
    static ngAcceptInputType_showApplyButton: unknown;
    static ngAcceptInputType_showMatchModes: unknown;
    static ngAcceptInputType_showAddButton: unknown;
    static ngAcceptInputType_hideOnClear: unknown;
    static ngAcceptInputType_maxConstraints: unknown;
    static ngAcceptInputType_minFractionDigits: unknown;
    static ngAcceptInputType_maxFractionDigits: unknown;
    static ngAcceptInputType_useGrouping: unknown;
    static ngAcceptInputType_showButtons: unknown;
}
declare class ColumnFilterFormElement implements OnInit {
    dt: Table;
    private colFilter;
    field: string | undefined;
    type: string | undefined;
    filterConstraint: FilterMetadata | undefined;
    filterTemplate: Nullable<TemplateRef<any>>;
    placeholder: string | undefined;
    minFractionDigits: number | undefined;
    maxFractionDigits: number | undefined;
    prefix: string | undefined;
    suffix: string | undefined;
    locale: string | undefined;
    localeMatcher: string | undefined;
    currency: string | undefined;
    currencyDisplay: string | undefined;
    useGrouping: boolean;
    ariaLabel: string | undefined;
    filterOn: string | undefined;
    get showButtons(): boolean;
    filterCallback: any;
    constructor(dt: Table, colFilter: ColumnFilter);
    ngOnInit(): void;
    onModelChange(value: any): void;
    onTextInputEnterKeyDown(event: KeyboardEvent): void;
    onNumericInputKeyDown(event: KeyboardEvent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ColumnFilterFormElement, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ColumnFilterFormElement, "p-columnFilterFormElement", never, { "field": { "alias": "field"; "required": false; }; "type": { "alias": "type"; "required": false; }; "filterConstraint": { "alias": "filterConstraint"; "required": false; }; "filterTemplate": { "alias": "filterTemplate"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "minFractionDigits": { "alias": "minFractionDigits"; "required": false; }; "maxFractionDigits": { "alias": "maxFractionDigits"; "required": false; }; "prefix": { "alias": "prefix"; "required": false; }; "suffix": { "alias": "suffix"; "required": false; }; "locale": { "alias": "locale"; "required": false; }; "localeMatcher": { "alias": "localeMatcher"; "required": false; }; "currency": { "alias": "currency"; "required": false; }; "currencyDisplay": { "alias": "currencyDisplay"; "required": false; }; "useGrouping": { "alias": "useGrouping"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "filterOn": { "alias": "filterOn"; "required": false; }; }, {}, never, never, false, never>;
    static ngAcceptInputType_minFractionDigits: unknown;
    static ngAcceptInputType_maxFractionDigits: unknown;
    static ngAcceptInputType_useGrouping: unknown;
}
declare class TableModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TableModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TableModule, [typeof Table, typeof SortableColumn, typeof FrozenColumn, typeof RowGroupHeader, typeof SelectableRow, typeof RowToggler, typeof ContextMenuRow, typeof ResizableColumn, typeof ReorderableColumn, typeof EditableColumn, typeof CellEditor, typeof TableBody, typeof SortIcon, typeof TableRadioButton, typeof TableCheckbox, typeof TableHeaderCheckbox, typeof ReorderableRowHandle, typeof ReorderableRow, typeof SelectableRowDblClick, typeof EditableRow, typeof InitEditableRow, typeof SaveEditableRow, typeof CancelEditableRow, typeof ColumnFilter, typeof ColumnFilterFormElement], [typeof i1.CommonModule, typeof i2.PaginatorModule, typeof i3.InputTextModule, typeof i4.SelectModule, typeof i5.FormsModule, typeof i6.ButtonModule, typeof i7.SelectButtonModule, typeof i8.DatePickerModule, typeof i9.InputNumberModule, typeof i10.BadgeModule, typeof i11.CheckboxModule, typeof i12.ScrollerModule, typeof i13.ArrowDownIcon, typeof i14.ArrowUpIcon, typeof i15.SpinnerIcon, typeof i16.SortAltIcon, typeof i17.SortAmountUpAltIcon, typeof i18.SortAmountDownIcon, typeof i19.FilterIcon, typeof i20.FilterFillIcon, typeof i21.FilterSlashIcon, typeof i22.PlusIcon, typeof i23.TrashIcon, typeof i24.RadioButtonModule], [typeof Table, typeof i25.SharedModule, typeof SortableColumn, typeof FrozenColumn, typeof RowGroupHeader, typeof SelectableRow, typeof RowToggler, typeof ContextMenuRow, typeof ResizableColumn, typeof ReorderableColumn, typeof EditableColumn, typeof CellEditor, typeof SortIcon, typeof TableRadioButton, typeof TableCheckbox, typeof TableHeaderCheckbox, typeof ReorderableRowHandle, typeof ReorderableRow, typeof SelectableRowDblClick, typeof EditableRow, typeof InitEditableRow, typeof SaveEditableRow, typeof CancelEditableRow, typeof ColumnFilter, typeof ColumnFilterFormElement, typeof i12.ScrollerModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TableModule>;
}

/**
 * Defines valid templates in Column Filter.
 * @group Templates
 */
interface TableColumnFilterTemplates {
    /**
     * Custom filter template.
     */
    filter(context: {
        /**
         * filterConstraint.value.
         */
        $implicit?: string;
        /**
         * filter callback.
         */
        filterCallback?: (value: string) => void;
        /**
         * Type of the input.
         */
        type?: string;
        /**
         * Filter constraint.
         */
        filterConstraint?: string;
        /**
         * Input placeholder.
         */
        placeholder?: boolean;
        /**
         * Minimum fraction of digits.
         */
        minFractionDigits?: number;
        /**
         * Maximum fraction of digits.
         */
        maxFractionDigits?: number;
        /**
         * Input prefix.
         */
        prefix?: string;
        /**
         * Input suffix.
         */
        suffix?: string;
        /**
         * Locale.
         */
        locale?: string;
        /**
         * Locale matcher.
         */
        localeMatcher?: string;
        /**
         * Enables currency input.
         */
        currency?: boolean;
        /**
         * Display of the currency input.
         */
        currencyDisplay?: boolean;
        /**
         * Defines if filter grouping will be enabled.
         */
        useGrouping?: boolean;
        /**
         * Defines the visibility of buttons.
         */
        showButtons?: boolean;
    }): TemplateRef<any>;
    /**
     * Custom header template.
     */
    header(): TemplateRef<any>;
    /**
     * Custom footer template.
     */
    footer(): TemplateRef<any>;
    /**
     * Custom filter icon template.
     */
    filterIcon(): TemplateRef<any>;
    /**
     * Custom remove rule icon template.
     */
    removeRuleIcon(): TemplateRef<any>;
    /**
     * Custom add rule icon template.
     */
    addRuleIcon(): TemplateRef<any>;
    /**
     * Custom clear filter icon template.
     */
    clearFilterIcon(): TemplateRef<any>;
}
/**
 * Defines valid properties in ColumnFilter component.
 * @group Components
 */
interface ColumnFilterProps {
    /**
     * Property represented by the column.
     * @defaultValue text
     * @group Props
     */
    field: string | undefined;
    /**
     * Type of the input.
     * @defaultValue text
     * @group Props
     */
    type: string;
    /**
     * Filter display.
     * @defaultValue row
     * @group Props
     */
    display: string;
    /**
     * Decides whether to display filter menu popup.
     * @defaultValue true
     * @group Props
     */
    showMenu: boolean;
    /**
     * Filter match mode.
     * @group Props
     */
    matchMode: string | undefined;
    /**
     * Filter operator.
     * @defaultValue 'AND'
     * @group Props
     */
    operator: string;
    /**
     * Decides whether to display filter operator.
     * @defaultValue true
     * @group Props
     */
    showOperator: boolean;
    /**
     * Decides whether to display clear filter button.
     * @defaultValue true
     * @group Props
     */
    showClearButton: boolean;
    /**
     * Decides whether to display apply filter button.
     * @defaultValue true
     * @group Props
     */
    showApplyButton: boolean;
    /**
     * Decides whether to display filter match modes.
     * @defaultValue true
     * @group Props
     */
    showMatchModes: boolean;
    /**
     * Decides whether to display add filter button.
     * @defaultValue true
     * @group Props
     */
    showAddButton: boolean;
    /**
     * Decides whether to close popup on clear button click.
     * @defaultValue true
     * @group Props
     */
    hideOnClear: boolean;
    /**
     * Filter placeholder.
     * @group Props
     */
    placeholder: string | undefined;
    /**
     * Filter match mode options.
     * @group Props
     */
    matchModeOptions: SelectItem[] | undefined;
    /**
     * Defines maximum amount of constraints.
     * @defaultValue 2
     * @group Props
     */
    maxConstraints: number;
    /**
     * Defines minimum fraction of digits.
     * @group Props
     */
    minFractionDigits: number | undefined;
    /**
     * Defines maximum fraction of digits.
     * @group Props
     */
    maxFractionDigits: number | undefined;
    /**
     * Defines prefix of the filter.
     * @group Props
     */
    prefix: string | undefined;
    /**
     * Defines suffix of the filter.
     * @group Props
     */
    suffix: string | undefined;
    /**
     * Defines filter locale.
     * @group Props
     */
    locale: string | undefined;
    /**
     * Defines filter locale matcher.
     * @group Props
     */
    localeMatcher: string | undefined;
    /**
     * Enables currency input.
     * @group Props
     */
    currency: boolean | undefined;
    /**
     * Defines the display of the currency input.
     * @group Props
     */
    currencyDisplay: string | undefined;
    /**
     * Defines if filter grouping will be enabled.
     * @defaultValue true
     * @group Props
     */
    useGrouping: boolean;
    /**
     * Defines the visibility of buttons.
     * @defaultValue true
     * @group Props
     */
    showButtons: boolean;
    /**
     * Defines the aria-label of the form element.
     * @group Props
     */
    ariaLabel: string | undefined;
}

export { CancelEditableRow, CellEditor, ColumnFilter, ColumnFilterFormElement, ContextMenuRow, EditableColumn, EditableRow, FrozenColumn, InitEditableRow, ReorderableColumn, ReorderableRow, ReorderableRowHandle, ResizableColumn, RowGroupHeader, RowToggler, SaveEditableRow, SelectableRow, SelectableRowDblClick, SortIcon, SortableColumn, Table, TableBody, TableCheckbox, TableClasses, TableHeaderCheckbox, TableModule, TableRadioButton, TableService, TableStyle };
export type { ColumnFilterProps, ExportCSVOptions, TableColResizeEvent, TableColumnFilterTemplates, TableColumnReorderEvent, TableContextMenuSelectEvent, TableContextMenuSelectionChangeEvent, TableEditCancelEvent, TableEditCompleteEvent, TableEditEvent, TableEditInitEvent, TableFilterButtonInlinePropsOptions, TableFilterButtonPopoverPropsOptions, TableFilterButtonPropsOptions, TableFilterEvent, TableHeaderCheckboxToggleEvent, TableLazyLoadEvent, TablePageEvent, TableRowCollapseEvent, TableRowExpandEvent, TableRowReorderEvent, TableRowSelectEvent, TableRowUnSelectEvent, TableSelectAllChangeEvent, TableTemplates };
