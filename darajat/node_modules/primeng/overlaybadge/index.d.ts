import { BaseComponent } from 'primeng/basecomponent';
import { BaseStyle } from 'primeng/base';
import * as i0 from '@angular/core';
import * as i1 from 'primeng/api';

declare class OverlayBadgeStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<OverlayBadgeStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<OverlayBadgeStyle>;
}

/**
 * OverlayPanel is a container component positioned as connected to its target.
 * @group Components
 */
declare class OverlayBadge extends BaseComponent {
    /**
     * Class of the element.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Inline style of the element.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Size of the badge, valid options are "large" and "xlarge".
     * @group Props
     */
    badgeSize: 'small' | 'large' | 'xlarge' | null | undefined;
    /**
     * Severity type of the badge.
     * @group Props
     */
    severity: 'secondary' | 'info' | 'success' | 'warn' | 'danger' | 'contrast' | null | undefined;
    /**
     * Value to display inside the badge.
     * @group Props
     */
    value: string | number | null | undefined;
    /**
     * When specified, disables the component.
     * @group Props
     */
    badgeDisabled: boolean;
    /**
     * Size of the badge, valid options are "large" and "xlarge".
     * @group Props
     * @deprecated use badgeSize instead.
     */
    set size(value: 'large' | 'xlarge' | 'small' | undefined | null);
    get size(): "large" | "xlarge" | "small" | undefined | null;
    _size: 'large' | 'xlarge' | 'small' | undefined | null;
    _componentStyle: OverlayBadgeStyle;
    constructor();
    static ɵfac: i0.ɵɵFactoryDeclaration<OverlayBadge, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<OverlayBadge, "p-overlayBadge, p-overlay-badge, p-overlaybadge", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "style": { "alias": "style"; "required": false; }; "badgeSize": { "alias": "badgeSize"; "required": false; }; "severity": { "alias": "severity"; "required": false; }; "value": { "alias": "value"; "required": false; }; "badgeDisabled": { "alias": "badgeDisabled"; "required": false; }; "size": { "alias": "size"; "required": false; }; }, {}, never, ["*"], true, never>;
    static ngAcceptInputType_badgeDisabled: unknown;
}
declare class OverlayBadgeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<OverlayBadgeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<OverlayBadgeModule, never, [typeof OverlayBadge, typeof i1.SharedModule], [typeof OverlayBadge, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<OverlayBadgeModule>;
}

export { OverlayBadge, OverlayBadgeModule, OverlayBadgeStyle };
