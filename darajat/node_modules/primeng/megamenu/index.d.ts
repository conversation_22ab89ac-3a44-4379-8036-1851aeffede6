import * as i0 from '@angular/core';
import { TemplateRef, EventEmitter, ElementRef, AfterContentInit, OnDestroy, OnInit, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { MegaMenuItem, PrimeTemplate, MenuItem } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import { VoidListener } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * MegaMenu is navigation component that displays submenus together.
 *
 * [Live Demo](https://www.primeng.org/megamenu/)
 *
 * @module megamenustyle
 *
 */
declare enum MegaMenuClasses {
    /**
     * Class name of the root element
     */
    root = "p-megamenu",
    /**
     * Class name of the start element
     */
    start = "p-megamenu-start",
    /**
     * Class name of the button element
     */
    button = "p-megamenu-button",
    /**
     * Class name of the root list element
     */
    rootList = "p-megamenu-root-list",
    /**
     * Class name of the submenu item element
     */
    submenuItem = "p-megamenu-submenu-item",
    /**
     * Class name of the item element
     */
    item = "p-megamenu-item",
    /**
     * Class name of the item content element
     */
    itemContent = "p-megamenu-item-content",
    /**
     * Class name of the item link element
     */
    itemLink = "p-megamenu-item-link",
    /**
     * Class name of the item icon element
     */
    itemIcon = "p-megamenu-item-icon",
    /**
     * Class name of the item label element
     */
    itemLabel = "p-megamenu-item-label",
    /**
     * Class name of the submenu icon element
     */
    submenuIcon = "p-megamenu-submenu-icon",
    /**
     * Class name of the panel element
     */
    panel = "p-megamenu-panel",
    /**
     * Class name of the grid element
     */
    grid = "p-megamenu-grid",
    /**
     * Class name of the submenu element
     */
    submenu = "p-megamenu-submenu",
    /**
     * Class name of the submenu item label element
     */
    submenuItemLabel = "p-megamenu-submenu-item-label",
    /**
     * Class name of the separator element
     */
    separator = "p-megamenu-separator",
    /**
     * Class name of the end element
     */
    end = "p-megamenu-end"
}
declare class MegaMenuStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-megamenu-mobile': any;
            'p-megamenu-mobile-active': any;
            'p-megamenu-horizontal': boolean;
            'p-megamenu-vertical': boolean;
        })[];
        start: string;
        button: string;
        rootList: string;
        submenuLabel: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => (string | {
            'p-disabled': any;
        })[];
        item: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => any[];
        itemContent: string;
        itemLink: string;
        itemIcon: string;
        itemLabel: string;
        submenuIcon: string;
        overlay: string;
        grid: string;
        column: ({ instance, processedItem }: {
            instance: any;
            processedItem: any;
        }) => any;
        submenu: string;
        separator: string;
        end: string;
    };
    inlineStyles: {
        rootList: ({ instance }: {
            instance: any;
        }) => {
            'max-height': any;
            overflow: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<MegaMenuStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<MegaMenuStyle>;
}
interface MegaMenuStyle extends BaseStyle {
}

declare class MegaMenuSub extends BaseComponent {
    id: string | undefined;
    items: any[] | undefined;
    itemTemplate: TemplateRef<any> | undefined;
    menuId: string | undefined;
    ariaLabel: string | undefined;
    ariaLabelledBy: string | undefined;
    level: number;
    focusedItemId: string | undefined;
    disabled: boolean;
    orientation: string | undefined;
    activeItem: any;
    submenu: any;
    queryMatches: boolean;
    mobileActive: boolean;
    scrollHeight: string;
    tabindex: number;
    root: boolean;
    itemClick: EventEmitter<any>;
    itemMouseEnter: EventEmitter<any>;
    menuFocus: EventEmitter<any>;
    menuBlur: EventEmitter<any>;
    menuKeydown: EventEmitter<any>;
    menubarViewChild: ElementRef;
    megaMenu: MegaMenu;
    _componentStyle: MegaMenuStyle;
    onItemClick(event: any, processedItem: any): void;
    getItemProp(processedItem: any, name: string, params?: any | null): any;
    getItemId(processedItem: any): string;
    getSubListId(processedItem: any): string;
    getItemLabel(processedItem: any): string;
    isSubmenuVisible(submenu: any): boolean;
    isItemVisible(processedItem: any): boolean;
    isItemActive(processedItem: any): boolean;
    isItemDisabled(processedItem: any): boolean;
    isItemFocused(processedItem: any): boolean;
    isItemGroup(processedItem: any): boolean;
    getAriaSetSize(): number;
    getAriaPosInset(index: number): number;
    onItemMouseEnter(param: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<MegaMenuSub, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<MegaMenuSub, "p-megaMenuSub, p-megamenu-sub", never, { "id": { "alias": "id"; "required": false; }; "items": { "alias": "items"; "required": false; }; "itemTemplate": { "alias": "itemTemplate"; "required": false; }; "menuId": { "alias": "menuId"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "level": { "alias": "level"; "required": false; }; "focusedItemId": { "alias": "focusedItemId"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; "activeItem": { "alias": "activeItem"; "required": false; }; "submenu": { "alias": "submenu"; "required": false; }; "queryMatches": { "alias": "queryMatches"; "required": false; }; "mobileActive": { "alias": "mobileActive"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "root": { "alias": "root"; "required": false; }; }, { "itemClick": "itemClick"; "itemMouseEnter": "itemMouseEnter"; "menuFocus": "menuFocus"; "menuBlur": "menuBlur"; "menuKeydown": "menuKeydown"; }, never, never, true, never>;
    static ngAcceptInputType_level: unknown;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_queryMatches: unknown;
    static ngAcceptInputType_mobileActive: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_root: unknown;
}
/**
 * MegaMenu is navigation component that displays submenus together.
 * @group Components
 */
declare class MegaMenu extends BaseComponent implements AfterContentInit, OnDestroy, OnInit {
    /**
     * An array of menuitems.
     * @group Props
     */
    set model(value: MegaMenuItem[] | undefined);
    get model(): MegaMenuItem[] | undefined;
    /**
     * Class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Defines the orientation.
     * @group Props
     */
    orientation: 'horizontal' | 'vertical' | string;
    /**
     * Current id state as a string.
     * @group Props
     */
    id: string | undefined;
    /**
     * Defines a string value that labels an interactive element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Identifier of the underlying input element.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * The breakpoint to define the maximum width boundary.
     * @group Props
     */
    breakpoint: string;
    /**
     * Height of the viewport, a scrollbar is defined if height of list exceeds this value.
     * @group Props
     */
    scrollHeight: string;
    /**
     * When present, it specifies that the component should be disabled.
     * @group Props
     */
    disabled: boolean;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number;
    /**
     * Defines template option for start.
     * @group Templates
     */
    startTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for end.
     * @group Templates
     */
    endTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for menu icon.
     * @group Templates
     */
    menuIconTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for submenu icon.
     * @group Templates
     */
    submenuIconTemplate: TemplateRef<any> | undefined;
    /**
     * Defines template option for submenu icon.
     * @group Templates
     */
    itemTemplate: TemplateRef<any> | undefined;
    /**
     * Custom menu button template on responsive mode.
     * @group Templates
     */
    buttonTemplate: TemplateRef<any> | undefined;
    /**
     * Custom menu button icon template on responsive mode.
     * @group Templates
     */
    buttonIconTemplate: TemplateRef<any> | undefined;
    templates: QueryList<PrimeTemplate> | undefined;
    menubuttonViewChild: ElementRef | undefined;
    rootmenu: MegaMenuSub | undefined;
    _startTemplate: TemplateRef<any> | undefined;
    _endTemplate: TemplateRef<any> | undefined;
    _menuIconTemplate: TemplateRef<any> | undefined;
    _submenuIconTemplate: TemplateRef<any> | undefined;
    _itemTemplate: TemplateRef<any> | undefined;
    _buttonTemplate: TemplateRef<any> | undefined;
    _buttonIconTemplate: TemplateRef<any> | undefined;
    outsideClickListener: VoidListener;
    resizeListener: (event: any) => void;
    dirty: boolean;
    focused: boolean;
    activeItem: i0.WritableSignal<any>;
    focusedItemInfo: i0.WritableSignal<any>;
    searchValue: string;
    searchTimeout: any;
    _processedItems: any[];
    _model: MegaMenuItem[] | undefined;
    _componentStyle: MegaMenuStyle;
    private matchMediaListener;
    private query;
    queryMatches: boolean;
    mobileActive: boolean;
    get visibleItems(): any;
    get processedItems(): any[];
    get focusedItemId(): any;
    constructor();
    ngOnInit(): void;
    ngAfterContentInit(): void;
    bindMatchMediaListener(): void;
    unbindMatchMediaListener(): void;
    createProcessedItems(items: any, level?: number, parent?: {}, parentKey?: string, columnIndex?: any): any[];
    getItemProp(item: any, name: string): any;
    onItemClick(event: any): void;
    onItemMouseEnter(event: any): void;
    menuButtonClick(event: any): void;
    menuButtonKeydown(event: any): void;
    toggle(event: MouseEvent): void;
    show(): void;
    scrollInView(index?: number): void;
    onItemChange(event: any): void;
    hide(event?: any, isFocus?: boolean): void;
    onMenuFocus(event: any): void;
    onMenuBlur(event: any): void;
    onKeyDown(event: KeyboardEvent): void;
    findFirstFocusedItemIndex(): any;
    findFirstItemIndex(): any;
    findSelectedItemIndex(): any;
    isProcessedItemGroup(processedItem: any): boolean;
    isSelected(processedItem: any): boolean;
    isValidSelectedItem(processedItem: any): boolean;
    isValidItem(processedItem: any): boolean;
    isItemDisabled(item: any): boolean;
    isItemSeparator(item: any): boolean;
    isItemMatched(processedItem: any): boolean;
    isProccessedItemGroup(processedItem: any): boolean;
    searchItems(event: any, char: string): boolean;
    getProccessedItemLabel(processedItem: any): any;
    getItemLabel(item: any): any;
    changeFocusedItemInfo(event: any, index: any): void;
    onArrowDownKey(event: KeyboardEvent): void;
    onArrowRightKey(event: KeyboardEvent): void;
    onArrowUpKey(event: KeyboardEvent): void;
    onArrowLeftKey(event: KeyboardEvent): void;
    onHomeKey(event: KeyboardEvent): void;
    onEndKey(event: KeyboardEvent): void;
    onSpaceKey(event: KeyboardEvent): void;
    onEscapeKey(event: KeyboardEvent): void;
    onTabKey(event: KeyboardEvent): void;
    onEnterKey(event: KeyboardEvent): void;
    findVisibleItem(index: any): any;
    findLastFocusedItemIndex(): any;
    findLastItemIndex(): number;
    findPrevItemIndex(index: number): number;
    findNextItemIndex(index: number): any;
    bindResizeListener(): void;
    bindOutsideClickListener(): void;
    unbindOutsideClickListener(): void;
    unbindResizeListener(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<MegaMenu, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<MegaMenu, "p-megaMenu, p-megamenu, p-mega-menu", never, { "model": { "alias": "model"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; "id": { "alias": "id"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "breakpoint": { "alias": "breakpoint"; "required": false; }; "scrollHeight": { "alias": "scrollHeight"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; }, {}, ["startTemplate", "endTemplate", "menuIconTemplate", "submenuIconTemplate", "itemTemplate", "buttonTemplate", "buttonIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_disabled: unknown;
    static ngAcceptInputType_tabindex: unknown;
}
declare class MegaMenuModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MegaMenuModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MegaMenuModule, never, [typeof MegaMenu, typeof i1.SharedModule], [typeof MegaMenu, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MegaMenuModule>;
}

/**
 * Defines valid templates in MegaMenu.
 * @group Templates
 */
interface MegaMenuTemplates {
    /**
     * Custom item template.
     */
    item(context: {
        /**
         * Item instance.
         */
        $implicit: MenuItem;
    }): TemplateRef<{
        $implicit: MenuItem;
    }>;
    /**
     * Custom template of start.
     */
    start(): TemplateRef<any>;
    /**
     * Custom template of submenuicon.
     */
    submenuicon(): TemplateRef<any>;
    /**
     * Custom template of end.
     */
    end(): TemplateRef<any>;
}

export { MegaMenu, MegaMenuClasses, MegaMenuModule, MegaMenuStyle, MegaMenuSub };
export type { MegaMenuTemplates };
