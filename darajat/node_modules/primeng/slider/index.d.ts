import * as i0 from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, EventEmitter, ElementRef } from '@angular/core';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { Nullable, VoidListener } from 'primeng/ts-helpers';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';
import * as i1 from 'primeng/api';

/**
 * Custom change event.
 * @see {@link Slider.onChange}
 * @group Events
 */
interface SliderChangeEvent {
    /**
     * Browser event.
     */
    event: Event;
    /**
     * New values.
     */
    values?: number[];
    /**
     * New value.
     */
    value?: number;
}
/**
 * Custom slide end event.
 * @see {@link Slider.onSlideEnd}
 * @group Events
 */
interface SliderSlideEndEvent {
    /**
     * Original event
     */
    originalEvent: Event;
    /**
     * New value.
     */
    value?: number;
    /**
     * New values.
     */
    values?: number[];
}

/**
 *
 * Slider is a component to provide input with a drag handle.
 *
 * [Live Demo](https://www.primeng.org/slider/)
 *
 * @module sliderstyle
 *
 */
declare enum SliderClasses {
    /**
     * Class name of the root element
     */
    root = "p-slider",
    /**
     * Class name of the range element
     */
    range = "p-slider-range",
    /**
     * Class name of the handle element
     */
    handle = "p-slider-handle"
}
declare class SliderStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-disabled': any;
            'p-invalid': any;
            'p-slider-horizontal': boolean;
            'p-slider-vertical': boolean;
            'p-slider-animate': any;
        })[];
        range: string;
        handle: string;
    };
    inlineStyles: {
        handle: {
            position: string;
        };
        range: {
            position: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<SliderStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SliderStyle>;
}
interface SliderStyle extends BaseStyle {
}

declare const SLIDER_VALUE_ACCESSOR: any;
/**
 * Slider is a component to provide input with a drag handle.
 * @group Components
 */
declare class Slider extends BaseEditableHolder implements OnDestroy {
    /**
     * When enabled, displays an animation on click of the slider bar.
     * @group Props
     */
    animate: boolean | undefined;
    /**
     * Mininum boundary value.
     * @group Props
     */
    min: number;
    /**
     * Maximum boundary value.
     * @group Props
     */
    max: number;
    /**
     * Orientation of the slider.
     * @group Props
     */
    orientation: 'horizontal' | 'vertical';
    /**
     * Step factor to increment/decrement the value.
     * @group Props
     */
    step: number | undefined;
    /**
     * When specified, allows two boundary values to be picked.
     * @group Props
     */
    range: boolean | undefined;
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Defines a string that labels the input for accessibility.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Callback to invoke on value change.
     * @param {SliderChangeEvent} event - Custom value change event.
     * @group Emits
     */
    onChange: EventEmitter<SliderChangeEvent>;
    /**
     * Callback to invoke when slide ended.
     * @param {SliderSlideEndEvent} event - Custom slide end event.
     * @group Emits
     */
    onSlideEnd: EventEmitter<SliderSlideEndEvent>;
    sliderHandle: Nullable<ElementRef>;
    sliderHandleStart: Nullable<ElementRef>;
    sliderHandleEnd: Nullable<ElementRef>;
    _componentStyle: SliderStyle;
    value: Nullable<number>;
    values: Nullable<number[]>;
    handleValue: Nullable<number>;
    handleValues: number[];
    diff: Nullable<number>;
    offset: Nullable<number>;
    bottom: Nullable<number>;
    dragging: Nullable<boolean>;
    dragListener: VoidListener;
    mouseupListener: VoidListener;
    initX: Nullable<number>;
    initY: Nullable<number>;
    barWidth: Nullable<number>;
    barHeight: Nullable<number>;
    sliderHandleClick: Nullable<boolean>;
    handleIndex: number;
    startHandleValue: any;
    startx: Nullable<number>;
    starty: Nullable<number>;
    private ngZone;
    onHostClick(event: MouseEvent): void;
    onMouseDown(event: Event, index?: number): void;
    onDragStart(event: TouchEvent, index?: number): void;
    onDrag(event: TouchEvent): void;
    onDragEnd(event: TouchEvent): void;
    onBarClick(event: Event): void;
    onKeyDown(event: any, index?: any): void;
    decrementValue(event: any, index: any, pageKey?: boolean): void;
    incrementValue(event: any, index: any, pageKey?: boolean): void;
    handleChange(event: Event): void;
    bindDragListeners(): void;
    unbindDragListeners(): void;
    setValueFromHandle(event: Event, handleValue: any): void;
    handleStepChange(newValue: number, oldValue: number): void;
    get rangeStartLeft(): string;
    get rangeStartBottom(): string;
    get rangeEndLeft(): string;
    get rangeEndBottom(): string;
    isVertical(): boolean;
    updateDomData(): void;
    calculateHandleValue(event: Event): number;
    updateHandleValue(): void;
    updateDiffAndOffset(): void;
    getDiff(): number;
    getOffset(): number;
    updateValue(val: number, event?: Event): void;
    getValueFromHandle(handleValue: number): number;
    getDecimalsCount(value: number): number;
    getNormalizedValue(val: number): number;
    ngOnDestroy(): void;
    get minVal(): number;
    get maxVal(): number;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Slider, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Slider, "p-slider", never, { "animate": { "alias": "animate"; "required": false; }; "min": { "alias": "min"; "required": false; }; "max": { "alias": "max"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; "step": { "alias": "step"; "required": false; }; "range": { "alias": "range"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; }, { "onChange": "onChange"; "onSlideEnd": "onSlideEnd"; }, never, never, true, never>;
    static ngAcceptInputType_animate: unknown;
    static ngAcceptInputType_min: unknown;
    static ngAcceptInputType_max: unknown;
    static ngAcceptInputType_step: unknown;
    static ngAcceptInputType_range: unknown;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_autofocus: unknown;
}
declare class SliderModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SliderModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SliderModule, never, [typeof Slider, typeof i1.SharedModule], [typeof Slider, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SliderModule>;
}

export { SLIDER_VALUE_ACCESSOR, Slider, SliderClasses, SliderModule, SliderStyle };
export type { SliderChangeEvent, SliderSlideEndEvent };
