import { BaseComponent } from 'primeng/basecomponent';
import { BaseStyle } from 'primeng/base';
import * as i0 from '@angular/core';
import * as i1 from 'primeng/api';

declare class InputIconStyle extends BaseStyle {
    name: string;
    classes: {
        root: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<InputIconStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<InputIconStyle>;
}

/**
 * InputIcon displays an icon.
 * @group Components
 */
declare class InputIcon extends BaseComponent {
    /**
     * Style class of the element.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    _componentStyle: InputIconStyle;
    static ɵfac: i0.ɵɵFactoryDeclaration<InputIcon, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<InputIcon, "p-inputicon, p-inputIcon", never, { "styleClass": { "alias": "styleClass"; "required": false; }; }, {}, never, ["*"], true, never>;
}
declare class InputIconModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<InputIconModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<InputIconModule, never, [typeof InputIcon, typeof i1.SharedModule], [typeof InputIcon, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<InputIconModule>;
}

export { InputIcon, InputIconModule, InputIconStyle };
