import * as i0 from '@angular/core';
import { TemplateRef, OnInit, AfterContentInit, EventEmitter, QueryList, ElementRef } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseInput } from 'primeng/baseinput';
import { Nullable } from 'primeng/ts-helpers';
import { BaseStyle } from 'primeng/base';

/**
 * Caret positions.
 * @group Types
 */
type Caret = {
    begin: number;
    end: number;
};
/**
 * Defines valid templates in InputMask.
 * @group Templates
 */
interface InputMaskTemplates {
    /**
     * Custom clear icon template.
     */
    clearicon(): TemplateRef<any>;
}

/**
 *
 * InputMask component is used to enter input in a certain format such as numeric, date, currency, email and phone.
 *
 * [Live Demo](https://www.primeng.org/inputmask/)
 *
 * @module inputmaskstyle
 *
 */
declare enum InputMaskClasses {
    /**
     * Class name of the root element
     */
    root = "p-inputmask",
    /**
     * Class name of the clear icon element
     */
    clearIcon = "p-inputmask-clear-icon"
}
declare class InputMaskStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-variant-filled': boolean;
        })[];
        clearIcon: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<InputMaskStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<InputMaskStyle>;
}
interface InputMaskStyle extends BaseStyle {
}

declare const INPUTMASK_VALUE_ACCESSOR: any;
/**
 * InputMask component is used to enter input in a certain format such as numeric, date, currency, email and phone.
 * @group Components
 */
declare class InputMask extends BaseInput implements OnInit, AfterContentInit {
    /**
     * HTML5 input type.
     * @group Props
     */
    type: string;
    /**
     * Placeholder character in mask, default is underscore.
     * @group Props
     */
    slotChar: string;
    /**
     * Clears the incomplete value on blur.
     * @group Props
     */
    autoClear: boolean;
    /**
     * When enabled, a clear icon is displayed to clear the value.
     * @group Props
     */
    showClear: boolean;
    /**
     * Inline style of the input field.
     * @group Props
     */
    style: {
        [klass: string]: any;
    } | null | undefined;
    /**
     * Identifier of the focus input to match a label defined for the component.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * Style class of the input field.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Advisory information to display on input.
     * @group Props
     */
    placeholder: string | undefined;
    /**
     * Specifies tab order of the element.
     * @group Props
     */
    tabindex: string | undefined;
    /**
     * Title text of the input text.
     * @group Props
     */
    title: string | undefined;
    /**
     * Used to define a string that labels the input element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * Used to indicate that user input is required on an element before a form can be submitted.
     * @group Props
     */
    ariaRequired: boolean | undefined;
    /**
     * When present, it specifies that an input field is read-only.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * Defines if ngModel sets the raw unmasked value to bound value or the formatted mask value.
     * @group Props
     */
    unmask: boolean | undefined;
    /**
     * Regex pattern for alpha characters
     * @group Props
     */
    characterPattern: string;
    /**
     * When present, the input gets a focus automatically on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Used to define a string that autocomplete attribute the current element.
     * @group Props
     */
    autocomplete: string | undefined;
    /**
     * When present, it specifies that whether to clean buffer value from model.
     * @group Props
     */
    keepBuffer: boolean;
    /**
     * Mask pattern.
     * @group Props
     */
    get mask(): string | undefined | null;
    set mask(val: string | undefined | null);
    /**
     * Callback to invoke when the mask is completed.
     * @group Emits
     */
    onComplete: EventEmitter<any>;
    /**
     * Callback to invoke when the component receives focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onFocus: EventEmitter<Event>;
    /**
     * Callback to invoke when the component loses focus.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onBlur: EventEmitter<Event>;
    /**
     * Callback to invoke on input.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onInput: EventEmitter<Event>;
    /**
     * Callback to invoke on input key press.
     * @param {Event} event - Browser event.
     * @group Emits
     */
    onKeydown: EventEmitter<Event>;
    /**
     * Callback to invoke when input field is cleared.
     * @group Emits
     */
    onClear: EventEmitter<any>;
    /**
     * Template of the clear icon.
     * @group Templates
     */
    clearIconTemplate: Nullable<TemplateRef<any>>;
    templates: QueryList<PrimeTemplate>;
    inputViewChild: Nullable<ElementRef>;
    value: Nullable<string>;
    _mask: Nullable<string>;
    input: Nullable<HTMLInputElement>;
    defs: Nullable<{
        [klass: string]: any;
    }>;
    tests: RegExp[] | any;
    partialPosition: Nullable<number>;
    firstNonMaskPos: Nullable<number>;
    lastRequiredNonMaskPos: Nullable<number>;
    len: Nullable<number>;
    oldVal: Nullable<string>;
    buffer: string[] | any;
    defaultBuffer: Nullable<string>;
    focusText: Nullable<string>;
    caretTimeoutId: any;
    androidChrome: boolean;
    focused: Nullable<boolean>;
    _componentStyle: InputMaskStyle;
    ngOnInit(): void;
    _clearIconTemplate: TemplateRef<any> | undefined;
    ngAfterContentInit(): void;
    initMask(): void;
    caret(first?: number, last?: number): Caret | undefined;
    isCompleted(): boolean;
    getPlaceholder(i: number): string;
    seekNext(pos: number): number;
    seekPrev(pos: number): number;
    shiftL(begin: number, end: number): void;
    shiftR(pos: number): void;
    handleAndroidInput(e: Event): void;
    onInputBlur(e: Event): void;
    onInputKeydown(e: KeyboardEvent): void;
    onKeyPress(e: KeyboardEvent): void;
    clearBuffer(start: number, end: number): void;
    writeBuffer(): void;
    checkVal(allow?: boolean): number;
    onInputFocus(event: Event): void;
    onInputChange(event: Event): void;
    handleInputChange(event: Event): void;
    getUnmaskedValue(): string;
    updateModel(e: Event): void;
    focus(): void;
    clear(): void;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<InputMask, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<InputMask, "p-inputmask, p-inputMask, p-input-mask", never, { "type": { "alias": "type"; "required": false; }; "slotChar": { "alias": "slotChar"; "required": false; }; "autoClear": { "alias": "autoClear"; "required": false; }; "showClear": { "alias": "showClear"; "required": false; }; "style": { "alias": "style"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "styleClass": { "alias": "styleClass"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "title": { "alias": "title"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "ariaRequired": { "alias": "ariaRequired"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "unmask": { "alias": "unmask"; "required": false; }; "characterPattern": { "alias": "characterPattern"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; "autocomplete": { "alias": "autocomplete"; "required": false; }; "keepBuffer": { "alias": "keepBuffer"; "required": false; }; "mask": { "alias": "mask"; "required": false; }; }, { "onComplete": "onComplete"; "onFocus": "onFocus"; "onBlur": "onBlur"; "onInput": "onInput"; "onKeydown": "onKeydown"; "onClear": "onClear"; }, ["clearIconTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_autoClear: unknown;
    static ngAcceptInputType_showClear: unknown;
    static ngAcceptInputType_ariaRequired: unknown;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_unmask: unknown;
    static ngAcceptInputType_autofocus: unknown;
    static ngAcceptInputType_keepBuffer: unknown;
}
declare class InputMaskModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<InputMaskModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<InputMaskModule, never, [typeof InputMask, typeof i1.SharedModule], [typeof InputMask, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<InputMaskModule>;
}

export { INPUTMASK_VALUE_ACCESSOR, InputMask, InputMaskClasses, InputMaskModule, InputMaskStyle };
export type { Caret, InputMaskTemplates };
