import * as i0 from '@angular/core';
import { AfterContentInit, EventEmitter, ElementRef, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseEditableHolder } from 'primeng/baseeditableholder';
import { BaseStyle } from 'primeng/base';

/**
 *
 * ToggleSwitch is used to select a boolean value.
 *
 * [Live Demo](https://www.primeng.org/toggleswitch/)
 *
 * @module toggleswitchstyle
 *
 */
declare enum ToggleSwitchClasses {
    /**
     * Class name of the root element
     */
    root = "p-toggleswitch",
    /**
     * Class name of the input element
     */
    input = "p-toggleswitch-input",
    /**
     * Class name of the slider element
     */
    slider = "p-toggleswitch-slider"
}
declare class ToggleSwitchStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-toggleswitch p-component': boolean;
            'p-toggleswitch-checked': any;
            'p-disabled': any;
            'p-invalid': any;
        })[];
        input: string;
        slider: string;
        handle: string;
    };
    inlineStyles: {
        root: {
            position: string;
        };
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<ToggleSwitchStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ToggleSwitchStyle>;
}
interface ToggleSwitchStyle extends BaseStyle {
}

/**
 * Custom change event.
 * @see {@link ToggleSwitch.onChange}
 * @group Events
 */
interface ToggleSwitchChangeEvent {
    /**
     * Browser event.
     */
    originalEvent: Event;
    /**
     * Checked state.
     */
    checked: boolean;
}

/**
 * Context interface for the handle template.
 * @property {boolean} checked - A flag indicating whether the input is checked.
 * @group Interface
 */
interface ToggleSwitchHandleTemplateContext {
    checked: boolean;
}
declare const TOGGLESWITCH_VALUE_ACCESSOR: any;
/**
 * ToggleSwitch is used to select a boolean value.
 * @group Components
 */
declare class ToggleSwitch extends BaseEditableHolder implements AfterContentInit {
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Index of the element in tabbing order.
     * @group Props
     */
    tabindex: number | undefined;
    /**
     * Identifier of the input element.
     * @group Props
     */
    inputId: string | undefined;
    /**
     * When present, it specifies that the component cannot be edited.
     * @group Props
     */
    readonly: boolean | undefined;
    /**
     * Value in checked state.
     * @group Props
     */
    trueValue: any;
    /**
     * Value in unchecked state.
     * @group Props
     */
    falseValue: any;
    /**
     * Used to define a string that autocomplete attribute the current element.
     * @group Props
     */
    ariaLabel: string | undefined;
    /**
     * Specifies the size of the component.
     * @defaultValue undefined
     * @group Props
     */
    size: i0.InputSignal<"small" | "large">;
    /**
     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
     * @group Props
     */
    ariaLabelledBy: string | undefined;
    /**
     * When present, it specifies that the component should automatically get focus on load.
     * @group Props
     */
    autofocus: boolean | undefined;
    /**
     * Callback to invoke when the on value change.
     * @param {ToggleSwitchChangeEvent} event - Custom change event.
     * @group Emits
     */
    onChange: EventEmitter<ToggleSwitchChangeEvent>;
    input: ElementRef;
    /**
     * Callback to invoke when the on value change.
     * @type {TemplateRef<ToggleSwitchHandleTemplateContext>} context - Context of the template
     * @example
     * ```html
     * <ng-template #handle let-checked="checked"> </ng-template>
     * ```
     * @see {@link ToggleSwitchHandleTemplateContext}
     * @group Templates
     */
    handleTemplate: TemplateRef<any> | undefined;
    _handleTemplate: TemplateRef<any> | undefined;
    focused: boolean;
    _componentStyle: ToggleSwitchStyle;
    templates: QueryList<PrimeTemplate>;
    onHostClick(event: MouseEvent): void;
    ngAfterContentInit(): void;
    onClick(event: Event): void;
    onFocus(): void;
    onBlur(): void;
    checked(): boolean;
    /**
     * @override
     *
     * @see {@link BaseEditableHolder.writeControlValue}
     * Writes the value to the control.
     */
    writeControlValue(value: any, setModelValue: (value: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ToggleSwitch, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ToggleSwitch, "p-toggleswitch, p-toggleSwitch, p-toggle-switch", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "tabindex": { "alias": "tabindex"; "required": false; }; "inputId": { "alias": "inputId"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "trueValue": { "alias": "trueValue"; "required": false; }; "falseValue": { "alias": "falseValue"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "size": { "alias": "size"; "required": false; "isSignal": true; }; "ariaLabelledBy": { "alias": "ariaLabelledBy"; "required": false; }; "autofocus": { "alias": "autofocus"; "required": false; }; }, { "onChange": "onChange"; }, ["handleTemplate", "templates"], never, true, never>;
    static ngAcceptInputType_tabindex: unknown;
    static ngAcceptInputType_readonly: unknown;
    static ngAcceptInputType_autofocus: unknown;
}
declare class ToggleSwitchModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<ToggleSwitchModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<ToggleSwitchModule, never, [typeof ToggleSwitch, typeof i1.SharedModule], [typeof ToggleSwitch, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<ToggleSwitchModule>;
}

export { TOGGLESWITCH_VALUE_ACCESSOR, ToggleSwitch, ToggleSwitchClasses, ToggleSwitchModule, ToggleSwitchStyle };
export type { ToggleSwitchChangeEvent, ToggleSwitchHandleTemplateContext };
