import * as i0 from '@angular/core';
import { OnInit, AfterViewInit, OnDestroy, EventEmitter } from '@angular/core';
import { NgControl } from '@angular/forms';
import { BaseModelHolder } from 'primeng/basemodelholder';
import { Fluid } from 'primeng/fluid';
import { Subscription } from 'rxjs';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Textarea is a multi-line text input element.
 *
 * [Live Demo](https://www.primeng.org/textarea/)
 *
 * @module textareastyle
 *
 */
declare enum TextareaClasses {
    /**
     * Class name of the root element
     */
    root = "p-textarea"
}
declare class TextareaStyle extends BaseStyle {
    name: string;
    theme: string;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-filled': any;
            'p-textarea-resizable ': any;
            'p-variant-filled': boolean;
            'p-textarea-fluid': any;
            'p-inputfield-sm p-textarea-sm': boolean;
            'p-textarea-lg p-inputfield-lg': boolean;
            'p-invalid': any;
        })[];
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TextareaStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TextareaStyle>;
}
interface TextareaStyle extends BaseStyle {
}

/**
 * Textarea adds styling and autoResize functionality to standard textarea element.
 * @group Components
 */
declare class Textarea extends BaseModelHolder implements OnInit, AfterViewInit, OnDestroy {
    /**
     * When present, textarea size changes as being typed.
     * @group Props
     */
    autoResize: boolean | undefined;
    /**
     * Defines the size of the component.
     * @group Props
     */
    pSize: 'large' | 'small';
    /**
     * Specifies the input variant of the component.
     * @defaultValue undefined
     * @group Props
     */
    variant: i0.InputSignal<"filled" | "outlined">;
    /**
     * Spans 100% width of the container when enabled.
     * @defaultValue undefined
     * @group Props
     */
    fluid: i0.InputSignalWithTransform<boolean, unknown>;
    /**
     * When present, it specifies that the component should have invalid state style.
     * @defaultValue false
     * @group Props
     */
    invalid: i0.InputSignalWithTransform<boolean, unknown>;
    $variant: i0.Signal<"filled" | "outlined">;
    /**
     * Callback to invoke on textarea resize.
     * @param {(Event | {})} event - Custom resize event.
     * @group Emits
     */
    onResize: EventEmitter<Event | {}>;
    ngModelSubscription: Subscription | undefined;
    ngControlSubscription: Subscription | undefined;
    _componentStyle: TextareaStyle;
    ngControl: NgControl;
    pcFluid: Fluid;
    get hasFluid(): boolean;
    ngOnInit(): void;
    ngAfterViewInit(): void;
    ngAfterViewChecked(): void;
    onInput(e: Event): void;
    resize(event?: Event): void;
    updateState(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Textarea, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<Textarea, "[pTextarea], [pInputTextarea]", never, { "autoResize": { "alias": "autoResize"; "required": false; }; "pSize": { "alias": "pSize"; "required": false; }; "variant": { "alias": "variant"; "required": false; "isSignal": true; }; "fluid": { "alias": "fluid"; "required": false; "isSignal": true; }; "invalid": { "alias": "invalid"; "required": false; "isSignal": true; }; }, { "onResize": "onResize"; }, never, never, true, never>;
    static ngAcceptInputType_autoResize: unknown;
}
declare class TextareaModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TextareaModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TextareaModule, never, [typeof Textarea], [typeof Textarea]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TextareaModule>;
}

export { Textarea, TextareaClasses, TextareaModule, TextareaStyle };
