import * as i0 from '@angular/core';
import { AfterContentInit, TemplateRef, QueryList } from '@angular/core';
import * as i1 from 'primeng/api';
import { PrimeTemplate } from 'primeng/api';
import { BaseComponent } from 'primeng/basecomponent';
import * as _primeuix_styled from '@primeuix/styled';
import { BaseStyle } from 'primeng/base';

/**
 *
 * Tag component is used to categorize content.
 *
 * [Live Demo](https://www.primeng.org/tag)
 *
 * @module tagstyle
 *
 */
declare enum TagClasses {
    /**
     * Class name of the root element
     */
    root = "p-tag",
    /**
     * Class name of the icon element
     */
    icon = "p-tag-icon",
    /**
     * Class name of the label element
     */
    label = "p-tag-label"
}
declare class TagStyle extends BaseStyle {
    name: string;
    theme: _primeuix_styled.StyleType;
    classes: {
        root: ({ instance }: {
            instance: any;
        }) => (string | {
            'p-tag-info': boolean;
            'p-tag-success': boolean;
            'p-tag-warn': boolean;
            'p-tag-danger': boolean;
            'p-tag-secondary': boolean;
            'p-tag-contrast': boolean;
            'p-tag-rounded': any;
        })[];
        icon: string;
        label: string;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TagStyle, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TagStyle>;
}
interface TagStyle extends BaseStyle {
}

/**
 * Tag component is used to categorize content.
 * @group Components
 */
declare class Tag extends BaseComponent implements AfterContentInit {
    /**
     * Style class of the component.
     * @deprecated since v20.0.0, use `class` instead.
     * @group Props
     */
    styleClass: string | undefined;
    /**
     * Severity type of the tag.
     * @group Props
     */
    severity: string | 'success' | 'secondary' | 'info' | 'warn' | 'danger' | 'contrast' | undefined | null;
    /**
     * Value to display inside the tag.
     * @group Props
     */
    value: string | undefined;
    /**
     * Icon of the tag to display next to the value.
     * @group Props
     */
    icon: string | undefined;
    /**
     * Whether the corners of the tag are rounded.
     * @group Props
     */
    rounded: boolean | undefined;
    iconTemplate: TemplateRef<any>;
    templates: QueryList<PrimeTemplate> | undefined;
    _iconTemplate: TemplateRef<any> | undefined;
    _componentStyle: TagStyle;
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<Tag, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<Tag, "p-tag", never, { "styleClass": { "alias": "styleClass"; "required": false; }; "severity": { "alias": "severity"; "required": false; }; "value": { "alias": "value"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "rounded": { "alias": "rounded"; "required": false; }; }, {}, ["iconTemplate", "templates"], ["*"], true, never>;
    static ngAcceptInputType_rounded: unknown;
}
declare class TagModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TagModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TagModule, never, [typeof Tag, typeof i1.SharedModule], [typeof Tag, typeof i1.SharedModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TagModule>;
}

/**
 * Defines valid templates in Tag.
 * @group Templates
 */
interface TagTemplates {
    /**
     * Custom icon template
     */
    icon(): TemplateRef<any>;
}

export { Tag, TagClasses, TagModule, TagStyle };
export type { TagTemplates };
