import { Static } from '../parser/index.mjs';
import * as T from '../type/index.mjs';
import * as S from './mapping.mjs';
export type TGenericReferenceParameterList_0<Input extends string, Context extends T.TProperties, Result extends unknown[] = []> = (TType<Input, Context> extends [infer _0, infer Input extends string] ? (Static.Token.Const<',', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0, infer Input extends string] ? TGenericReferenceParameterList_0<Input, Context, [...Result, _0]> : [Result, Input];
export type TGenericReferenceParameterList<Input extends string, Context extends T.TProperties = {}> = (TGenericReferenceParameterList_0<Input, Context> extends [infer _0, infer Input extends string] ? ((TType<Input, Context> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : [] : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TGenericReferenceParameterListMapping<_0, Context>, Input] : [];
export type TGenericReference<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Ident<Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TGenericReferenceParameterList<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TGenericReferenceMapping<_0, Context>, Input] : [];
export type TGenericArgumentsList_0<Input extends string, Context extends T.TProperties, Result extends unknown[] = []> = (Static.Token.Ident<Input> extends [infer _0, infer Input extends string] ? (Static.Token.Const<',', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0, infer Input extends string] ? TGenericArgumentsList_0<Input, Context, [...Result, _0]> : [Result, Input];
export type TGenericArgumentsList<Input extends string, Context extends T.TProperties = {}> = (TGenericArgumentsList_0<Input, Context> extends [infer _0, infer Input extends string] ? ((Static.Token.Ident<Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : [] : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TGenericArgumentsListMapping<_0, Context>, Input] : [];
export type TGenericArguments<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'<', Input> extends [infer _0, infer Input extends string] ? TGenericArgumentsList<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown], infer Input extends string] ? [S.TGenericArgumentsMapping<_0, Context>, Input] : [];
export type TKeywordString<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'string', Input> extends [infer _0 extends 'string', infer Input extends string] ? [S.TKeywordStringMapping<_0, Context>, Input] : [];
export type TKeywordNumber<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'number', Input> extends [infer _0 extends 'number', infer Input extends string] ? [S.TKeywordNumberMapping<_0, Context>, Input] : [];
export type TKeywordBoolean<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'boolean', Input> extends [infer _0 extends 'boolean', infer Input extends string] ? [S.TKeywordBooleanMapping<_0, Context>, Input] : [];
export type TKeywordUndefined<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'undefined', Input> extends [infer _0 extends 'undefined', infer Input extends string] ? [S.TKeywordUndefinedMapping<_0, Context>, Input] : [];
export type TKeywordNull<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'null', Input> extends [infer _0 extends 'null', infer Input extends string] ? [S.TKeywordNullMapping<_0, Context>, Input] : [];
export type TKeywordInteger<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'integer', Input> extends [infer _0 extends 'integer', infer Input extends string] ? [S.TKeywordIntegerMapping<_0, Context>, Input] : [];
export type TKeywordBigInt<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'bigint', Input> extends [infer _0 extends 'bigint', infer Input extends string] ? [S.TKeywordBigIntMapping<_0, Context>, Input] : [];
export type TKeywordUnknown<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'unknown', Input> extends [infer _0 extends 'unknown', infer Input extends string] ? [S.TKeywordUnknownMapping<_0, Context>, Input] : [];
export type TKeywordAny<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'any', Input> extends [infer _0 extends 'any', infer Input extends string] ? [S.TKeywordAnyMapping<_0, Context>, Input] : [];
export type TKeywordNever<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'never', Input> extends [infer _0 extends 'never', infer Input extends string] ? [S.TKeywordNeverMapping<_0, Context>, Input] : [];
export type TKeywordSymbol<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'symbol', Input> extends [infer _0 extends 'symbol', infer Input extends string] ? [S.TKeywordSymbolMapping<_0, Context>, Input] : [];
export type TKeywordVoid<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'void', Input> extends [infer _0 extends 'void', infer Input extends string] ? [S.TKeywordVoidMapping<_0, Context>, Input] : [];
export type TKeyword<Input extends string, Context extends T.TProperties = {}> = (TKeywordString<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordNumber<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordBoolean<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordUndefined<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordNull<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordInteger<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordBigInt<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordUnknown<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordAny<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordNever<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordSymbol<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TKeywordVoid<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends unknown, infer Input extends string] ? [S.TKeywordMapping<_0, Context>, Input] : [];
export type TLiteralString<Input extends string, Context extends T.TProperties = {}> = Static.Token.String<["'", '"', '`'], Input> extends [infer _0 extends string, infer Input extends string] ? [S.TLiteralStringMapping<_0, Context>, Input] : [];
export type TLiteralNumber<Input extends string, Context extends T.TProperties = {}> = Static.Token.Number<Input> extends [infer _0 extends string, infer Input extends string] ? [S.TLiteralNumberMapping<_0, Context>, Input] : [];
export type TLiteralBoolean<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'true', Input> extends [infer _0, infer Input extends string] ? [_0, Input] : Static.Token.Const<'false', Input> extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends 'true' | 'false', infer Input extends string] ? [S.TLiteralBooleanMapping<_0, Context>, Input] : [];
export type TLiteral<Input extends string, Context extends T.TProperties = {}> = (TLiteralBoolean<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TLiteralNumber<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TLiteralString<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends unknown, infer Input extends string] ? [S.TLiteralMapping<_0, Context>, Input] : [];
export type TKeyOf<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'keyof', Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown] | [], infer Input extends string] ? [S.TKeyOfMapping<_0, Context>, Input] : [];
export type TIndexArray_0<Input extends string, Context extends T.TProperties, Result extends unknown[] = []> = ((Static.Token.Const<'[', Input> extends [infer _0, infer Input extends string] ? TType<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<']', Input> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : (Static.Token.Const<'[', Input> extends [infer _0, infer Input extends string] ? (Static.Token.Const<']', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [
    infer _0,
    infer Input extends string
] ? [_0, Input] : []) extends [infer _0, infer Input extends string] ? TIndexArray_0<Input, Context, [...Result, _0]> : [Result, Input];
export type TIndexArray<Input extends string, Context extends T.TProperties = {}> = TIndexArray_0<Input, Context> extends [infer _0 extends ([unknown, unknown, unknown] | [unknown, unknown])[], infer Input extends string] ? [S.TIndexArrayMapping<_0, Context>, Input] : [];
export type TExtends<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'extends', Input> extends [infer _0, infer Input extends string] ? TType<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<'?', Input> extends [infer _2, infer Input extends string] ? TType<Input, Context> extends [infer _3, infer Input extends string] ? Static.Token.Const<':', Input> extends [infer _4, infer Input extends string] ? TType<Input, Context> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown] | [], infer Input extends string] ? [S.TExtendsMapping<_0, Context>, Input] : [];
export type TBase<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'(', Input> extends [infer _0, infer Input extends string] ? TType<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<')', Input> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : TKeyword<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TObject<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TTuple<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TLiteral<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TConstructor<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TFunction<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TMapped<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TAsyncIterator<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TIterator<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TConstructorParameters<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TFunctionParameters<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TInstanceType<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TReturnType<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TArgument<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TAwaited<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TArray<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TRecord<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TPromise<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TPartial<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TRequired<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TPick<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TOmit<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TExclude<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TExtract<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TUppercase<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TLowercase<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TCapitalize<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TUncapitalize<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TDate<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TUint8Array<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TGenericReference<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : TReference<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown, unknown, unknown] | unknown, infer Input extends string] ? [S.TBaseMapping<_0, Context>, Input] : [];
export type TFactor<Input extends string, Context extends T.TProperties = {}> = (TKeyOf<Input, Context> extends [infer _0, infer Input extends string] ? TBase<Input, Context> extends [infer _1, infer Input extends string] ? TIndexArray<Input, Context> extends [infer _2, infer Input extends string] ? TExtends<Input, Context> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TFactorMapping<_0, Context>, Input] : [];
export type TExprTermTail<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'&', Input> extends [infer _0, infer Input extends string] ? TFactor<Input, Context> extends [infer _1, infer Input extends string] ? TExprTermTail<Input, Context> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown, unknown, unknown] | [], infer Input extends string] ? [S.TExprTermTailMapping<_0, Context>, Input] : [];
export type TExprTerm<Input extends string, Context extends T.TProperties = {}> = (TFactor<Input, Context> extends [infer _0, infer Input extends string] ? (TExprTermTail<Input, Context> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TExprTermMapping<_0, Context>, Input] : [];
export type TExprTail<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'|', Input> extends [infer _0, infer Input extends string] ? TExprTerm<Input, Context> extends [infer _1, infer Input extends string] ? TExprTail<Input, Context> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown, unknown, unknown] | [], infer Input extends string] ? [S.TExprTailMapping<_0, Context>, Input] : [];
export type TExpr<Input extends string, Context extends T.TProperties = {}> = (TExprTerm<Input, Context> extends [infer _0, infer Input extends string] ? (TExprTail<Input, Context> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TExprMapping<_0, Context>, Input] : [];
export type TType<Input extends string, Context extends T.TProperties = {}> = (TGenericArguments<Input, Context> extends [infer _0 extends T.TProperties, infer Input extends string] ? TExpr<Input, _0> : [] extends [infer _0, infer Input extends string] ? [_0, Input] : TExpr<Input, Context> extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends unknown, infer Input extends string] ? [S.TTypeMapping<_0, Context>, Input] : [];
export type TPropertyKey<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Ident<Input> extends [infer _0, infer Input extends string] ? [_0, Input] : Static.Token.String<["'", '"'], Input> extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends string, infer Input extends string] ? [S.TPropertyKeyMapping<_0, Context>, Input] : [];
export type TReadonly<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'readonly', Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown] | [], infer Input extends string] ? [S.TReadonlyMapping<_0, Context>, Input] : [];
export type TOptional<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<'?', Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown] | [], infer Input extends string] ? [S.TOptionalMapping<_0, Context>, Input] : [];
export type TProperty<Input extends string, Context extends T.TProperties = {}> = (TReadonly<Input, Context> extends [infer _0, infer Input extends string] ? TPropertyKey<Input, Context> extends [infer _1, infer Input extends string] ? TOptional<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<':', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? [[_0, _1, _2, _3, _4], Input] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TPropertyMapping<_0, Context>, Input] : [];
export type TPropertyDelimiter<Input extends string, Context extends T.TProperties = {}> = ((Static.Token.Const<',', Input> extends [infer _0, infer Input extends string] ? (Static.Token.Const<'\n', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [
    infer _0,
    infer Input extends string
] ? [_0, Input] : (Static.Token.Const<';', Input> extends [infer _0, infer Input extends string] ? (Static.Token.Const<'\n', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [
    infer _0,
    infer Input extends string
] ? [_0, Input] : (Static.Token.Const<',', Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : (Static.Token.Const<';', Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : (Static.Token.Const<'\n', Input> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _0 extends [unknown, unknown] | [unknown], infer Input extends string] ? [S.TPropertyDelimiterMapping<_0, Context>, Input] : [];
export type TPropertyList_0<Input extends string, Context extends T.TProperties, Result extends unknown[] = []> = (TProperty<Input, Context> extends [infer _0, infer Input extends string] ? (TPropertyDelimiter<Input, Context> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0, infer Input extends string] ? TPropertyList_0<Input, Context, [...Result, _0]> : [Result, Input];
export type TPropertyList<Input extends string, Context extends T.TProperties = {}> = (TPropertyList_0<Input, Context> extends [infer _0, infer Input extends string] ? ((TProperty<Input, Context> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : [] : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TPropertyListMapping<_0, Context>, Input] : [];
export type TObject<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'{', Input> extends [infer _0, infer Input extends string] ? TPropertyList<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<'}', Input> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown], infer Input extends string] ? [S.TObjectMapping<_0, Context>, Input] : [];
export type TElementList_0<Input extends string, Context extends T.TProperties, Result extends unknown[] = []> = (TType<Input, Context> extends [infer _0, infer Input extends string] ? (Static.Token.Const<',', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0, infer Input extends string] ? TElementList_0<Input, Context, [...Result, _0]> : [Result, Input];
export type TElementList<Input extends string, Context extends T.TProperties = {}> = (TElementList_0<Input, Context> extends [infer _0, infer Input extends string] ? ((TType<Input, Context> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : [] : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TElementListMapping<_0, Context>, Input] : [];
export type TTuple<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'[', Input> extends [infer _0, infer Input extends string] ? TElementList<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<']', Input> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown], infer Input extends string] ? [S.TTupleMapping<_0, Context>, Input] : [];
export type TParameter<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Ident<Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<':', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? [[_0, _1, _2], Input] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown], infer Input extends string] ? [S.TParameterMapping<_0, Context>, Input] : [];
export type TParameterList_0<Input extends string, Context extends T.TProperties, Result extends unknown[] = []> = (TParameter<Input, Context> extends [infer _0, infer Input extends string] ? (Static.Token.Const<',', Input> extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : []) : []) extends [infer _0, infer Input extends string] ? TParameterList_0<Input, Context, [...Result, _0]> : [Result, Input];
export type TParameterList<Input extends string, Context extends T.TProperties = {}> = (TParameterList_0<Input, Context> extends [infer _0, infer Input extends string] ? ((TParameter<Input, Context> extends [infer _0, infer Input extends string] ? [[_0], Input] : []) extends [infer _0, infer Input extends string] ? [_0, Input] : [[], Input] extends [infer _0, infer Input extends string] ? [_0, Input] : []) extends [infer _1, infer Input extends string] ? [[_0, _1], Input] : [] : []) extends [infer _0 extends [unknown, unknown], infer Input extends string] ? [S.TParameterListMapping<_0, Context>, Input] : [];
export type TFunction<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'(', Input> extends [infer _0, infer Input extends string] ? TParameterList<Input, Context> extends [infer _1, infer Input extends string] ? Static.Token.Const<')', Input> extends [infer _2, infer Input extends string] ? Static.Token.Const<'=>', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? [[_0, _1, _2, _3, _4], Input] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TFunctionMapping<_0, Context>, Input] : [];
export type TConstructor<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'new', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'(', Input> extends [infer _1, infer Input extends string] ? TParameterList<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<')', Input> extends [infer _3, infer Input extends string] ? Static.Token.Const<'=>', Input> extends [infer _4, infer Input extends string] ? TType<Input, Context> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TConstructorMapping<_0, Context>, Input] : [];
export type TMapped<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'{', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'[', Input> extends [infer _1, infer Input extends string] ? Static.Token.Ident<Input> extends [infer _2, infer Input extends string] ? Static.Token.Const<'in', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? Static.Token.Const<']', Input> extends [infer _5, infer Input extends string] ? Static.Token.Const<':', Input> extends [infer _6, infer Input extends string] ? TType<Input, Context> extends [infer _7, infer Input extends string] ? Static.Token.Const<'}', Input> extends [infer _8, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5, _6, _7, _8], Input] : [] : [] : [] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TMappedMapping<_0, Context>, Input] : [];
export type TAsyncIterator<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'AsyncIterator', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TAsyncIteratorMapping<_0, Context>, Input] : [];
export type TIterator<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Iterator', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TIteratorMapping<_0, Context>, Input] : [];
export type TArgument<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Argument', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TArgumentMapping<_0, Context>, Input] : [];
export type TAwaited<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Awaited', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TAwaitedMapping<_0, Context>, Input] : [];
export type TArray<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Array', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TArrayMapping<_0, Context>, Input] : [];
export type TRecord<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Record', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<',', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TRecordMapping<_0, Context>, Input] : [];
export type TPromise<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Promise', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TPromiseMapping<_0, Context>, Input] : [];
export type TConstructorParameters<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'ConstructorParameters', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TConstructorParametersMapping<_0, Context>, Input] : [];
export type TFunctionParameters<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Parameters', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TFunctionParametersMapping<_0, Context>, Input] : [];
export type TInstanceType<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'InstanceType', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TInstanceTypeMapping<_0, Context>, Input] : [];
export type TReturnType<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'ReturnType', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TReturnTypeMapping<_0, Context>, Input] : [];
export type TPartial<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Partial', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TPartialMapping<_0, Context>, Input] : [];
export type TRequired<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Required', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TRequiredMapping<_0, Context>, Input] : [];
export type TPick<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Pick', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<',', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TPickMapping<_0, Context>, Input] : [];
export type TOmit<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Omit', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<',', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TOmitMapping<_0, Context>, Input] : [];
export type TExclude<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Exclude', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<',', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TExcludeMapping<_0, Context>, Input] : [];
export type TExtract<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Extract', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<',', Input> extends [infer _3, infer Input extends string] ? TType<Input, Context> extends [infer _4, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _5, infer Input extends string] ? [[_0, _1, _2, _3, _4, _5], Input] : [] : [] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TExtractMapping<_0, Context>, Input] : [];
export type TUppercase<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Uppercase', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TUppercaseMapping<_0, Context>, Input] : [];
export type TLowercase<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Lowercase', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TLowercaseMapping<_0, Context>, Input] : [];
export type TCapitalize<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Capitalize', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TCapitalizeMapping<_0, Context>, Input] : [];
export type TUncapitalize<Input extends string, Context extends T.TProperties = {}> = (Static.Token.Const<'Uncapitalize', Input> extends [infer _0, infer Input extends string] ? Static.Token.Const<'<', Input> extends [infer _1, infer Input extends string] ? TType<Input, Context> extends [infer _2, infer Input extends string] ? Static.Token.Const<'>', Input> extends [infer _3, infer Input extends string] ? [[_0, _1, _2, _3], Input] : [] : [] : [] : []) extends [infer _0 extends [unknown, unknown, unknown, unknown], infer Input extends string] ? [S.TUncapitalizeMapping<_0, Context>, Input] : [];
export type TDate<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'Date', Input> extends [infer _0 extends 'Date', infer Input extends string] ? [S.TDateMapping<_0, Context>, Input] : [];
export type TUint8Array<Input extends string, Context extends T.TProperties = {}> = Static.Token.Const<'Uint8Array', Input> extends [infer _0 extends 'Uint8Array', infer Input extends string] ? [S.TUint8ArrayMapping<_0, Context>, Input] : [];
export type TReference<Input extends string, Context extends T.TProperties = {}> = Static.Token.Ident<Input> extends [infer _0 extends string, infer Input extends string] ? [S.TReferenceMapping<_0, Context>, Input] : [];
export declare const GenericReferenceParameterList_0: (input: string, context: T.TProperties, result?: unknown[]) => [unknown[], string];
export declare const GenericReferenceParameterList: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const GenericReference: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const GenericArgumentsList_0: (input: string, context: T.TProperties, result?: unknown[]) => [unknown[], string];
export declare const GenericArgumentsList: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const GenericArguments: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordString: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordNumber: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordBoolean: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordUndefined: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordNull: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordInteger: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordBigInt: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordUnknown: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordAny: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordNever: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordSymbol: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeywordVoid: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Keyword: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const LiteralString: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const LiteralNumber: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const LiteralBoolean: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Literal: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const KeyOf: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const IndexArray_0: (input: string, context: T.TProperties, result?: unknown[]) => [unknown[], string];
export declare const IndexArray: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Extends: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Base: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Factor: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ExprTermTail: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ExprTerm: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ExprTail: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Expr: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Type: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const PropertyKey: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Readonly: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Optional: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Property: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const PropertyDelimiter: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const PropertyList_0: (input: string, context: T.TProperties, result?: unknown[]) => [unknown[], string];
export declare const PropertyList: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const _Object: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ElementList_0: (input: string, context: T.TProperties, result?: unknown[]) => [unknown[], string];
export declare const ElementList: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Tuple: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Parameter: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ParameterList_0: (input: string, context: T.TProperties, result?: unknown[]) => [unknown[], string];
export declare const ParameterList: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Function: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Constructor: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Mapped: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const AsyncIterator: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Iterator: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Argument: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Awaited: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Array: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Record: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Promise: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ConstructorParameters: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const FunctionParameters: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const InstanceType: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const ReturnType: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Partial: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Required: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Pick: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Omit: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Exclude: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Extract: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Uppercase: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Lowercase: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Capitalize: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Uncapitalize: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Date: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Uint8Array: (input: string, context?: T.TProperties) => [unknown, string] | [];
export declare const Reference: (input: string, context?: T.TProperties) => [unknown, string] | [];
